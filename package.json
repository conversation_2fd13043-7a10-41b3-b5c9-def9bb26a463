{"name": "modern-action-2.0", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "repository": {"type": "git", "url": "git+https://github.com/columj9/modern-action-2.0.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/columj9/modern-action-2.0/issues"}, "homepage": "https://github.com/columj9/modern-action-2.0#readme", "devDependencies": {"@playwright/test": "^1.54.1"}, "dependencies": {"axios": "^1.10.0"}}