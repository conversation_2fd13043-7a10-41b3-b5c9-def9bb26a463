name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: modernaction-api
  ECS_SERVICE: modernaction-api-service
  ECS_CLUSTER: modernaction-dev
  ECS_TASK_DEFINITION: task-definition.json

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: modernaction_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 1.6.1
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Cache Poetry dependencies
      uses: actions/cache@v3
      with:
        path: apps/api/.venv
        key: venv-${{ runner.os }}-${{ hashFiles('apps/api/poetry.lock') }}

    - name: Install API dependencies
      working-directory: apps/api
      run: |
        poetry install --no-root

    - name: Install frontend dependencies
      working-directory: apps/web
      run: |
        npm ci

    - name: Install E2E dependencies
      run: |
        npm ci
        npx playwright install --with-deps

    - name: Run API tests
      working-directory: apps/api
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/modernaction_test
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: test
      run: |
        poetry run pytest tests/ -v --cov=app --cov-report=xml

    - name: Run frontend tests
      working-directory: apps/web
      run: |
        npm run test:ci

    - name: Run linting
      run: |
        cd apps/api && poetry run ruff check . --fix
        cd apps/web && npm run lint

    - name: Run type checking
      run: |
        cd apps/api && poetry run mypy app/
        cd apps/web && npm run type-check

    - name: Start API for E2E tests
      working-directory: apps/api
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/modernaction_test
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: test
      run: |
        poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 &
        sleep 10

    - name: Start frontend for E2E tests
      working-directory: apps/web
      env:
        NEXT_PUBLIC_API_URL: http://localhost:8000
      run: |
        npm run build
        npm run start &
        sleep 15

    - name: Run E2E tests
      run: |
        npx playwright test

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: apps/api/coverage.xml
        flags: backend
        name: codecov-umbrella

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          apps/api/coverage.xml
          test-results/
          playwright-report/

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Build Docker image
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f apps/api/Dockerfile apps/api
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Deploy to Amazon ECS
      id: deploy
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ env.ECS_TASK_DEFINITION }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Deploy frontend to S3/CloudFront
      working-directory: apps/web
      env:
        NEXT_PUBLIC_API_URL: ${{ github.ref == 'refs/heads/main' && 'https://api.modernaction.io' || 'https://api-dev.modernaction.io' }}
      run: |
        npm ci
        npm run build
        aws s3 sync out/ s3://${{ github.ref == 'refs/heads/main' && 'modernaction-prod' || 'modernaction-dev' }} --delete
        aws cloudfront create-invalidation --distribution-id ${{ github.ref == 'refs/heads/main' && secrets.CLOUDFRONT_DISTRIBUTION_ID_PROD || secrets.CLOUDFRONT_DISTRIBUTION_ID_DEV }} --paths "/*"

  security-scan:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        publishToken: ${{ secrets.SEMGREP_APP_TOKEN }}
        publishDeployment: true
        generateSarif: "1"

    - name: Upload Semgrep scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: semgrep.sarif
      if: always()