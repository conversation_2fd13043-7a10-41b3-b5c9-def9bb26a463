version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: modernaction
      POSTGRES_USER: modernaction_user
      POSTGRES_PASSWORD: modernaction_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/api/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U modernaction_user -d modernaction"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # FastAPI Backend
  api:
    build:
      context: ./apps/api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: ************************************************************/modernaction
      REDIS_URL: redis://redis:6379/0
      ENVIRONMENT: development
      SECRET_KEY: development-secret-key
      JWT_SECRET: development-jwt-secret
      CORS_ORIGINS: http://localhost:3000,http://localhost:3001
    volumes:
      - ./apps/api:/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
    volumes:
      - ./apps/web:/app
      - /app/node_modules
    depends_on:
      - api

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
      - web

volumes:
  postgres_data:
  redis_data: