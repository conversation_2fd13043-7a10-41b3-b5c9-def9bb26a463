'use strict';

var React20 = require('react');
var components = require('storybook/internal/components');
var icons = require('@storybook/icons');
var theming = require('storybook/theming');
var clientLogger = require('storybook/internal/client-logger');
var csf = require('storybook/internal/csf');
var coreEvents = require('storybook/internal/core-events');
var previewApi = require('storybook/preview-api');
var docsTools = require('storybook/internal/docs-tools');
var tsDedent = require('ts-dedent');
var channels = require('storybook/internal/channels');

function _interopNamespace(e) {
      if (e && e.__esModule) return e;
      var n = Object.create(null);
      if (e) {
            Object.keys(e).forEach(function (k) {
                  if (k !== 'default') {
                        var d = Object.getOwnPropertyDescriptor(e, k);
                        Object.defineProperty(n, k, d.get ? d : {
                              enumerable: true,
                              get: function () { return e[k]; }
                        });
                  }
            });
      }
      n.default = e;
      return Object.freeze(n);
}

var React20__namespace = /*#__PURE__*/_interopNamespace(React20);

var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __require=(x3=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(x3,{get:(a3,b3)=>(typeof require<"u"?require:a3)[b3]}):x3)(function(x3){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+x3+'" is not supported')});var __esm=(fn,res)=>function(){return fn&&(res=(0, fn[__getOwnPropNames(fn)[0]])(fn=0)),res};var __commonJS=(cb,mod)=>function(){return mod||(0, cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0});},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod));function isSymbol(value2){return typeof value2=="symbol"||value2 instanceof Symbol}var init_isSymbol=__esm({"../../node_modules/es-toolkit/dist/compat/predicate/isSymbol.mjs"(){}});function toNumber(value2){return isSymbol(value2)?NaN:Number(value2)}var init_toNumber=__esm({"../../node_modules/es-toolkit/dist/compat/util/toNumber.mjs"(){init_isSymbol();}});function toFinite(value2){return value2?(value2=toNumber(value2),value2===1/0||value2===-1/0?(value2<0?-1:1)*Number.MAX_VALUE:value2===value2?value2:0):value2===0?value2:0}var init_toFinite=__esm({"../../node_modules/es-toolkit/dist/compat/util/toFinite.mjs"(){init_toNumber();}});function toInteger(value2){let finite=toFinite(value2),remainder=finite%1;return remainder?finite-remainder:finite}var init_toInteger=__esm({"../../node_modules/es-toolkit/dist/compat/util/toInteger.mjs"(){init_toFinite();}});function uniq(arr){return Array.from(new Set(arr))}var init_uniq=__esm({"../../node_modules/es-toolkit/dist/array/uniq.mjs"(){}});function isPrimitive(value2){return value2==null||typeof value2!="object"&&typeof value2!="function"}var init_isPrimitive=__esm({"../../node_modules/es-toolkit/dist/predicate/isPrimitive.mjs"(){}});function isTypedArray(x3){return ArrayBuffer.isView(x3)&&!(x3 instanceof DataView)}var init_isTypedArray=__esm({"../../node_modules/es-toolkit/dist/predicate/isTypedArray.mjs"(){}});function getSymbols(object2){return Object.getOwnPropertySymbols(object2).filter(symbol=>Object.prototype.propertyIsEnumerable.call(object2,symbol))}var init_getSymbols=__esm({"../../node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs"(){}});function getTag(value2){return value2==null?value2===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(value2)}var init_getTag=__esm({"../../node_modules/es-toolkit/dist/compat/_internal/getTag.mjs"(){}});var regexpTag,stringTag,numberTag,booleanTag,argumentsTag,symbolTag,dateTag,mapTag,setTag,arrayTag,arrayBufferTag,objectTag,dataViewTag,uint8ArrayTag,uint8ClampedArrayTag,uint16ArrayTag,uint32ArrayTag,int8ArrayTag,int16ArrayTag,int32ArrayTag,float32ArrayTag,float64ArrayTag,init_tags=__esm({"../../node_modules/es-toolkit/dist/compat/_internal/tags.mjs"(){regexpTag="[object RegExp]",stringTag="[object String]",numberTag="[object Number]",booleanTag="[object Boolean]",argumentsTag="[object Arguments]",symbolTag="[object Symbol]",dateTag="[object Date]",mapTag="[object Map]",setTag="[object Set]",arrayTag="[object Array]",arrayBufferTag="[object ArrayBuffer]",objectTag="[object Object]",dataViewTag="[object DataView]",uint8ArrayTag="[object Uint8Array]",uint8ClampedArrayTag="[object Uint8ClampedArray]",uint16ArrayTag="[object Uint16Array]",uint32ArrayTag="[object Uint32Array]",int8ArrayTag="[object Int8Array]",int16ArrayTag="[object Int16Array]",int32ArrayTag="[object Int32Array]",float32ArrayTag="[object Float32Array]",float64ArrayTag="[object Float64Array]";}});function cloneDeepWith(obj,cloneValue){return cloneDeepWithImpl(obj,void 0,obj,new Map,cloneValue)}function cloneDeepWithImpl(valueToClone,keyToClone,objectToClone,stack=new Map,cloneValue=void 0){let cloned=cloneValue?.(valueToClone,keyToClone,objectToClone,stack);if(cloned!=null)return cloned;if(isPrimitive(valueToClone))return valueToClone;if(stack.has(valueToClone))return stack.get(valueToClone);if(Array.isArray(valueToClone)){let result=new Array(valueToClone.length);stack.set(valueToClone,result);for(let i3=0;i3<valueToClone.length;i3++)result[i3]=cloneDeepWithImpl(valueToClone[i3],i3,objectToClone,stack,cloneValue);return Object.hasOwn(valueToClone,"index")&&(result.index=valueToClone.index),Object.hasOwn(valueToClone,"input")&&(result.input=valueToClone.input),result}if(valueToClone instanceof Date)return new Date(valueToClone.getTime());if(valueToClone instanceof RegExp){let result=new RegExp(valueToClone.source,valueToClone.flags);return result.lastIndex=valueToClone.lastIndex,result}if(valueToClone instanceof Map){let result=new Map;stack.set(valueToClone,result);for(let[key,value2]of valueToClone)result.set(key,cloneDeepWithImpl(value2,key,objectToClone,stack,cloneValue));return result}if(valueToClone instanceof Set){let result=new Set;stack.set(valueToClone,result);for(let value2 of valueToClone)result.add(cloneDeepWithImpl(value2,void 0,objectToClone,stack,cloneValue));return result}if(typeof Buffer<"u"&&Buffer.isBuffer(valueToClone))return valueToClone.subarray();if(isTypedArray(valueToClone)){let result=new(Object.getPrototypeOf(valueToClone)).constructor(valueToClone.length);stack.set(valueToClone,result);for(let i3=0;i3<valueToClone.length;i3++)result[i3]=cloneDeepWithImpl(valueToClone[i3],i3,objectToClone,stack,cloneValue);return result}if(valueToClone instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&valueToClone instanceof SharedArrayBuffer)return valueToClone.slice(0);if(valueToClone instanceof DataView){let result=new DataView(valueToClone.buffer.slice(0),valueToClone.byteOffset,valueToClone.byteLength);return stack.set(valueToClone,result),copyProperties(result,valueToClone,objectToClone,stack,cloneValue),result}if(typeof File<"u"&&valueToClone instanceof File){let result=new File([valueToClone],valueToClone.name,{type:valueToClone.type});return stack.set(valueToClone,result),copyProperties(result,valueToClone,objectToClone,stack,cloneValue),result}if(valueToClone instanceof Blob){let result=new Blob([valueToClone],{type:valueToClone.type});return stack.set(valueToClone,result),copyProperties(result,valueToClone,objectToClone,stack,cloneValue),result}if(valueToClone instanceof Error){let result=new valueToClone.constructor;return stack.set(valueToClone,result),result.message=valueToClone.message,result.name=valueToClone.name,result.stack=valueToClone.stack,result.cause=valueToClone.cause,copyProperties(result,valueToClone,objectToClone,stack,cloneValue),result}if(typeof valueToClone=="object"&&isCloneableObject(valueToClone)){let result=Object.create(Object.getPrototypeOf(valueToClone));return stack.set(valueToClone,result),copyProperties(result,valueToClone,objectToClone,stack,cloneValue),result}return valueToClone}function copyProperties(target,source,objectToClone=target,stack,cloneValue){let keys=[...Object.keys(source),...getSymbols(source)];for(let i3=0;i3<keys.length;i3++){let key=keys[i3],descriptor=Object.getOwnPropertyDescriptor(target,key);(descriptor==null||descriptor.writable)&&(target[key]=cloneDeepWithImpl(source[key],key,objectToClone,stack,cloneValue));}}function isCloneableObject(object2){switch(getTag(object2)){case argumentsTag:case arrayTag:case arrayBufferTag:case dataViewTag:case booleanTag:case dateTag:case float32ArrayTag:case float64ArrayTag:case int8ArrayTag:case int16ArrayTag:case int32ArrayTag:case mapTag:case numberTag:case objectTag:case regexpTag:case setTag:case stringTag:case symbolTag:case uint8ArrayTag:case uint8ClampedArrayTag:case uint16ArrayTag:case uint32ArrayTag:return !0;default:return !1}}var init_cloneDeepWith=__esm({"../../node_modules/es-toolkit/dist/object/cloneDeepWith.mjs"(){init_getSymbols();init_getTag();init_tags();init_isPrimitive();init_isTypedArray();}});function isLength(value2){return Number.isSafeInteger(value2)&&value2>=0}var init_isLength=__esm({"../../node_modules/es-toolkit/dist/predicate/isLength.mjs"(){}});function isArrayLike(value2){return value2!=null&&typeof value2!="function"&&isLength(value2.length)}var init_isArrayLike=__esm({"../../node_modules/es-toolkit/dist/compat/predicate/isArrayLike.mjs"(){init_isLength();}});function cloneDeepWith2(obj,cloneValue){return cloneDeepWith(obj,(value2,key,object2,stack)=>{let cloned=cloneValue?.(value2,key,object2,stack);if(cloned!=null)return cloned;if(typeof obj=="object")switch(Object.prototype.toString.call(obj)){case numberTag:case stringTag:case booleanTag:{let result=new obj.constructor(obj?.valueOf());return copyProperties(result,obj),result}case argumentsTag:{let result={};return copyProperties(result,obj),result.length=obj.length,result[Symbol.iterator]=obj[Symbol.iterator],result}default:return}})}var init_cloneDeepWith2=__esm({"../../node_modules/es-toolkit/dist/compat/object/cloneDeepWith.mjs"(){init_cloneDeepWith();init_tags();}});function cloneDeep(obj){return cloneDeepWith2(obj)}var init_cloneDeep=__esm({"../../node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs"(){init_cloneDeepWith2();}});function range(start,end,step=1){if(end==null&&(end=start,start=0),!Number.isInteger(step)||step===0)throw new Error("The step value must be a non-zero integer.");let length=Math.max(Math.ceil((end-start)/step),0),result=new Array(length);for(let i3=0;i3<length;i3++)result[i3]=start+i3*step;return result}var init_range=__esm({"../../node_modules/es-toolkit/dist/math/range.mjs"(){}});function uniq2(arr){return isArrayLike(arr)?uniq(Array.from(arr)):[]}var init_uniq2=__esm({"../../node_modules/es-toolkit/dist/compat/array/uniq.mjs"(){init_uniq();init_isArrayLike();}});function debounce(func,debounceMs,{signal,edges}={}){let pendingThis,pendingArgs=null,leading=edges!=null&&edges.includes("leading"),trailing=edges==null||edges.includes("trailing"),invoke=()=>{pendingArgs!==null&&(func.apply(pendingThis,pendingArgs),pendingThis=void 0,pendingArgs=null);},onTimerEnd=()=>{trailing&&invoke(),cancel();},timeoutId=null,schedule=()=>{timeoutId!=null&&clearTimeout(timeoutId),timeoutId=setTimeout(()=>{timeoutId=null,onTimerEnd();},debounceMs);},cancelTimer=()=>{timeoutId!==null&&(clearTimeout(timeoutId),timeoutId=null);},cancel=()=>{cancelTimer(),pendingThis=void 0,pendingArgs=null;},flush=()=>{cancelTimer(),invoke();},debounced=function(...args){if(signal?.aborted)return;pendingThis=this,pendingArgs=args;let isFirstCall=timeoutId==null;schedule(),leading&&isFirstCall&&invoke();};return debounced.schedule=schedule,debounced.cancel=cancel,debounced.flush=flush,signal?.addEventListener("abort",cancel,{once:!0}),debounced}var init_debounce=__esm({"../../node_modules/es-toolkit/dist/function/debounce.mjs"(){}});function debounce2(func,debounceMs=0,options={}){typeof options!="object"&&(options={});let{signal,leading=!1,trailing=!0,maxWait}=options,edges=Array(2);leading&&(edges[0]="leading"),trailing&&(edges[1]="trailing");let result,pendingAt=null,_debounced=debounce(function(...args){result=func.apply(this,args),pendingAt=null;},debounceMs,{signal,edges}),debounced=function(...args){if(maxWait!=null){if(pendingAt===null)pendingAt=Date.now();else if(Date.now()-pendingAt>=maxWait)return result=func.apply(this,args),pendingAt=Date.now(),_debounced.cancel(),_debounced.schedule(),result}return _debounced.apply(this,args),result},flush=()=>(_debounced.flush(),result);return debounced.cancel=_debounced.cancel,debounced.flush=flush,debounced}var init_debounce2=__esm({"../../node_modules/es-toolkit/dist/compat/function/debounce.mjs"(){init_debounce();}});function isBuffer(x3){return typeof Buffer<"u"&&Buffer.isBuffer(x3)}var init_isBuffer=__esm({"../../node_modules/es-toolkit/dist/predicate/isBuffer.mjs"(){}});function isPrototype(value2){let constructor=value2?.constructor,prototype=typeof constructor=="function"?constructor.prototype:Object.prototype;return value2===prototype}var init_isPrototype=__esm({"../../node_modules/es-toolkit/dist/compat/_internal/isPrototype.mjs"(){}});function isTypedArray2(x3){return isTypedArray(x3)}var init_isTypedArray2=__esm({"../../node_modules/es-toolkit/dist/compat/predicate/isTypedArray.mjs"(){init_isTypedArray();}});function times(n3,getValue2){if(n3=toInteger(n3),n3<1||!Number.isSafeInteger(n3))return [];let result=new Array(n3);for(let i3=0;i3<n3;i3++)result[i3]=typeof getValue2=="function"?getValue2(i3):i3;return result}var init_times=__esm({"../../node_modules/es-toolkit/dist/compat/util/times.mjs"(){init_toInteger();}});function keysIn(object2){if(object2==null)return [];switch(typeof object2){case"object":case"function":return isArrayLike(object2)?arrayLikeKeysIn(object2):isPrototype(object2)?prototypeKeysIn(object2):keysInImpl(object2);default:return keysInImpl(Object(object2))}}function keysInImpl(object2){let result=[];for(let key in object2)result.push(key);return result}function prototypeKeysIn(object2){return keysInImpl(object2).filter(key=>key!=="constructor")}function arrayLikeKeysIn(object2){let indices=times(object2.length,index=>`${index}`),filteredKeys=new Set(indices);return isBuffer(object2)&&(filteredKeys.add("offset"),filteredKeys.add("parent")),isTypedArray2(object2)&&(filteredKeys.add("buffer"),filteredKeys.add("byteLength"),filteredKeys.add("byteOffset")),[...indices,...keysInImpl(object2).filter(key=>!filteredKeys.has(key))]}var init_keysIn=__esm({"../../node_modules/es-toolkit/dist/compat/object/keysIn.mjs"(){init_isBuffer();init_isPrototype();init_isArrayLike();init_isTypedArray2();init_times();}});function getSymbolsIn(object2){let result=[];for(;object2;)result.push(...getSymbols(object2)),object2=Object.getPrototypeOf(object2);return result}var init_getSymbolsIn=__esm({"../../node_modules/es-toolkit/dist/compat/_internal/getSymbolsIn.mjs"(){init_getSymbols();}});function pickBy(obj,shouldPick){if(obj==null)return {};let result={};if(shouldPick==null)return obj;let keys=isArrayLike(obj)?range(0,obj.length):[...keysIn(obj),...getSymbolsIn(obj)];for(let i3=0;i3<keys.length;i3++){let key=isSymbol(keys[i3])?keys[i3]:keys[i3].toString(),value2=obj[key];shouldPick(value2,key,obj)&&(result[key]=value2);}return result}var init_pickBy=__esm({"../../node_modules/es-toolkit/dist/compat/object/pickBy.mjs"(){init_keysIn();init_range();init_getSymbolsIn();init_isArrayLike();init_isSymbol();}});var init_compat=__esm({"../../node_modules/es-toolkit/dist/compat/index.mjs"(){init_uniq2();init_debounce2();init_cloneDeep();init_pickBy();}});var getControlId,getControlSetterButtonId,init_helpers=__esm({"src/blocks/controls/helpers.ts"(){getControlId=value2=>`control-${value2.replace(/\s+/g,"-")}`,getControlSetterButtonId=value2=>`set-${value2.replace(/\s+/g,"-")}`;}});var require_color_name=__commonJS({"../../node_modules/color-name/index.js"(exports,module){module.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};}});var require_conversions=__commonJS({"../../node_modules/color-convert/conversions.js"(exports,module){var cssKeywords=require_color_name(),reverseKeywords={};for(let key of Object.keys(cssKeywords))reverseKeywords[cssKeywords[key]]=key;var convert3={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};module.exports=convert3;for(let model of Object.keys(convert3)){if(!("channels"in convert3[model]))throw new Error("missing channels property: "+model);if(!("labels"in convert3[model]))throw new Error("missing channel labels property: "+model);if(convert3[model].labels.length!==convert3[model].channels)throw new Error("channel and label counts mismatch: "+model);let{channels,labels}=convert3[model];delete convert3[model].channels,delete convert3[model].labels,Object.defineProperty(convert3[model],"channels",{value:channels}),Object.defineProperty(convert3[model],"labels",{value:labels});}convert3.rgb.hsl=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,min=Math.min(r3,g3,b3),max=Math.max(r3,g3,b3),delta=max-min,h3,s3;max===min?h3=0:r3===max?h3=(g3-b3)/delta:g3===max?h3=2+(b3-r3)/delta:b3===max&&(h3=4+(r3-g3)/delta),h3=Math.min(h3*60,360),h3<0&&(h3+=360);let l3=(min+max)/2;return max===min?s3=0:l3<=.5?s3=delta/(max+min):s3=delta/(2-max-min),[h3,s3*100,l3*100]};convert3.rgb.hsv=function(rgb2){let rdif,gdif,bdif,h3,s3,r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,v3=Math.max(r3,g3,b3),diff=v3-Math.min(r3,g3,b3),diffc=function(c3){return (v3-c3)/6/diff+1/2};return diff===0?(h3=0,s3=0):(s3=diff/v3,rdif=diffc(r3),gdif=diffc(g3),bdif=diffc(b3),r3===v3?h3=bdif-gdif:g3===v3?h3=1/3+rdif-bdif:b3===v3&&(h3=2/3+gdif-rdif),h3<0?h3+=1:h3>1&&(h3-=1)),[h3*360,s3*100,v3*100]};convert3.rgb.hwb=function(rgb2){let r3=rgb2[0],g3=rgb2[1],b3=rgb2[2],h3=convert3.rgb.hsl(rgb2)[0],w3=1/255*Math.min(r3,Math.min(g3,b3));return b3=1-1/255*Math.max(r3,Math.max(g3,b3)),[h3,w3*100,b3*100]};convert3.rgb.cmyk=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,k3=Math.min(1-r3,1-g3,1-b3),c3=(1-r3-k3)/(1-k3)||0,m3=(1-g3-k3)/(1-k3)||0,y3=(1-b3-k3)/(1-k3)||0;return [c3*100,m3*100,y3*100,k3*100]};function comparativeDistance(x3,y3){return (x3[0]-y3[0])**2+(x3[1]-y3[1])**2+(x3[2]-y3[2])**2}convert3.rgb.keyword=function(rgb2){let reversed=reverseKeywords[rgb2];if(reversed)return reversed;let currentClosestDistance=1/0,currentClosestKeyword;for(let keyword of Object.keys(cssKeywords)){let value2=cssKeywords[keyword],distance=comparativeDistance(rgb2,value2);distance<currentClosestDistance&&(currentClosestDistance=distance,currentClosestKeyword=keyword);}return currentClosestKeyword};convert3.keyword.rgb=function(keyword){return cssKeywords[keyword]};convert3.rgb.xyz=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255;r3=r3>.04045?((r3+.055)/1.055)**2.4:r3/12.92,g3=g3>.04045?((g3+.055)/1.055)**2.4:g3/12.92,b3=b3>.04045?((b3+.055)/1.055)**2.4:b3/12.92;let x3=r3*.4124+g3*.3576+b3*.1805,y3=r3*.2126+g3*.7152+b3*.0722,z3=r3*.0193+g3*.1192+b3*.9505;return [x3*100,y3*100,z3*100]};convert3.rgb.lab=function(rgb2){let xyz=convert3.rgb.xyz(rgb2),x3=xyz[0],y3=xyz[1],z3=xyz[2];x3/=95.047,y3/=100,z3/=108.883,x3=x3>.008856?x3**(1/3):7.787*x3+16/116,y3=y3>.008856?y3**(1/3):7.787*y3+16/116,z3=z3>.008856?z3**(1/3):7.787*z3+16/116;let l3=116*y3-16,a3=500*(x3-y3),b3=200*(y3-z3);return [l3,a3,b3]};convert3.hsl.rgb=function(hsl2){let h3=hsl2[0]/360,s3=hsl2[1]/100,l3=hsl2[2]/100,t22,t3,val;if(s3===0)return val=l3*255,[val,val,val];l3<.5?t22=l3*(1+s3):t22=l3+s3-l3*s3;let t1=2*l3-t22,rgb2=[0,0,0];for(let i3=0;i3<3;i3++)t3=h3+1/3*-(i3-1),t3<0&&t3++,t3>1&&t3--,6*t3<1?val=t1+(t22-t1)*6*t3:2*t3<1?val=t22:3*t3<2?val=t1+(t22-t1)*(2/3-t3)*6:val=t1,rgb2[i3]=val*255;return rgb2};convert3.hsl.hsv=function(hsl2){let h3=hsl2[0],s3=hsl2[1]/100,l3=hsl2[2]/100,smin=s3,lmin=Math.max(l3,.01);l3*=2,s3*=l3<=1?l3:2-l3,smin*=lmin<=1?lmin:2-lmin;let v3=(l3+s3)/2,sv=l3===0?2*smin/(lmin+smin):2*s3/(l3+s3);return [h3,sv*100,v3*100]};convert3.hsv.rgb=function(hsv){let h3=hsv[0]/60,s3=hsv[1]/100,v3=hsv[2]/100,hi=Math.floor(h3)%6,f3=h3-Math.floor(h3),p3=255*v3*(1-s3),q3=255*v3*(1-s3*f3),t3=255*v3*(1-s3*(1-f3));switch(v3*=255,hi){case 0:return [v3,t3,p3];case 1:return [q3,v3,p3];case 2:return [p3,v3,t3];case 3:return [p3,q3,v3];case 4:return [t3,p3,v3];case 5:return [v3,p3,q3]}};convert3.hsv.hsl=function(hsv){let h3=hsv[0],s3=hsv[1]/100,v3=hsv[2]/100,vmin=Math.max(v3,.01),sl,l3;l3=(2-s3)*v3;let lmin=(2-s3)*vmin;return sl=s3*vmin,sl/=lmin<=1?lmin:2-lmin,sl=sl||0,l3/=2,[h3,sl*100,l3*100]};convert3.hwb.rgb=function(hwb){let h3=hwb[0]/360,wh=hwb[1]/100,bl=hwb[2]/100,ratio=wh+bl,f3;ratio>1&&(wh/=ratio,bl/=ratio);let i3=Math.floor(6*h3),v3=1-bl;f3=6*h3-i3,(i3&1)!==0&&(f3=1-f3);let n3=wh+f3*(v3-wh),r3,g3,b3;switch(i3){default:case 6:case 0:r3=v3,g3=n3,b3=wh;break;case 1:r3=n3,g3=v3,b3=wh;break;case 2:r3=wh,g3=v3,b3=n3;break;case 3:r3=wh,g3=n3,b3=v3;break;case 4:r3=n3,g3=wh,b3=v3;break;case 5:r3=v3,g3=wh,b3=n3;break}return [r3*255,g3*255,b3*255]};convert3.cmyk.rgb=function(cmyk){let c3=cmyk[0]/100,m3=cmyk[1]/100,y3=cmyk[2]/100,k3=cmyk[3]/100,r3=1-Math.min(1,c3*(1-k3)+k3),g3=1-Math.min(1,m3*(1-k3)+k3),b3=1-Math.min(1,y3*(1-k3)+k3);return [r3*255,g3*255,b3*255]};convert3.xyz.rgb=function(xyz){let x3=xyz[0]/100,y3=xyz[1]/100,z3=xyz[2]/100,r3,g3,b3;return r3=x3*3.2406+y3*-1.5372+z3*-.4986,g3=x3*-.9689+y3*1.8758+z3*.0415,b3=x3*.0557+y3*-.204+z3*1.057,r3=r3>.0031308?1.055*r3**(1/2.4)-.055:r3*12.92,g3=g3>.0031308?1.055*g3**(1/2.4)-.055:g3*12.92,b3=b3>.0031308?1.055*b3**(1/2.4)-.055:b3*12.92,r3=Math.min(Math.max(0,r3),1),g3=Math.min(Math.max(0,g3),1),b3=Math.min(Math.max(0,b3),1),[r3*255,g3*255,b3*255]};convert3.xyz.lab=function(xyz){let x3=xyz[0],y3=xyz[1],z3=xyz[2];x3/=95.047,y3/=100,z3/=108.883,x3=x3>.008856?x3**(1/3):7.787*x3+16/116,y3=y3>.008856?y3**(1/3):7.787*y3+16/116,z3=z3>.008856?z3**(1/3):7.787*z3+16/116;let l3=116*y3-16,a3=500*(x3-y3),b3=200*(y3-z3);return [l3,a3,b3]};convert3.lab.xyz=function(lab){let l3=lab[0],a3=lab[1],b3=lab[2],x3,y3,z3;y3=(l3+16)/116,x3=a3/500+y3,z3=y3-b3/200;let y22=y3**3,x22=x3**3,z22=z3**3;return y3=y22>.008856?y22:(y3-16/116)/7.787,x3=x22>.008856?x22:(x3-16/116)/7.787,z3=z22>.008856?z22:(z3-16/116)/7.787,x3*=95.047,y3*=100,z3*=108.883,[x3,y3,z3]};convert3.lab.lch=function(lab){let l3=lab[0],a3=lab[1],b3=lab[2],h3;h3=Math.atan2(b3,a3)*360/2/Math.PI,h3<0&&(h3+=360);let c3=Math.sqrt(a3*a3+b3*b3);return [l3,c3,h3]};convert3.lch.lab=function(lch){let l3=lch[0],c3=lch[1],hr=lch[2]/360*2*Math.PI,a3=c3*Math.cos(hr),b3=c3*Math.sin(hr);return [l3,a3,b3]};convert3.rgb.ansi16=function(args,saturation=null){let[r3,g3,b3]=args,value2=saturation===null?convert3.rgb.hsv(args)[2]:saturation;if(value2=Math.round(value2/50),value2===0)return 30;let ansi=30+(Math.round(b3/255)<<2|Math.round(g3/255)<<1|Math.round(r3/255));return value2===2&&(ansi+=60),ansi};convert3.hsv.ansi16=function(args){return convert3.rgb.ansi16(convert3.hsv.rgb(args),args[2])};convert3.rgb.ansi256=function(args){let r3=args[0],g3=args[1],b3=args[2];return r3===g3&&g3===b3?r3<8?16:r3>248?231:Math.round((r3-8)/247*24)+232:16+36*Math.round(r3/255*5)+6*Math.round(g3/255*5)+Math.round(b3/255*5)};convert3.ansi16.rgb=function(args){let color=args%10;if(color===0||color===7)return args>50&&(color+=3.5),color=color/10.5*255,[color,color,color];let mult=(~~(args>50)+1)*.5,r3=(color&1)*mult*255,g3=(color>>1&1)*mult*255,b3=(color>>2&1)*mult*255;return [r3,g3,b3]};convert3.ansi256.rgb=function(args){if(args>=232){let c3=(args-232)*10+8;return [c3,c3,c3]}args-=16;let rem2,r3=Math.floor(args/36)/5*255,g3=Math.floor((rem2=args%36)/6)/5*255,b3=rem2%6/5*255;return [r3,g3,b3]};convert3.rgb.hex=function(args){let string=(((Math.round(args[0])&255)<<16)+((Math.round(args[1])&255)<<8)+(Math.round(args[2])&255)).toString(16).toUpperCase();return "000000".substring(string.length)+string};convert3.hex.rgb=function(args){let match=args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!match)return [0,0,0];let colorString=match[0];match[0].length===3&&(colorString=colorString.split("").map(char=>char+char).join(""));let integer=parseInt(colorString,16),r3=integer>>16&255,g3=integer>>8&255,b3=integer&255;return [r3,g3,b3]};convert3.rgb.hcg=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,max=Math.max(Math.max(r3,g3),b3),min=Math.min(Math.min(r3,g3),b3),chroma=max-min,grayscale,hue;return chroma<1?grayscale=min/(1-chroma):grayscale=0,chroma<=0?hue=0:max===r3?hue=(g3-b3)/chroma%6:max===g3?hue=2+(b3-r3)/chroma:hue=4+(r3-g3)/chroma,hue/=6,hue%=1,[hue*360,chroma*100,grayscale*100]};convert3.hsl.hcg=function(hsl2){let s3=hsl2[1]/100,l3=hsl2[2]/100,c3=l3<.5?2*s3*l3:2*s3*(1-l3),f3=0;return c3<1&&(f3=(l3-.5*c3)/(1-c3)),[hsl2[0],c3*100,f3*100]};convert3.hsv.hcg=function(hsv){let s3=hsv[1]/100,v3=hsv[2]/100,c3=s3*v3,f3=0;return c3<1&&(f3=(v3-c3)/(1-c3)),[hsv[0],c3*100,f3*100]};convert3.hcg.rgb=function(hcg){let h3=hcg[0]/360,c3=hcg[1]/100,g3=hcg[2]/100;if(c3===0)return [g3*255,g3*255,g3*255];let pure=[0,0,0],hi=h3%1*6,v3=hi%1,w3=1-v3,mg=0;switch(Math.floor(hi)){case 0:pure[0]=1,pure[1]=v3,pure[2]=0;break;case 1:pure[0]=w3,pure[1]=1,pure[2]=0;break;case 2:pure[0]=0,pure[1]=1,pure[2]=v3;break;case 3:pure[0]=0,pure[1]=w3,pure[2]=1;break;case 4:pure[0]=v3,pure[1]=0,pure[2]=1;break;default:pure[0]=1,pure[1]=0,pure[2]=w3;}return mg=(1-c3)*g3,[(c3*pure[0]+mg)*255,(c3*pure[1]+mg)*255,(c3*pure[2]+mg)*255]};convert3.hcg.hsv=function(hcg){let c3=hcg[1]/100,g3=hcg[2]/100,v3=c3+g3*(1-c3),f3=0;return v3>0&&(f3=c3/v3),[hcg[0],f3*100,v3*100]};convert3.hcg.hsl=function(hcg){let c3=hcg[1]/100,l3=hcg[2]/100*(1-c3)+.5*c3,s3=0;return l3>0&&l3<.5?s3=c3/(2*l3):l3>=.5&&l3<1&&(s3=c3/(2*(1-l3))),[hcg[0],s3*100,l3*100]};convert3.hcg.hwb=function(hcg){let c3=hcg[1]/100,g3=hcg[2]/100,v3=c3+g3*(1-c3);return [hcg[0],(v3-c3)*100,(1-v3)*100]};convert3.hwb.hcg=function(hwb){let w3=hwb[1]/100,v3=1-hwb[2]/100,c3=v3-w3,g3=0;return c3<1&&(g3=(v3-c3)/(1-c3)),[hwb[0],c3*100,g3*100]};convert3.apple.rgb=function(apple){return [apple[0]/65535*255,apple[1]/65535*255,apple[2]/65535*255]};convert3.rgb.apple=function(rgb2){return [rgb2[0]/255*65535,rgb2[1]/255*65535,rgb2[2]/255*65535]};convert3.gray.rgb=function(args){return [args[0]/100*255,args[0]/100*255,args[0]/100*255]};convert3.gray.hsl=function(args){return [0,0,args[0]]};convert3.gray.hsv=convert3.gray.hsl;convert3.gray.hwb=function(gray){return [0,100,gray[0]]};convert3.gray.cmyk=function(gray){return [0,0,0,gray[0]]};convert3.gray.lab=function(gray){return [gray[0],0,0]};convert3.gray.hex=function(gray){let val=Math.round(gray[0]/100*255)&255,string=((val<<16)+(val<<8)+val).toString(16).toUpperCase();return "000000".substring(string.length)+string};convert3.rgb.gray=function(rgb2){return [(rgb2[0]+rgb2[1]+rgb2[2])/3/255*100]};}});var require_route=__commonJS({"../../node_modules/color-convert/route.js"(exports,module){var conversions=require_conversions();function buildGraph(){let graph={},models=Object.keys(conversions);for(let len=models.length,i3=0;i3<len;i3++)graph[models[i3]]={distance:-1,parent:null};return graph}function deriveBFS(fromModel){let graph=buildGraph(),queue=[fromModel];for(graph[fromModel].distance=0;queue.length;){let current=queue.pop(),adjacents=Object.keys(conversions[current]);for(let len=adjacents.length,i3=0;i3<len;i3++){let adjacent=adjacents[i3],node=graph[adjacent];node.distance===-1&&(node.distance=graph[current].distance+1,node.parent=current,queue.unshift(adjacent));}}return graph}function link(from,to){return function(args){return to(from(args))}}function wrapConversion(toModel,graph){let path=[graph[toModel].parent,toModel],fn=conversions[graph[toModel].parent][toModel],cur=graph[toModel].parent;for(;graph[cur].parent;)path.unshift(graph[cur].parent),fn=link(conversions[graph[cur].parent][cur],fn),cur=graph[cur].parent;return fn.conversion=path,fn}module.exports=function(fromModel){let graph=deriveBFS(fromModel),conversion={},models=Object.keys(graph);for(let len=models.length,i3=0;i3<len;i3++){let toModel=models[i3];graph[toModel].parent!==null&&(conversion[toModel]=wrapConversion(toModel,graph));}return conversion};}});var require_color_convert=__commonJS({"../../node_modules/color-convert/index.js"(exports,module){var conversions=require_conversions(),route=require_route(),convert3={},models=Object.keys(conversions);function wrapRaw(fn){let wrappedFn=function(...args){let arg0=args[0];return arg0==null?arg0:(arg0.length>1&&(args=arg0),fn(args))};return "conversion"in fn&&(wrappedFn.conversion=fn.conversion),wrappedFn}function wrapRounded(fn){let wrappedFn=function(...args){let arg0=args[0];if(arg0==null)return arg0;arg0.length>1&&(args=arg0);let result=fn(args);if(typeof result=="object")for(let len=result.length,i3=0;i3<len;i3++)result[i3]=Math.round(result[i3]);return result};return "conversion"in fn&&(wrappedFn.conversion=fn.conversion),wrappedFn}models.forEach(fromModel=>{convert3[fromModel]={},Object.defineProperty(convert3[fromModel],"channels",{value:conversions[fromModel].channels}),Object.defineProperty(convert3[fromModel],"labels",{value:conversions[fromModel].labels});let routes=route(fromModel);Object.keys(routes).forEach(toModel=>{let fn=routes[toModel];convert3[fromModel][toModel]=wrapRounded(fn),convert3[fromModel][toModel].raw=wrapRaw(fn);});});module.exports=convert3;}});function u2(){return (u2=Object.assign||function(e3){for(var r3=1;r3<arguments.length;r3++){var t3=arguments[r3];for(var n3 in t3)Object.prototype.hasOwnProperty.call(t3,n3)&&(e3[n3]=t3[n3]);}return e3}).apply(this,arguments)}function c2(e3,r3){if(e3==null)return {};var t3,n3,o3={},a3=Object.keys(e3);for(n3=0;n3<a3.length;n3++)r3.indexOf(t3=a3[n3])>=0||(o3[t3]=e3[t3]);return o3}function i2(e3){var t3=React20.useRef(e3),n3=React20.useRef(function(e4){t3.current&&t3.current(e4);});return t3.current=e3,n3.current}function Y2(e3,t3,l3){var u3=i2(l3),c3=React20.useState(function(){return e3.toHsva(t3)}),s3=c3[0],f3=c3[1],v3=React20.useRef({color:t3,hsva:s3});React20.useEffect(function(){if(!e3.equal(t3,v3.current.color)){var r3=e3.toHsva(t3);v3.current={hsva:r3,color:t3},f3(r3);}},[t3,e3]),React20.useEffect(function(){var r3;F2(s3,v3.current.hsva)||e3.equal(r3=e3.fromHsva(s3),v3.current.color)||(v3.current={hsva:s3,color:r3},u3(r3));},[s3,e3,u3]);var d3=React20.useCallback(function(e4){f3(function(r3){return Object.assign({},r3,e4)});},[]);return [s3,d3]}var s2,f2,v2,d2,h2,m2,g2,p2,b2,_2,x2,C2,E2,H2,N2,w2,y2,q2,k2,I2,z2,D2,K2,L2,S2,T2,F2,P2,X2,V2,$2,J2,Q2,U2,W2,Z2,ee2,re2,le2,ue2,Ee2,He2,init_dist=__esm({"../../node_modules/react-colorful/dist/index.mjs"(){s2=function(e3,r3,t3){return r3===void 0&&(r3=0),t3===void 0&&(t3=1),e3>t3?t3:e3<r3?r3:e3},f2=function(e3){return "touches"in e3},v2=function(e3){return e3&&e3.ownerDocument.defaultView||self},d2=function(e3,r3,t3){var n3=e3.getBoundingClientRect(),o3=f2(r3)?function(e4,r4){for(var t4=0;t4<e4.length;t4++)if(e4[t4].identifier===r4)return e4[t4];return e4[0]}(r3.touches,t3):r3;return {left:s2((o3.pageX-(n3.left+v2(e3).pageXOffset))/n3.width),top:s2((o3.pageY-(n3.top+v2(e3).pageYOffset))/n3.height)}},h2=function(e3){!f2(e3)&&e3.preventDefault();},m2=React20__namespace.default.memo(function(o3){var a3=o3.onMove,l3=o3.onKey,s3=c2(o3,["onMove","onKey"]),m3=React20.useRef(null),g3=i2(a3),p3=i2(l3),b3=React20.useRef(null),_3=React20.useRef(!1),x3=React20.useMemo(function(){var e3=function(e4){h2(e4),(f2(e4)?e4.touches.length>0:e4.buttons>0)&&m3.current?g3(d2(m3.current,e4,b3.current)):t3(!1);},r3=function(){return t3(!1)};function t3(t4){var n3=_3.current,o4=v2(m3.current),a4=t4?o4.addEventListener:o4.removeEventListener;a4(n3?"touchmove":"mousemove",e3),a4(n3?"touchend":"mouseup",r3);}return [function(e4){var r4=e4.nativeEvent,n3=m3.current;if(n3&&(h2(r4),!function(e5,r5){return r5&&!f2(e5)}(r4,_3.current)&&n3)){if(f2(r4)){_3.current=!0;var o4=r4.changedTouches||[];o4.length&&(b3.current=o4[0].identifier);}n3.focus(),g3(d2(n3,r4,b3.current)),t3(!0);}},function(e4){var r4=e4.which||e4.keyCode;r4<37||r4>40||(e4.preventDefault(),p3({left:r4===39?.05:r4===37?-.05:0,top:r4===40?.05:r4===38?-.05:0}));},t3]},[p3,g3]),C3=x3[0],E3=x3[1],H4=x3[2];return React20.useEffect(function(){return H4},[H4]),React20__namespace.default.createElement("div",u2({},s3,{onTouchStart:C3,onMouseDown:C3,className:"react-colorful__interactive",ref:m3,onKeyDown:E3,tabIndex:0,role:"slider"}))}),g2=function(e3){return e3.filter(Boolean).join(" ")},p2=function(r3){var t3=r3.color,n3=r3.left,o3=r3.top,a3=o3===void 0?.5:o3,l3=g2(["react-colorful__pointer",r3.className]);return React20__namespace.default.createElement("div",{className:l3,style:{top:100*a3+"%",left:100*n3+"%"}},React20__namespace.default.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t3}}))},b2=function(e3,r3,t3){return r3===void 0&&(r3=0),t3===void 0&&(t3=Math.pow(10,r3)),Math.round(t3*e3)/t3},_2={grad:.9,turn:360,rad:360/(2*Math.PI)},x2=function(e3){return L2(C2(e3))},C2=function(e3){return e3[0]==="#"&&(e3=e3.substring(1)),e3.length<6?{r:parseInt(e3[0]+e3[0],16),g:parseInt(e3[1]+e3[1],16),b:parseInt(e3[2]+e3[2],16),a:e3.length===4?b2(parseInt(e3[3]+e3[3],16)/255,2):1}:{r:parseInt(e3.substring(0,2),16),g:parseInt(e3.substring(2,4),16),b:parseInt(e3.substring(4,6),16),a:e3.length===8?b2(parseInt(e3.substring(6,8),16)/255,2):1}},E2=function(e3,r3){return r3===void 0&&(r3="deg"),Number(e3)*(_2[r3]||1)},H2=function(e3){var r3=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e3);return r3?N2({h:E2(r3[1],r3[2]),s:Number(r3[3]),l:Number(r3[4]),a:r3[5]===void 0?1:Number(r3[5])/(r3[6]?100:1)}):{h:0,s:0,v:0,a:1}},N2=function(e3){var r3=e3.s,t3=e3.l;return {h:e3.h,s:(r3*=(t3<50?t3:100-t3)/100)>0?2*r3/(t3+r3)*100:0,v:t3+r3,a:e3.a}},w2=function(e3){return K2(I2(e3))},y2=function(e3){var r3=e3.s,t3=e3.v,n3=e3.a,o3=(200-r3)*t3/100;return {h:b2(e3.h),s:b2(o3>0&&o3<200?r3*t3/100/(o3<=100?o3:200-o3)*100:0),l:b2(o3/2),a:b2(n3,2)}},q2=function(e3){var r3=y2(e3);return "hsl("+r3.h+", "+r3.s+"%, "+r3.l+"%)"},k2=function(e3){var r3=y2(e3);return "hsla("+r3.h+", "+r3.s+"%, "+r3.l+"%, "+r3.a+")"},I2=function(e3){var r3=e3.h,t3=e3.s,n3=e3.v,o3=e3.a;r3=r3/360*6,t3/=100,n3/=100;var a3=Math.floor(r3),l3=n3*(1-t3),u3=n3*(1-(r3-a3)*t3),c3=n3*(1-(1-r3+a3)*t3),i3=a3%6;return {r:b2(255*[n3,u3,l3,l3,c3,n3][i3]),g:b2(255*[c3,n3,n3,u3,l3,l3][i3]),b:b2(255*[l3,l3,c3,n3,n3,u3][i3]),a:b2(o3,2)}},z2=function(e3){var r3=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e3);return r3?L2({r:Number(r3[1])/(r3[2]?100/255:1),g:Number(r3[3])/(r3[4]?100/255:1),b:Number(r3[5])/(r3[6]?100/255:1),a:r3[7]===void 0?1:Number(r3[7])/(r3[8]?100:1)}):{h:0,s:0,v:0,a:1}},D2=function(e3){var r3=e3.toString(16);return r3.length<2?"0"+r3:r3},K2=function(e3){var r3=e3.r,t3=e3.g,n3=e3.b,o3=e3.a,a3=o3<1?D2(b2(255*o3)):"";return "#"+D2(r3)+D2(t3)+D2(n3)+a3},L2=function(e3){var r3=e3.r,t3=e3.g,n3=e3.b,o3=e3.a,a3=Math.max(r3,t3,n3),l3=a3-Math.min(r3,t3,n3),u3=l3?a3===r3?(t3-n3)/l3:a3===t3?2+(n3-r3)/l3:4+(r3-t3)/l3:0;return {h:b2(60*(u3<0?u3+6:u3)),s:b2(a3?l3/a3*100:0),v:b2(a3/255*100),a:o3}},S2=React20__namespace.default.memo(function(r3){var t3=r3.hue,n3=r3.onChange,o3=g2(["react-colorful__hue",r3.className]);return React20__namespace.default.createElement("div",{className:o3},React20__namespace.default.createElement(m2,{onMove:function(e3){n3({h:360*e3.left});},onKey:function(e3){n3({h:s2(t3+360*e3.left,0,360)});},"aria-label":"Hue","aria-valuenow":b2(t3),"aria-valuemax":"360","aria-valuemin":"0"},React20__namespace.default.createElement(p2,{className:"react-colorful__hue-pointer",left:t3/360,color:q2({h:t3,s:100,v:100,a:1})})))}),T2=React20__namespace.default.memo(function(r3){var t3=r3.hsva,n3=r3.onChange,o3={backgroundColor:q2({h:t3.h,s:100,v:100,a:1})};return React20__namespace.default.createElement("div",{className:"react-colorful__saturation",style:o3},React20__namespace.default.createElement(m2,{onMove:function(e3){n3({s:100*e3.left,v:100-100*e3.top});},onKey:function(e3){n3({s:s2(t3.s+100*e3.left,0,100),v:s2(t3.v-100*e3.top,0,100)});},"aria-label":"Color","aria-valuetext":"Saturation "+b2(t3.s)+"%, Brightness "+b2(t3.v)+"%"},React20__namespace.default.createElement(p2,{className:"react-colorful__saturation-pointer",top:1-t3.v/100,left:t3.s/100,color:q2(t3)})))}),F2=function(e3,r3){if(e3===r3)return !0;for(var t3 in e3)if(e3[t3]!==r3[t3])return !1;return !0},P2=function(e3,r3){return e3.replace(/\s/g,"")===r3.replace(/\s/g,"")},X2=function(e3,r3){return e3.toLowerCase()===r3.toLowerCase()||F2(C2(e3),C2(r3))};V2=typeof window<"u"?React20.useLayoutEffect:React20.useEffect,$2=function(){return (typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0)},J2=new Map,Q2=function(e3){V2(function(){var r3=e3.current?e3.current.ownerDocument:document;if(r3!==void 0&&!J2.has(r3)){var t3=r3.createElement("style");t3.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,J2.set(r3,t3);var n3=$2();n3&&t3.setAttribute("nonce",n3),r3.head.appendChild(t3);}},[]);},U2=function(t3){var n3=t3.className,o3=t3.colorModel,a3=t3.color,l3=a3===void 0?o3.defaultColor:a3,i3=t3.onChange,s3=c2(t3,["className","colorModel","color","onChange"]),f3=React20.useRef(null);Q2(f3);var v3=Y2(o3,l3,i3),d3=v3[0],h3=v3[1],m3=g2(["react-colorful",n3]);return React20__namespace.default.createElement("div",u2({},s3,{ref:f3,className:m3}),React20__namespace.default.createElement(T2,{hsva:d3,onChange:h3}),React20__namespace.default.createElement(S2,{hue:d3.h,onChange:h3,className:"react-colorful__last-control"}))},W2={defaultColor:"000",toHsva:x2,fromHsva:function(e3){return w2({h:e3.h,s:e3.s,v:e3.v,a:1})},equal:X2},Z2=function(r3){return React20__namespace.default.createElement(U2,u2({},r3,{colorModel:W2}))},ee2=function(r3){var t3=r3.className,n3=r3.hsva,o3=r3.onChange,a3={backgroundImage:"linear-gradient(90deg, "+k2(Object.assign({},n3,{a:0}))+", "+k2(Object.assign({},n3,{a:1}))+")"},l3=g2(["react-colorful__alpha",t3]),u3=b2(100*n3.a);return React20__namespace.default.createElement("div",{className:l3},React20__namespace.default.createElement("div",{className:"react-colorful__alpha-gradient",style:a3}),React20__namespace.default.createElement(m2,{onMove:function(e3){o3({a:e3.left});},onKey:function(e3){o3({a:s2(n3.a+e3.left)});},"aria-label":"Alpha","aria-valuetext":u3+"%","aria-valuenow":u3,"aria-valuemin":"0","aria-valuemax":"100"},React20__namespace.default.createElement(p2,{className:"react-colorful__alpha-pointer",left:n3.a,color:k2(n3)})))},re2=function(t3){var n3=t3.className,o3=t3.colorModel,a3=t3.color,l3=a3===void 0?o3.defaultColor:a3,i3=t3.onChange,s3=c2(t3,["className","colorModel","color","onChange"]),f3=React20.useRef(null);Q2(f3);var v3=Y2(o3,l3,i3),d3=v3[0],h3=v3[1],m3=g2(["react-colorful",n3]);return React20__namespace.default.createElement("div",u2({},s3,{ref:f3,className:m3}),React20__namespace.default.createElement(T2,{hsva:d3,onChange:h3}),React20__namespace.default.createElement(S2,{hue:d3.h,onChange:h3}),React20__namespace.default.createElement(ee2,{hsva:d3,onChange:h3,className:"react-colorful__last-control"}))},le2={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:H2,fromHsva:k2,equal:P2},ue2=function(r3){return React20__namespace.default.createElement(re2,u2({},r3,{colorModel:le2}))},Ee2={defaultColor:"rgba(0, 0, 0, 1)",toHsva:z2,fromHsva:function(e3){var r3=I2(e3);return "rgba("+r3.r+", "+r3.g+", "+r3.b+", "+r3.a+")"},equal:P2},He2=function(r3){return React20__namespace.default.createElement(re2,u2({},r3,{colorModel:Ee2}))};}});var Color_exports={};__export(Color_exports,{ColorControl:()=>ColorControl,default:()=>Color_default});var import_color_convert,Wrapper9,PickerTooltip,TooltipContent,Note,Swatches2,SwatchColor,swatchBackground,Swatch2,Input2,ToggleIcon,ColorSpace,COLOR_SPACES,COLOR_REGEXP,RGB_REGEXP,HSL_REGEXP,HEX_REGEXP,SHORTHEX_REGEXP,ColorPicker,fallbackColor,stringToArgs,parseRgb,parseHsl,parseHexOrKeyword,parseValue,getRealValue,useColorInput,id,usePresets,ColorControl,Color_default,init_Color=__esm({"src/blocks/controls/Color.tsx"(){import_color_convert=__toESM(require_color_convert());init_compat();init_dist();init_helpers();Wrapper9=theming.styled.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),PickerTooltip=theming.styled(components.WithTooltip)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),TooltipContent=theming.styled.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),Note=theming.styled(components.TooltipNote)(({theme})=>({fontFamily:theme.typography.fonts.base})),Swatches2=theming.styled.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),SwatchColor=theming.styled.div(({theme,active})=>({width:16,height:16,boxShadow:active?`${theme.appBorderColor} 0 0 0 1px inset, ${theme.textMutedColor}50 0 0 0 4px`:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:theme.appBorderRadius})),swatchBackground=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,Swatch2=({value:value2,style,...props})=>{let backgroundImage=`linear-gradient(${value2}, ${value2}), ${swatchBackground}, linear-gradient(#fff, #fff)`;return React20__namespace.default.createElement(SwatchColor,{...props,style:{...style,backgroundImage}})},Input2=theming.styled(components.Form.Input)(({theme,readOnly})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:theme.typography.fonts.base})),ToggleIcon=theming.styled(icons.MarkupIcon)(({theme})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:theme.input.color})),ColorSpace=(ColorSpace2=>(ColorSpace2.RGB="rgb",ColorSpace2.HSL="hsl",ColorSpace2.HEX="hex",ColorSpace2))(ColorSpace||{}),COLOR_SPACES=Object.values(ColorSpace),COLOR_REGEXP=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,RGB_REGEXP=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,HSL_REGEXP=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,HEX_REGEXP=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,SHORTHEX_REGEXP=/^\s*#?([0-9a-f]{3})\s*$/i,ColorPicker={hex:Z2,rgb:He2,hsl:ue2},fallbackColor={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},stringToArgs=value2=>{let match=value2?.match(COLOR_REGEXP);if(!match)return [0,0,0,1];let[,x3,y3,z3,a3=1]=match;return [x3,y3,z3,a3].map(Number)},parseRgb=value2=>{let[r3,g3,b3,a3]=stringToArgs(value2),[h3,s3,l3]=import_color_convert.default.rgb.hsl([r3,g3,b3])||[0,0,0];return {valid:!0,value:value2,keyword:import_color_convert.default.rgb.keyword([r3,g3,b3]),colorSpace:"rgb",rgb:value2,hsl:`hsla(${h3}, ${s3}%, ${l3}%, ${a3})`,hex:`#${import_color_convert.default.rgb.hex([r3,g3,b3]).toLowerCase()}`}},parseHsl=value2=>{let[h3,s3,l3,a3]=stringToArgs(value2),[r3,g3,b3]=import_color_convert.default.hsl.rgb([h3,s3,l3])||[0,0,0];return {valid:!0,value:value2,keyword:import_color_convert.default.hsl.keyword([h3,s3,l3]),colorSpace:"hsl",rgb:`rgba(${r3}, ${g3}, ${b3}, ${a3})`,hsl:value2,hex:`#${import_color_convert.default.hsl.hex([h3,s3,l3]).toLowerCase()}`}},parseHexOrKeyword=value2=>{let plain=value2.replace("#",""),rgb2=import_color_convert.default.keyword.rgb(plain)||import_color_convert.default.hex.rgb(plain),hsl2=import_color_convert.default.rgb.hsl(rgb2),mapped=value2;/[^#a-f0-9]/i.test(value2)?mapped=plain:HEX_REGEXP.test(value2)&&(mapped=`#${plain}`);let valid=!0;if(mapped.startsWith("#"))valid=HEX_REGEXP.test(mapped);else try{import_color_convert.default.keyword.hex(mapped);}catch{valid=!1;}return {valid,value:mapped,keyword:import_color_convert.default.rgb.keyword(rgb2),colorSpace:"hex",rgb:`rgba(${rgb2[0]}, ${rgb2[1]}, ${rgb2[2]}, 1)`,hsl:`hsla(${hsl2[0]}, ${hsl2[1]}%, ${hsl2[2]}%, 1)`,hex:mapped}},parseValue=value2=>{if(value2)return RGB_REGEXP.test(value2)?parseRgb(value2):HSL_REGEXP.test(value2)?parseHsl(value2):parseHexOrKeyword(value2)},getRealValue=(value2,color,colorSpace)=>{if(!value2||!color?.valid)return fallbackColor[colorSpace];if(colorSpace!=="hex")return color?.[colorSpace]||fallbackColor[colorSpace];if(!color.hex.startsWith("#"))try{return `#${import_color_convert.default.keyword.hex(color.hex)}`}catch{return fallbackColor.hex}let short=color.hex.match(SHORTHEX_REGEXP);if(!short)return HEX_REGEXP.test(color.hex)?color.hex:fallbackColor.hex;let[r3,g3,b3]=short[1].split("");return `#${r3}${r3}${g3}${g3}${b3}${b3}`},useColorInput=(initialValue,onChange)=>{let[value2,setValue]=React20.useState(initialValue||""),[color,setColor]=React20.useState(()=>parseValue(value2)),[colorSpace,setColorSpace]=React20.useState(color?.colorSpace||"hex");React20.useEffect(()=>{let nextValue=initialValue||"",nextColor=parseValue(nextValue);setValue(nextValue),setColor(nextColor),setColorSpace(nextColor?.colorSpace||"hex");},[initialValue]);let realValue=React20.useMemo(()=>getRealValue(value2,color,colorSpace).toLowerCase(),[value2,color,colorSpace]),updateValue=React20.useCallback(update=>{let parsed=parseValue(update),v3=parsed?.value||update||"";setValue(v3),v3===""&&(setColor(void 0),onChange(void 0)),parsed&&(setColor(parsed),setColorSpace(parsed.colorSpace),onChange(parsed.value));},[onChange]),cycleColorSpace=React20.useCallback(()=>{let nextIndex=(COLOR_SPACES.indexOf(colorSpace)+1)%COLOR_SPACES.length,nextSpace=COLOR_SPACES[nextIndex];setColorSpace(nextSpace);let updatedValue=color?.[nextSpace]||"";setValue(updatedValue),onChange(updatedValue);},[color,colorSpace,onChange]);return {value:value2,realValue,updateValue,color,colorSpace,cycleColorSpace}},id=value2=>value2.replace(/\s*/,"").toLowerCase(),usePresets=(presetColors,currentColor,colorSpace)=>{let[selectedColors,setSelectedColors]=React20.useState(currentColor?.valid?[currentColor]:[]);React20.useEffect(()=>{currentColor===void 0&&setSelectedColors([]);},[currentColor]);let presets=React20.useMemo(()=>(presetColors||[]).map(preset=>typeof preset=="string"?parseValue(preset):preset.title?{...parseValue(preset.color),keyword:preset.title}:parseValue(preset.color)).concat(selectedColors).filter(Boolean).slice(-27),[presetColors,selectedColors]),addPreset=React20.useCallback(color=>{color?.valid&&(presets.some(preset=>preset&&preset[colorSpace]&&id(preset[colorSpace]||"")===id(color[colorSpace]||""))||setSelectedColors(arr=>arr.concat(color)));},[colorSpace,presets]);return {presets,addPreset}},ColorControl=({name,value:initialValue,onChange,onFocus,onBlur,presetColors,startOpen=!1,argType})=>{let debouncedOnChange=React20.useCallback(debounce2(onChange,200),[onChange]),{value:value2,realValue,updateValue,color,colorSpace,cycleColorSpace}=useColorInput(initialValue,debouncedOnChange),{presets,addPreset}=usePresets(presetColors??[],color,colorSpace),Picker=ColorPicker[colorSpace],readonly=!!argType?.table?.readonly;return React20__namespace.default.createElement(Wrapper9,{"aria-readonly":readonly},React20__namespace.default.createElement(PickerTooltip,{startOpen,trigger:readonly?null:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>color&&addPreset(color),tooltip:React20__namespace.default.createElement(TooltipContent,null,React20__namespace.default.createElement(Picker,{color:realValue==="transparent"?"#000000":realValue,onChange:updateValue,onFocus,onBlur}),presets.length>0&&React20__namespace.default.createElement(Swatches2,null,presets.map((preset,index)=>React20__namespace.default.createElement(components.WithTooltip,{key:`${preset?.value||index}-${index}`,hasChrome:!1,tooltip:React20__namespace.default.createElement(Note,{note:preset?.keyword||preset?.value||""})},React20__namespace.default.createElement(Swatch2,{value:preset?.[colorSpace]||"",active:!!(color&&preset&&preset[colorSpace]&&id(preset[colorSpace]||"")===id(color[colorSpace])),onClick:()=>preset&&updateValue(preset.value||"")})))))},React20__namespace.default.createElement(Swatch2,{value:realValue,style:{margin:4}})),React20__namespace.default.createElement(Input2,{id:getControlId(name),value:value2,onChange:e3=>updateValue(e3.target.value),onFocus:e3=>e3.target.select(),readOnly:readonly,placeholder:"Choose color..."}),value2?React20__namespace.default.createElement(ToggleIcon,{onClick:cycleColorSpace}):null)},Color_default=ColorControl;}});var require_memoizerific=__commonJS({"../../node_modules/memoizerific/memoizerific.js"(exports,module){(function(f3){if(typeof exports=="object"&&typeof module<"u")module.exports=f3();else if(typeof define=="function"&&define.amd)define([],f3);else {var g3;typeof window<"u"?g3=window:typeof global<"u"?g3=global:typeof self<"u"?g3=self:g3=this,g3.memoizerific=f3();}})(function(){return function e3(t3,n3,r3){function s3(o4,u3){if(!n3[o4]){if(!t3[o4]){var a3=typeof __require=="function"&&__require;if(!u3&&a3)return a3(o4,!0);if(i3)return i3(o4,!0);var f3=new Error("Cannot find module '"+o4+"'");throw f3.code="MODULE_NOT_FOUND",f3}var l3=n3[o4]={exports:{}};t3[o4][0].call(l3.exports,function(e4){var n4=t3[o4][1][e4];return s3(n4||e4)},l3,l3.exports,e3,t3,n3,r3);}return n3[o4].exports}for(var i3=typeof __require=="function"&&__require,o3=0;o3<r3.length;o3++)s3(r3[o3]);return s3}({1:[function(_dereq_,module3,exports3){module3.exports=function(forceSimilar){if(typeof Map!="function"||forceSimilar){var Similar=_dereq_("./similar");return new Similar}else return new Map};},{"./similar":2}],2:[function(_dereq_,module3,exports3){function Similar(){return this.list=[],this.lastItem=void 0,this.size=0,this}Similar.prototype.get=function(key){var index;if(this.lastItem&&this.isEqual(this.lastItem.key,key))return this.lastItem.val;if(index=this.indexOf(key),index>=0)return this.lastItem=this.list[index],this.list[index].val},Similar.prototype.set=function(key,val){var index;return this.lastItem&&this.isEqual(this.lastItem.key,key)?(this.lastItem.val=val,this):(index=this.indexOf(key),index>=0?(this.lastItem=this.list[index],this.list[index].val=val,this):(this.lastItem={key,val},this.list.push(this.lastItem),this.size++,this))},Similar.prototype.delete=function(key){var index;if(this.lastItem&&this.isEqual(this.lastItem.key,key)&&(this.lastItem=void 0),index=this.indexOf(key),index>=0)return this.size--,this.list.splice(index,1)[0]},Similar.prototype.has=function(key){var index;return this.lastItem&&this.isEqual(this.lastItem.key,key)?!0:(index=this.indexOf(key),index>=0?(this.lastItem=this.list[index],!0):!1)},Similar.prototype.forEach=function(callback,thisArg){var i3;for(i3=0;i3<this.size;i3++)callback.call(thisArg||this,this.list[i3].val,this.list[i3].key,this);},Similar.prototype.indexOf=function(key){var i3;for(i3=0;i3<this.size;i3++)if(this.isEqual(this.list[i3].key,key))return i3;return -1},Similar.prototype.isEqual=function(val1,val2){return val1===val2||val1!==val1&&val2!==val2},module3.exports=Similar;},{}],3:[function(_dereq_,module3,exports3){var MapOrSimilar=_dereq_("map-or-similar");module3.exports=function(limit){var cache=new MapOrSimilar(!1),lru=[];return function(fn){var memoizerific=function(){var currentCache=cache,newMap,fnResult,argsLengthMinusOne=arguments.length-1,lruPath=Array(argsLengthMinusOne+1),isMemoized=!0,i3;if((memoizerific.numArgs||memoizerific.numArgs===0)&&memoizerific.numArgs!==argsLengthMinusOne+1)throw new Error("Memoizerific functions should always be called with the same number of arguments");for(i3=0;i3<argsLengthMinusOne;i3++){if(lruPath[i3]={cacheItem:currentCache,arg:arguments[i3]},currentCache.has(arguments[i3])){currentCache=currentCache.get(arguments[i3]);continue}isMemoized=!1,newMap=new MapOrSimilar(!1),currentCache.set(arguments[i3],newMap),currentCache=newMap;}return isMemoized&&(currentCache.has(arguments[argsLengthMinusOne])?fnResult=currentCache.get(arguments[argsLengthMinusOne]):isMemoized=!1),isMemoized||(fnResult=fn.apply(null,arguments),currentCache.set(arguments[argsLengthMinusOne],fnResult)),limit>0&&(lruPath[argsLengthMinusOne]={cacheItem:currentCache,arg:arguments[argsLengthMinusOne]},isMemoized?moveToMostRecentLru(lru,lruPath):lru.push(lruPath),lru.length>limit&&removeCachedResult(lru.shift())),memoizerific.wasMemoized=isMemoized,memoizerific.numArgs=argsLengthMinusOne+1,fnResult};return memoizerific.limit=limit,memoizerific.wasMemoized=!1,memoizerific.cache=cache,memoizerific.lru=lru,memoizerific}};function moveToMostRecentLru(lru,lruPath){var lruLen=lru.length,lruPathLen=lruPath.length,isMatch,i3,ii;for(i3=0;i3<lruLen;i3++){for(isMatch=!0,ii=0;ii<lruPathLen;ii++)if(!isEqual(lru[i3][ii].arg,lruPath[ii].arg)){isMatch=!1;break}if(isMatch)break}lru.push(lru.splice(i3,1)[0]);}function removeCachedResult(removedLru){var removedLruLen=removedLru.length,currentLru=removedLru[removedLruLen-1],tmp,i3;for(currentLru.cacheItem.delete(currentLru.arg),i3=removedLruLen-2;i3>=0&&(currentLru=removedLru[i3],tmp=currentLru.cacheItem.get(currentLru.arg),!tmp||!tmp.size);i3--)currentLru.cacheItem.delete(currentLru.arg);}function isEqual(val1,val2){return val1===val2||val1!==val1&&val2!==val2}},{"map-or-similar":1}]},{},[3])(3)});}});init_compat();function _extends(){return _extends=Object.assign?Object.assign.bind():function(n3){for(var e3=1;e3<arguments.length;e3++){var t3=arguments[e3];for(var r3 in t3)({}).hasOwnProperty.call(t3,r3)&&(n3[r3]=t3[r3]);}return n3},_extends.apply(null,arguments)}function _assertThisInitialized(e3){if(e3===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e3}function _setPrototypeOf(t3,e3){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t4,e4){return t4.__proto__=e4,t4},_setPrototypeOf(t3,e3)}function _inheritsLoose(t3,o3){t3.prototype=Object.create(o3.prototype),t3.prototype.constructor=t3,_setPrototypeOf(t3,o3);}function _getPrototypeOf(t3){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t4){return t4.__proto__||Object.getPrototypeOf(t4)},_getPrototypeOf(t3)}function _isNativeFunction(t3){try{return Function.toString.call(t3).indexOf("[native code]")!==-1}catch{return typeof t3=="function"}}function _isNativeReflectConstruct(){try{var t3=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));}catch{}return (_isNativeReflectConstruct=function(){return !!t3})()}function _construct(t3,e3,r3){if(_isNativeReflectConstruct())return Reflect.construct.apply(null,arguments);var o3=[null];o3.push.apply(o3,e3);var p3=new(t3.bind.apply(t3,o3));return r3&&_setPrototypeOf(p3,r3.prototype),p3}function _wrapNativeSuper(t3){var r3=typeof Map=="function"?new Map:void 0;return _wrapNativeSuper=function(t4){if(t4===null||!_isNativeFunction(t4))return t4;if(typeof t4!="function")throw new TypeError("Super expression must either be null or a function");if(r3!==void 0){if(r3.has(t4))return r3.get(t4);r3.set(t4,Wrapper12);}function Wrapper12(){return _construct(t4,arguments,_getPrototypeOf(this).constructor)}return Wrapper12.prototype=Object.create(t4.prototype,{constructor:{value:Wrapper12,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(Wrapper12,t4)},_wrapNativeSuper(t3)}var ERRORS={1:`Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).

`,2:`Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).

`,3:`Passed an incorrect argument to a color function, please pass a string representation of a color.

`,4:`Couldn't generate valid rgb string from %s, it returned %s.

`,5:`Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.

`,6:`Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).

`,7:`Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).

`,8:`Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.

`,9:`Please provide a number of steps to the modularScale helper.

`,10:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,11:`Invalid value passed as base to modularScale, expected number or em string but got "%s"

`,12:`Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.

`,13:`Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.

`,14:`Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.

`,15:`Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.

`,16:`You must provide a template to this method.

`,17:`You passed an unsupported selector state to this method.

`,18:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,19:`fromSize and toSize must be provided as stringified numbers with the same units.

`,20:`expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,21:"expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",22:"expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",23:`fontFace expects a name of a font-family.

`,24:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,25:`fontFace expects localFonts to be an array.

`,26:`fontFace expects fileFormats to be an array.

`,27:`radialGradient requries at least 2 color-stops to properly render.

`,28:`Please supply a filename to retinaImage() as the first argument.

`,29:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,30:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",31:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation

`,32:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])
To pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')

`,33:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation

`,34:`borderRadius expects a radius value as a string or number as the second argument.

`,35:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,36:`Property must be a string value.

`,37:`Syntax Error at %s.

`,38:`Formula contains a function that needs parentheses at %s.

`,39:`Formula is missing closing parenthesis at %s.

`,40:`Formula has too many closing parentheses at %s.

`,41:`All values in a formula must have the same unit or be unitless.

`,42:`Please provide a number of steps to the modularScale helper.

`,43:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,44:`Invalid value passed as base to modularScale, expected number or em/rem string but got %s.

`,45:`Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.

`,46:`Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.

`,47:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,48:`fromSize and toSize must be provided as stringified numbers with the same units.

`,49:`Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,50:`Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.

`,51:`Expects the first argument object to have the properties prop, fromSize, and toSize.

`,52:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,53:`fontFace expects localFonts to be an array.

`,54:`fontFace expects fileFormats to be an array.

`,55:`fontFace expects a name of a font-family.

`,56:`linearGradient requries at least 2 color-stops to properly render.

`,57:`radialGradient requries at least 2 color-stops to properly render.

`,58:`Please supply a filename to retinaImage() as the first argument.

`,59:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,60:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",61:`Property must be a string value.

`,62:`borderRadius expects a radius value as a string or number as the second argument.

`,63:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,64:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.

`,65:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').

`,66:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.

`,67:`You must provide a template to this method.

`,68:`You passed an unsupported selector state to this method.

`,69:`Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.

`,70:`Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.

`,71:`Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.

`,72:`Passed invalid base value %s to %s(), please pass a value like "12px" or 12.

`,73:`Please provide a valid CSS variable.

`,74:`CSS variable not found and no default was provided.

`,75:`important requires a valid style object, got a %s instead.

`,76:`fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.

`,77:`remToPx expects a value in "rem" but you provided it in "%s".

`,78:`base must be set in "px" or "%" but you set it in "%s".
`};function format(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];var a3=args[0],b3=[],c3;for(c3=1;c3<args.length;c3+=1)b3.push(args[c3]);return b3.forEach(function(d3){a3=a3.replace(/%[a-z]/,d3);}),a3}var PolishedError=function(_Error){_inheritsLoose(PolishedError2,_Error);function PolishedError2(code){var _this;if(process.env.NODE_ENV==="production")_this=_Error.call(this,"An error occurred. See https://github.com/styled-components/polished/blob/main/src/internalHelpers/errors.md#"+code+" for more information.")||this;else {for(var _len2=arguments.length,args=new Array(_len2>1?_len2-1:0),_key2=1;_key2<_len2;_key2++)args[_key2-1]=arguments[_key2];_this=_Error.call(this,format.apply(void 0,[ERRORS[code]].concat(args)))||this;}return _assertThisInitialized(_this)}return PolishedError2}(_wrapNativeSuper(Error));function colorToInt(color){return Math.round(color*255)}function convertToInt(red,green,blue){return colorToInt(red)+","+colorToInt(green)+","+colorToInt(blue)}function hslToRgb(hue,saturation,lightness,convert3){if(convert3===void 0&&(convert3=convertToInt),saturation===0)return convert3(lightness,lightness,lightness);var huePrime=(hue%360+360)%360/60,chroma=(1-Math.abs(2*lightness-1))*saturation,secondComponent=chroma*(1-Math.abs(huePrime%2-1)),red=0,green=0,blue=0;huePrime>=0&&huePrime<1?(red=chroma,green=secondComponent):huePrime>=1&&huePrime<2?(red=secondComponent,green=chroma):huePrime>=2&&huePrime<3?(green=chroma,blue=secondComponent):huePrime>=3&&huePrime<4?(green=secondComponent,blue=chroma):huePrime>=4&&huePrime<5?(red=secondComponent,blue=chroma):huePrime>=5&&huePrime<6&&(red=chroma,blue=secondComponent);var lightnessModification=lightness-chroma/2,finalRed=red+lightnessModification,finalGreen=green+lightnessModification,finalBlue=blue+lightnessModification;return convert3(finalRed,finalGreen,finalBlue)}var namedColorMap={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};function nameToHex(color){if(typeof color!="string")return color;var normalizedColorName=color.toLowerCase();return namedColorMap[normalizedColorName]?"#"+namedColorMap[normalizedColorName]:color}var hexRegex=/^#[a-fA-F0-9]{6}$/,hexRgbaRegex=/^#[a-fA-F0-9]{8}$/,reducedHexRegex=/^#[a-fA-F0-9]{3}$/,reducedRgbaHexRegex=/^#[a-fA-F0-9]{4}$/,rgbRegex=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,rgbaRegex=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,hslRegex=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,hslaRegex=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function parseToRgb(color){if(typeof color!="string")throw new PolishedError(3);var normalizedColor=nameToHex(color);if(normalizedColor.match(hexRegex))return {red:parseInt(""+normalizedColor[1]+normalizedColor[2],16),green:parseInt(""+normalizedColor[3]+normalizedColor[4],16),blue:parseInt(""+normalizedColor[5]+normalizedColor[6],16)};if(normalizedColor.match(hexRgbaRegex)){var alpha=parseFloat((parseInt(""+normalizedColor[7]+normalizedColor[8],16)/255).toFixed(2));return {red:parseInt(""+normalizedColor[1]+normalizedColor[2],16),green:parseInt(""+normalizedColor[3]+normalizedColor[4],16),blue:parseInt(""+normalizedColor[5]+normalizedColor[6],16),alpha}}if(normalizedColor.match(reducedHexRegex))return {red:parseInt(""+normalizedColor[1]+normalizedColor[1],16),green:parseInt(""+normalizedColor[2]+normalizedColor[2],16),blue:parseInt(""+normalizedColor[3]+normalizedColor[3],16)};if(normalizedColor.match(reducedRgbaHexRegex)){var _alpha=parseFloat((parseInt(""+normalizedColor[4]+normalizedColor[4],16)/255).toFixed(2));return {red:parseInt(""+normalizedColor[1]+normalizedColor[1],16),green:parseInt(""+normalizedColor[2]+normalizedColor[2],16),blue:parseInt(""+normalizedColor[3]+normalizedColor[3],16),alpha:_alpha}}var rgbMatched=rgbRegex.exec(normalizedColor);if(rgbMatched)return {red:parseInt(""+rgbMatched[1],10),green:parseInt(""+rgbMatched[2],10),blue:parseInt(""+rgbMatched[3],10)};var rgbaMatched=rgbaRegex.exec(normalizedColor.substring(0,50));if(rgbaMatched)return {red:parseInt(""+rgbaMatched[1],10),green:parseInt(""+rgbaMatched[2],10),blue:parseInt(""+rgbaMatched[3],10),alpha:parseFloat(""+rgbaMatched[4])>1?parseFloat(""+rgbaMatched[4])/100:parseFloat(""+rgbaMatched[4])};var hslMatched=hslRegex.exec(normalizedColor);if(hslMatched){var hue=parseInt(""+hslMatched[1],10),saturation=parseInt(""+hslMatched[2],10)/100,lightness=parseInt(""+hslMatched[3],10)/100,rgbColorString="rgb("+hslToRgb(hue,saturation,lightness)+")",hslRgbMatched=rgbRegex.exec(rgbColorString);if(!hslRgbMatched)throw new PolishedError(4,normalizedColor,rgbColorString);return {red:parseInt(""+hslRgbMatched[1],10),green:parseInt(""+hslRgbMatched[2],10),blue:parseInt(""+hslRgbMatched[3],10)}}var hslaMatched=hslaRegex.exec(normalizedColor.substring(0,50));if(hslaMatched){var _hue=parseInt(""+hslaMatched[1],10),_saturation=parseInt(""+hslaMatched[2],10)/100,_lightness=parseInt(""+hslaMatched[3],10)/100,_rgbColorString="rgb("+hslToRgb(_hue,_saturation,_lightness)+")",_hslRgbMatched=rgbRegex.exec(_rgbColorString);if(!_hslRgbMatched)throw new PolishedError(4,normalizedColor,_rgbColorString);return {red:parseInt(""+_hslRgbMatched[1],10),green:parseInt(""+_hslRgbMatched[2],10),blue:parseInt(""+_hslRgbMatched[3],10),alpha:parseFloat(""+hslaMatched[4])>1?parseFloat(""+hslaMatched[4])/100:parseFloat(""+hslaMatched[4])}}throw new PolishedError(5)}function rgbToHsl(color){var red=color.red/255,green=color.green/255,blue=color.blue/255,max=Math.max(red,green,blue),min=Math.min(red,green,blue),lightness=(max+min)/2;if(max===min)return color.alpha!==void 0?{hue:0,saturation:0,lightness,alpha:color.alpha}:{hue:0,saturation:0,lightness};var hue,delta=max-min,saturation=lightness>.5?delta/(2-max-min):delta/(max+min);switch(max){case red:hue=(green-blue)/delta+(green<blue?6:0);break;case green:hue=(blue-red)/delta+2;break;default:hue=(red-green)/delta+4;break}return hue*=60,color.alpha!==void 0?{hue,saturation,lightness,alpha:color.alpha}:{hue,saturation,lightness}}function parseToHsl(color){return rgbToHsl(parseToRgb(color))}var reduceHexValue=function(value2){return value2.length===7&&value2[1]===value2[2]&&value2[3]===value2[4]&&value2[5]===value2[6]?"#"+value2[1]+value2[3]+value2[5]:value2},reduceHexValue$1=reduceHexValue;function numberToHex(value2){var hex=value2.toString(16);return hex.length===1?"0"+hex:hex}function colorToHex(color){return numberToHex(Math.round(color*255))}function convertToHex(red,green,blue){return reduceHexValue$1("#"+colorToHex(red)+colorToHex(green)+colorToHex(blue))}function hslToHex(hue,saturation,lightness){return hslToRgb(hue,saturation,lightness,convertToHex)}function hsl(value2,saturation,lightness){if(typeof value2=="number"&&typeof saturation=="number"&&typeof lightness=="number")return hslToHex(value2,saturation,lightness);if(typeof value2=="object"&&saturation===void 0&&lightness===void 0)return hslToHex(value2.hue,value2.saturation,value2.lightness);throw new PolishedError(1)}function hsla(value2,saturation,lightness,alpha){if(typeof value2=="number"&&typeof saturation=="number"&&typeof lightness=="number"&&typeof alpha=="number")return alpha>=1?hslToHex(value2,saturation,lightness):"rgba("+hslToRgb(value2,saturation,lightness)+","+alpha+")";if(typeof value2=="object"&&saturation===void 0&&lightness===void 0&&alpha===void 0)return value2.alpha>=1?hslToHex(value2.hue,value2.saturation,value2.lightness):"rgba("+hslToRgb(value2.hue,value2.saturation,value2.lightness)+","+value2.alpha+")";throw new PolishedError(2)}function rgb(value2,green,blue){if(typeof value2=="number"&&typeof green=="number"&&typeof blue=="number")return reduceHexValue$1("#"+numberToHex(value2)+numberToHex(green)+numberToHex(blue));if(typeof value2=="object"&&green===void 0&&blue===void 0)return reduceHexValue$1("#"+numberToHex(value2.red)+numberToHex(value2.green)+numberToHex(value2.blue));throw new PolishedError(6)}function rgba(firstValue,secondValue,thirdValue,fourthValue){if(typeof firstValue=="string"&&typeof secondValue=="number"){var rgbValue=parseToRgb(firstValue);return "rgba("+rgbValue.red+","+rgbValue.green+","+rgbValue.blue+","+secondValue+")"}else {if(typeof firstValue=="number"&&typeof secondValue=="number"&&typeof thirdValue=="number"&&typeof fourthValue=="number")return fourthValue>=1?rgb(firstValue,secondValue,thirdValue):"rgba("+firstValue+","+secondValue+","+thirdValue+","+fourthValue+")";if(typeof firstValue=="object"&&secondValue===void 0&&thirdValue===void 0&&fourthValue===void 0)return firstValue.alpha>=1?rgb(firstValue.red,firstValue.green,firstValue.blue):"rgba("+firstValue.red+","+firstValue.green+","+firstValue.blue+","+firstValue.alpha+")"}throw new PolishedError(7)}var isRgb=function(color){return typeof color.red=="number"&&typeof color.green=="number"&&typeof color.blue=="number"&&(typeof color.alpha!="number"||typeof color.alpha>"u")},isRgba=function(color){return typeof color.red=="number"&&typeof color.green=="number"&&typeof color.blue=="number"&&typeof color.alpha=="number"},isHsl=function(color){return typeof color.hue=="number"&&typeof color.saturation=="number"&&typeof color.lightness=="number"&&(typeof color.alpha!="number"||typeof color.alpha>"u")},isHsla=function(color){return typeof color.hue=="number"&&typeof color.saturation=="number"&&typeof color.lightness=="number"&&typeof color.alpha=="number"};function toColorString(color){if(typeof color!="object")throw new PolishedError(8);if(isRgba(color))return rgba(color);if(isRgb(color))return rgb(color);if(isHsla(color))return hsla(color);if(isHsl(color))return hsl(color);throw new PolishedError(8)}function curried(f3,length,acc){return function(){var combined=acc.concat(Array.prototype.slice.call(arguments));return combined.length>=length?f3.apply(this,combined):curried(f3,length,combined)}}function curry(f3){return curried(f3,f3.length,[])}function adjustHue(degree,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{hue:hslColor.hue+parseFloat(degree)}))}curry(adjustHue);function guard(lowerBoundary,upperBoundary,value2){return Math.max(lowerBoundary,Math.min(upperBoundary,value2))}function darken(amount,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{lightness:guard(0,1,hslColor.lightness-parseFloat(amount))}))}var curriedDarken=curry(darken),curriedDarken$1=curriedDarken;function desaturate(amount,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{saturation:guard(0,1,hslColor.saturation-parseFloat(amount))}))}curry(desaturate);function lighten(amount,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{lightness:guard(0,1,hslColor.lightness+parseFloat(amount))}))}var curriedLighten=curry(lighten),curriedLighten$1=curriedLighten;function mix(weight,color,otherColor){if(color==="transparent")return otherColor;if(otherColor==="transparent")return color;if(weight===0)return otherColor;var parsedColor1=parseToRgb(color),color1=_extends({},parsedColor1,{alpha:typeof parsedColor1.alpha=="number"?parsedColor1.alpha:1}),parsedColor2=parseToRgb(otherColor),color2=_extends({},parsedColor2,{alpha:typeof parsedColor2.alpha=="number"?parsedColor2.alpha:1}),alphaDelta=color1.alpha-color2.alpha,x3=parseFloat(weight)*2-1,y3=x3*alphaDelta===-1?x3:x3+alphaDelta,z3=1+x3*alphaDelta,weight1=(y3/z3+1)/2,weight2=1-weight1,mixedColor={red:Math.floor(color1.red*weight1+color2.red*weight2),green:Math.floor(color1.green*weight1+color2.green*weight2),blue:Math.floor(color1.blue*weight1+color2.blue*weight2),alpha:color1.alpha*parseFloat(weight)+color2.alpha*(1-parseFloat(weight))};return rgba(mixedColor)}var curriedMix=curry(mix),mix$1=curriedMix;function opacify(amount,color){if(color==="transparent")return color;var parsedColor=parseToRgb(color),alpha=typeof parsedColor.alpha=="number"?parsedColor.alpha:1,colorWithAlpha=_extends({},parsedColor,{alpha:guard(0,1,(alpha*100+parseFloat(amount)*100)/100)});return rgba(colorWithAlpha)}var curriedOpacify=curry(opacify),curriedOpacify$1=curriedOpacify;function saturate(amount,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{saturation:guard(0,1,hslColor.saturation+parseFloat(amount))}))}curry(saturate);function setHue(hue,color){return color==="transparent"?color:toColorString(_extends({},parseToHsl(color),{hue:parseFloat(hue)}))}curry(setHue);function setLightness(lightness,color){return color==="transparent"?color:toColorString(_extends({},parseToHsl(color),{lightness:parseFloat(lightness)}))}curry(setLightness);function setSaturation(saturation,color){return color==="transparent"?color:toColorString(_extends({},parseToHsl(color),{saturation:parseFloat(saturation)}))}curry(setSaturation);function shade(percentage,color){return color==="transparent"?color:mix$1(parseFloat(percentage),"rgb(0, 0, 0)",color)}curry(shade);function tint(percentage,color){return color==="transparent"?color:mix$1(parseFloat(percentage),"rgb(255, 255, 255)",color)}curry(tint);function transparentize(amount,color){if(color==="transparent")return color;var parsedColor=parseToRgb(color),alpha=typeof parsedColor.alpha=="number"?parsedColor.alpha:1,colorWithAlpha=_extends({},parsedColor,{alpha:guard(0,1,+(alpha*100-parseFloat(amount)*100).toFixed(2)/100)});return rgba(colorWithAlpha)}var curriedTransparentize=curry(transparentize),curriedTransparentize$1=curriedTransparentize;var Wrapper=theming.styled.div(components.withReset,({theme})=>({backgroundColor:theme.base==="light"?"rgba(0,0,0,.01)":"rgba(255,255,255,.01)",borderRadius:theme.appBorderRadius,border:`1px dashed ${theme.appBorderColor}`,display:"flex",alignItems:"center",justifyContent:"center",padding:20,margin:"25px 0 40px",color:curriedTransparentize$1(.3,theme.color.defaultText),fontSize:theme.typography.size.s2})),EmptyBlock=props=>React20__namespace.default.createElement(Wrapper,{...props,className:"docblock-emptyblock sb-unstyled"});var StyledSyntaxHighlighter=theming.styled(components.SyntaxHighlighter)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,lineHeight:"19px",margin:"25px 0 40px",borderRadius:theme.appBorderRadius,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0","pre.prismjs":{padding:20,background:"inherit"}}));var SourceSkeletonWrapper=theming.styled.div(({theme})=>({background:theme.background.content,borderRadius:theme.appBorderRadius,border:`1px solid ${theme.appBorderColor}`,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",margin:"25px 0 40px",padding:"20px 20px 20px 22px"})),SourceSkeletonPlaceholder=theming.styled.div(({theme})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,height:17,marginTop:1,width:"60%",[`&:first-child${theming.ignoreSsrWarning}`]:{margin:0}})),SourceSkeleton=()=>React20__namespace.default.createElement(SourceSkeletonWrapper,null,React20__namespace.default.createElement(SourceSkeletonPlaceholder,null),React20__namespace.default.createElement(SourceSkeletonPlaceholder,{style:{width:"80%"}}),React20__namespace.default.createElement(SourceSkeletonPlaceholder,{style:{width:"30%"}}),React20__namespace.default.createElement(SourceSkeletonPlaceholder,{style:{width:"80%"}})),Source=({isLoading,error,language,code,dark,format:format3=!0,...rest})=>{let{typography}=theming.useTheme();if(isLoading)return React20__namespace.default.createElement(SourceSkeleton,null);if(error)return React20__namespace.default.createElement(EmptyBlock,null,error);let syntaxHighlighter=React20__namespace.default.createElement(StyledSyntaxHighlighter,{bordered:!0,copyable:!0,format:format3,language:language??"jsx",className:"docblock-source sb-unstyled",...rest},code);if(typeof dark>"u")return syntaxHighlighter;let overrideTheme=dark?theming.themes.dark:theming.themes.light;return React20__namespace.default.createElement(theming.ThemeProvider,{theme:theming.convert({...overrideTheme,fontCode:typography.fonts.mono,fontBase:typography.fonts.base})},syntaxHighlighter)};var toGlobalSelector=element=>`& :where(${element}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${element}))`,breakpoint=600,Title=theming.styled.h1(components.withReset,({theme})=>({color:theme.color.defaultText,fontSize:theme.typography.size.m3,fontWeight:theme.typography.weight.bold,lineHeight:"32px",[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.l1,lineHeight:"36px",marginBottom:"16px"}})),Subtitle=theming.styled.h2(components.withReset,({theme})=>({fontWeight:theme.typography.weight.regular,fontSize:theme.typography.size.s3,lineHeight:"20px",borderBottom:"none",marginBottom:15,[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.m1,lineHeight:"28px",marginBottom:24},color:curriedTransparentize$1(.25,theme.color.defaultText)})),DocsContent=theming.styled.div(({theme})=>{let reset={fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s3,margin:0,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch"},headers={margin:"20px 0 8px",padding:0,cursor:"text",position:"relative",color:theme.color.defaultText,"&:first-of-type":{marginTop:0,paddingTop:0},"&:hover a.anchor":{textDecoration:"none"},"& code":{fontSize:"inherit"}},code={lineHeight:1,margin:"0 2px",padding:"3px 5px",whiteSpace:"nowrap",borderRadius:3,fontSize:theme.typography.size.s2-1,border:theme.base==="light"?`1px solid ${theme.color.mediumlight}`:`1px solid ${theme.color.darker}`,color:theme.base==="light"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),backgroundColor:theme.base==="light"?theme.color.lighter:theme.color.border};return {maxWidth:1e3,width:"100%",minWidth:0,[toGlobalSelector("a")]:{...reset,fontSize:"inherit",lineHeight:"24px",color:theme.color.secondary,textDecoration:"none","&.absent":{color:"#cc0000"},"&.anchor":{display:"block",paddingLeft:30,marginLeft:-30,cursor:"pointer",position:"absolute",top:0,left:0,bottom:0}},[toGlobalSelector("blockquote")]:{...reset,margin:"16px 0",borderLeft:`4px solid ${theme.color.medium}`,padding:"0 15px",color:theme.color.dark,"& > :first-of-type":{marginTop:0},"& > :last-child":{marginBottom:0}},[toGlobalSelector("div")]:reset,[toGlobalSelector("dl")]:{...reset,margin:"16px 0",padding:0,"& dt":{fontSize:"14px",fontWeight:"bold",fontStyle:"italic",padding:0,margin:"16px 0 4px"},"& dt:first-of-type":{padding:0},"& dt > :first-of-type":{marginTop:0},"& dt > :last-child":{marginBottom:0},"& dd":{margin:"0 0 16px",padding:"0 15px"},"& dd > :first-of-type":{marginTop:0},"& dd > :last-child":{marginBottom:0}},[toGlobalSelector("h1")]:{...reset,...headers,fontSize:`${theme.typography.size.l1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector("h2")]:{...reset,...headers,fontSize:`${theme.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${theme.appBorderColor}`},[toGlobalSelector("h3")]:{...reset,...headers,fontSize:`${theme.typography.size.m1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector("h4")]:{...reset,...headers,fontSize:`${theme.typography.size.s3}px`},[toGlobalSelector("h5")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`},[toGlobalSelector("h6")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`,color:theme.color.dark},[toGlobalSelector("hr")]:{border:"0 none",borderTop:`1px solid ${theme.appBorderColor}`,height:4,padding:0},[toGlobalSelector("img")]:{maxWidth:"100%"},[toGlobalSelector("li")]:{...reset,fontSize:theme.typography.size.s2,color:theme.color.defaultText,lineHeight:"24px","& + li":{marginTop:".25em"},"& ul, & ol":{marginTop:".25em",marginBottom:0},"& code":code},[toGlobalSelector("ol")]:{...reset,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0}},[toGlobalSelector("p")]:{...reset,margin:"16px 0",fontSize:theme.typography.size.s2,lineHeight:"24px",color:theme.color.defaultText,"& code":code},[toGlobalSelector("pre")]:{...reset,fontFamily:theme.typography.fonts.mono,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",lineHeight:"18px",padding:"11px 1rem",whiteSpace:"pre-wrap",color:"inherit",borderRadius:3,margin:"1rem 0","&:not(.prismjs)":{background:"transparent",border:"none",borderRadius:0,padding:0,margin:0},"& pre, &.prismjs":{padding:15,margin:0,whiteSpace:"pre-wrap",color:"inherit",fontSize:"13px",lineHeight:"19px",code:{color:"inherit",fontSize:"inherit"}},"& code":{whiteSpace:"pre"},"& code, & tt":{border:"none"}},[toGlobalSelector("span")]:{...reset,"&.frame":{display:"block",overflow:"hidden","& > span":{border:`1px solid ${theme.color.medium}`,display:"block",float:"left",overflow:"hidden",margin:"13px 0 0",padding:7,width:"auto"},"& span img":{display:"block",float:"left"},"& span span":{clear:"both",color:theme.color.darkest,display:"block",padding:"5px 0 0"}},"&.align-center":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"center"},"& span img":{margin:"0 auto",textAlign:"center"}},"&.align-right":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px 0 0",textAlign:"right"},"& span img":{margin:0,textAlign:"right"}},"&.float-left":{display:"block",marginRight:13,overflow:"hidden",float:"left","& span":{margin:"13px 0 0"}},"&.float-right":{display:"block",marginLeft:13,overflow:"hidden",float:"right","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"right"}}},[toGlobalSelector("table")]:{...reset,margin:"16px 0",fontSize:theme.typography.size.s2,lineHeight:"24px",padding:0,borderCollapse:"collapse","& tr":{borderTop:`1px solid ${theme.appBorderColor}`,backgroundColor:theme.appContentBg,margin:0,padding:0},"& tr:nth-of-type(2n)":{backgroundColor:theme.base==="dark"?theme.color.darker:theme.color.lighter},"& tr th":{fontWeight:"bold",color:theme.color.defaultText,border:`1px solid ${theme.appBorderColor}`,margin:0,padding:"6px 13px"},"& tr td":{border:`1px solid ${theme.appBorderColor}`,color:theme.color.defaultText,margin:0,padding:"6px 13px"},"& tr th :first-of-type, & tr td :first-of-type":{marginTop:0},"& tr th :last-child, & tr td :last-child":{marginBottom:0}},[toGlobalSelector("ul")]:{...reset,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0},listStyle:"disc"}}}),DocsWrapper=theming.styled.div(({theme})=>({background:theme.background.content,display:"flex",flexDirection:"row-reverse",justifyContent:"center",padding:"4rem 20px",minHeight:"100vh",boxSizing:"border-box",gap:"3rem",[`@media (min-width: ${breakpoint}px)`]:{}})),DocsPageWrapper=({children,toc})=>React20__namespace.default.createElement(DocsWrapper,{className:"sbdocs sbdocs-wrapper"},toc,React20__namespace.default.createElement(DocsContent,{className:"sbdocs sbdocs-content"},children));var getBlockBackgroundStyle=theme=>({borderRadius:theme.appBorderRadius,background:theme.background.content,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",border:`1px solid ${theme.appBorderColor}`});var{window:globalWindow}=globalThis,IFrame=class extends React20.Component{constructor(){super(...arguments);this.iframe=null;}componentDidMount(){let{id:id2}=this.props;this.iframe=globalWindow.document.getElementById(id2);}shouldComponentUpdate(nextProps){let{scale}=nextProps;return scale!==this.props.scale&&this.setIframeBodyStyle({width:`${scale*100}%`,height:`${scale*100}%`,transform:`scale(${1/scale})`,transformOrigin:"top left"}),!1}setIframeBodyStyle(style){return Object.assign(this.iframe.contentDocument.body.style,style)}render(){let{id:id2,title,src,allowFullScreen,scale,...rest}=this.props;return React20__namespace.default.createElement("iframe",{id:id2,title,src,...allowFullScreen?{allow:"fullscreen"}:{},loading:"lazy",...rest})}};var ZoomContext=React20.createContext({scale:1});var{PREVIEW_URL}=globalThis,BASE_URL=PREVIEW_URL||"iframe.html",storyBlockIdFromId=({story,primary})=>`story--${story.id}${primary?"--primary":""}`,InlineStory=props=>{let storyRef=React20.useRef(),[showLoader,setShowLoader]=React20.useState(!0),[error,setError]=React20.useState(),{story,height,autoplay,forceInitialArgs,renderStoryToElement}=props;return React20.useEffect(()=>{if(!(story&&storyRef.current))return ()=>{};let element=storyRef.current,cleanup=renderStoryToElement(story,element,{showMain:()=>{},showError:({title,description})=>setError(new Error(`${title} - ${description}`)),showException:err=>setError(err)},{autoplay,forceInitialArgs});return setShowLoader(!1),()=>{Promise.resolve().then(()=>cleanup());}},[autoplay,renderStoryToElement,story]),error?React20__namespace.default.createElement("pre",null,React20__namespace.default.createElement(components.ErrorFormatter,{error})):React20__namespace.default.createElement(React20__namespace.default.Fragment,null,height?React20__namespace.default.createElement("style",null,`#${storyBlockIdFromId(props)} { min-height: ${height}; transform: translateZ(0); overflow: auto }`):null,showLoader&&React20__namespace.default.createElement(StorySkeleton,null),React20__namespace.default.createElement("div",{ref:storyRef,id:`${storyBlockIdFromId(props)}-inner`,"data-name":story.name}))},IFrameStory=({story,height="500px"})=>React20__namespace.default.createElement("div",{style:{width:"100%",height}},React20__namespace.default.createElement(ZoomContext.Consumer,null,({scale})=>React20__namespace.default.createElement(IFrame,{key:"iframe",id:`iframe--${story.id}`,title:story.name,src:components.getStoryHref(BASE_URL,story.id,{viewMode:"story"}),allowFullScreen:!0,scale,style:{width:"100%",height:"100%",border:"0 none"}}))),ErrorMessage=theming.styled.strong(({theme})=>({color:theme.color.orange})),Story=props=>{let{inline,story}=props;return inline&&!props.autoplay&&story.usesMount?React20__namespace.default.createElement(ErrorMessage,null,"This story mounts inside of play. Set"," ",React20__namespace.default.createElement("a",{href:"https://storybook.js.org/docs/api/doc-blocks/doc-block-story#autoplay"},"autoplay")," ","to true to view this story."):React20__namespace.default.createElement("div",{id:storyBlockIdFromId(props),className:"sb-story sb-unstyled","data-story-block":"true"},inline?React20__namespace.default.createElement(InlineStory,{...props}):React20__namespace.default.createElement(IFrameStory,{...props}))},StorySkeleton=()=>React20__namespace.default.createElement(components.Loader,null);var Bar=theming.styled(components.FlexBar)({position:"absolute",left:0,right:0,top:0,transition:"transform .2s linear"}),Wrapper2=theming.styled.div({display:"flex",alignItems:"center",gap:4}),IconPlaceholder=theming.styled.div(({theme})=>({width:14,height:14,borderRadius:2,margin:"0 7px",backgroundColor:theme.appBorderColor,animation:`${theme.animation.glow} 1.5s ease-in-out infinite`})),Toolbar=({isLoading,storyId,baseUrl,zoom,resetZoom,...rest})=>React20__namespace.default.createElement(Bar,{...rest},React20__namespace.default.createElement(Wrapper2,{key:"left"},isLoading?[1,2,3].map(key=>React20__namespace.default.createElement(IconPlaceholder,{key})):React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(components.IconButton,{key:"zoomin",onClick:e3=>{e3.preventDefault(),zoom(.8);},title:"Zoom in"},React20__namespace.default.createElement(icons.ZoomIcon,null)),React20__namespace.default.createElement(components.IconButton,{key:"zoomout",onClick:e3=>{e3.preventDefault(),zoom(1.25);},title:"Zoom out"},React20__namespace.default.createElement(icons.ZoomOutIcon,null)),React20__namespace.default.createElement(components.IconButton,{key:"zoomreset",onClick:e3=>{e3.preventDefault(),resetZoom();},title:"Reset zoom"},React20__namespace.default.createElement(icons.ZoomResetIcon,null)))));var ChildrenContainer=theming.styled.div(({isColumn,columns,layout})=>({display:isColumn||!columns?"block":"flex",position:"relative",flexWrap:"wrap",overflow:"auto",flexDirection:isColumn?"column":"row","& .innerZoomElementWrapper > *":isColumn?{width:layout!=="fullscreen"?"calc(100% - 20px)":"100%",display:"block"}:{maxWidth:layout!=="fullscreen"?"calc(100% - 20px)":"100%",display:"inline-block"}}),({layout="padded",inline})=>layout==="centered"||layout==="padded"?{padding:inline?"32px 22px":"0px","& .innerZoomElementWrapper > *":{width:"auto",border:"8px solid transparent!important"}}:{},({layout="padded",inline})=>layout==="centered"&&inline?{display:"flex",justifyContent:"center",justifyItems:"center",alignContent:"center",alignItems:"center"}:{},({columns})=>columns&&columns>1?{".innerZoomElementWrapper > *":{minWidth:`calc(100% / ${columns} - 20px)`}}:{}),StyledSource=theming.styled(Source)(({theme})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:theme.appBorderRadius,borderBottomRightRadius:theme.appBorderRadius,border:"none",background:theme.base==="light"?"rgba(0, 0, 0, 0.85)":curriedDarken$1(.05,theme.background.content),color:theme.color.lightest,button:{background:theme.base==="light"?"rgba(0, 0, 0, 0.85)":curriedDarken$1(.05,theme.background.content)}})),PreviewContainer=theming.styled.div(({theme,withSource,isExpanded})=>({position:"relative",overflow:"hidden",margin:"25px 0 40px",...getBlockBackgroundStyle(theme),borderBottomLeftRadius:withSource&&isExpanded&&0,borderBottomRightRadius:withSource&&isExpanded&&0,borderBottomWidth:isExpanded&&0,"h3 + &":{marginTop:"16px"}}),({withToolbar})=>withToolbar&&{paddingTop:40}),getSource=(withSource,expanded,setExpanded)=>{switch(!0){case!!(withSource&&withSource.error):return {source:null,actionItem:{title:"No code available",className:"docblock-code-toggle docblock-code-toggle--disabled",disabled:!0,onClick:()=>setExpanded(!1)}};case expanded:return {source:React20__namespace.default.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:"Hide code",className:"docblock-code-toggle docblock-code-toggle--expanded",onClick:()=>setExpanded(!1)}};default:return {source:React20__namespace.default.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:"Show code",className:"docblock-code-toggle",onClick:()=>setExpanded(!0)}}}};function getStoryId(children){if(React20.Children.count(children)===1){let elt=children;if(elt.props)return elt.props.id}return null}var PositionedToolbar=theming.styled(Toolbar)({position:"absolute",top:0,left:0,right:0,height:40}),Relative=theming.styled.div({overflow:"hidden",position:"relative"}),Preview=({isLoading,isColumn,columns,children,withSource,withToolbar=!1,isExpanded=!1,additionalActions,className,layout="padded",inline=!1,...props})=>{let[expanded,setExpanded]=React20.useState(isExpanded),{source,actionItem}=getSource(withSource,expanded,setExpanded),[scale,setScale]=React20.useState(1),previewClasses=[className].concat(["sbdocs","sbdocs-preview","sb-unstyled"]),defaultActionItems=withSource?[actionItem]:[],[additionalActionItems,setAdditionalActionItems]=React20.useState(additionalActions?[...additionalActions]:[]),actionItems=[...defaultActionItems,...additionalActionItems],{window:globalWindow4}=globalThis,copyToClipboard=React20.useCallback(async text=>{let{createCopyToClipboardFunction}=await import('storybook/internal/components');createCopyToClipboardFunction();},[]),onCopyCapture=e3=>{let selection=globalWindow4.getSelection();selection&&selection.type==="Range"||(e3.preventDefault(),additionalActionItems.filter(item=>item.title==="Copied").length===0&&copyToClipboard(source?.props.code??"").then(()=>{setAdditionalActionItems([...additionalActionItems,{title:"Copied",onClick:()=>{}}]),globalWindow4.setTimeout(()=>setAdditionalActionItems(additionalActionItems.filter(item=>item.title!=="Copied")),1500);}));};return React20__namespace.default.createElement(PreviewContainer,{withSource,withToolbar,...props,className:previewClasses.join(" ")},withToolbar&&React20__namespace.default.createElement(PositionedToolbar,{isLoading,border:!0,zoom:z3=>setScale(scale*z3),resetZoom:()=>setScale(1),storyId:getStoryId(children),baseUrl:"./iframe.html"}),React20__namespace.default.createElement(ZoomContext.Provider,{value:{scale}},React20__namespace.default.createElement(Relative,{className:"docs-story",onCopyCapture:withSource&&onCopyCapture},React20__namespace.default.createElement(ChildrenContainer,{isColumn:isColumn||!Array.isArray(children),columns,layout,inline},React20__namespace.default.createElement(components.Zoom.Element,{centered:layout==="centered",scale:inline?scale:1},Array.isArray(children)?children.map((child,i3)=>React20__namespace.default.createElement("div",{key:i3},child)):React20__namespace.default.createElement("div",null,children))),React20__namespace.default.createElement(components.ActionBar,{actionItems}))),withSource&&expanded&&source)};theming.styled(Preview)(()=>({".docs-story":{paddingTop:32,paddingBottom:40}}));var TabbedArgsTable=({tabs,...props})=>{let entries=Object.entries(tabs);return entries.length===1?React20__namespace.default.createElement(ArgsTable,{...entries[0][1],...props}):React20__namespace.default.createElement(components.TabsState,null,entries.map((entry,index)=>{let[label,table]=entry,id2=`prop_table_div_${label}`,Component4="div",argsTableProps=index===0?props:{sort:props.sort};return React20__namespace.default.createElement(Component4,{key:id2,id:id2,title:label},({active})=>active?React20__namespace.default.createElement(ArgsTable,{key:`prop_table_${label}`,...table,...argsTableProps}):null)}))};var Label=theming.styled.div(({theme})=>({marginRight:30,fontSize:`${theme.typography.size.s1}px`,color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText)})),Sample=theming.styled.div({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),TypeSpecimen=theming.styled.div({display:"flex",flexDirection:"row",alignItems:"baseline","&:not(:last-child)":{marginBottom:"1rem"}}),Wrapper3=theming.styled.div(components.withReset,({theme})=>({...getBlockBackgroundStyle(theme),margin:"25px 0 40px",padding:"30px 20px"})),Typeset=({fontFamily,fontSizes,fontWeight,sampleText,...props})=>React20__namespace.default.createElement(Wrapper3,{...props,className:"docblock-typeset sb-unstyled"},fontSizes.map(size=>React20__namespace.default.createElement(TypeSpecimen,{key:size},React20__namespace.default.createElement(Label,null,size),React20__namespace.default.createElement(Sample,{style:{fontFamily,fontSize:size,fontWeight,lineHeight:1.2}},sampleText||"Was he a beast if music could move him so?"))));var ItemTitle=theming.styled.div(({theme})=>({fontWeight:theme.typography.weight.bold,color:theme.color.defaultText})),ItemSubtitle=theming.styled.div(({theme})=>({color:theme.base==="light"?curriedTransparentize$1(.2,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText)})),ItemDescription=theming.styled.div({flex:"0 0 30%",lineHeight:"20px",marginTop:5}),SwatchLabel=theming.styled.div(({theme})=>({flex:1,textAlign:"center",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,lineHeight:1,overflow:"hidden",color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText),"> div":{display:"inline-block",overflow:"hidden",maxWidth:"100%",textOverflow:"ellipsis"},span:{display:"block",marginTop:2}})),SwatchLabels=theming.styled.div({display:"flex",flexDirection:"row"}),Swatch=theming.styled.div(({background})=>({position:"relative",flex:1,"&::before":{position:"absolute",top:0,left:0,width:"100%",height:"100%",background,content:'""'}})),SwatchColors=theming.styled.div(({theme})=>({...getBlockBackgroundStyle(theme),display:"flex",flexDirection:"row",height:50,marginBottom:5,overflow:"hidden",backgroundColor:"white",backgroundImage:"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)",backgroundClip:"padding-box"})),SwatchSpecimen=theming.styled.div({display:"flex",flexDirection:"column",flex:1,position:"relative",marginBottom:30}),Swatches=theming.styled.div({flex:1,display:"flex",flexDirection:"row"}),Item=theming.styled.div({display:"flex",alignItems:"flex-start"}),ListName=theming.styled.div({flex:"0 0 30%"}),ListSwatches=theming.styled.div({flex:1}),ListHeading=theming.styled.div(({theme})=>({display:"flex",flexDirection:"row",alignItems:"center",paddingBottom:20,fontWeight:theme.typography.weight.bold,color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText)})),List=theming.styled.div(({theme})=>({fontSize:theme.typography.size.s2,lineHeight:"20px",display:"flex",flexDirection:"column"}));function renderSwatch(color,index){return React20__namespace.default.createElement(Swatch,{key:`${color}-${index}`,title:color,background:color})}function renderSwatchLabel(color,index,colorDescription){return React20__namespace.default.createElement(SwatchLabel,{key:`${color}-${index}`,title:color},React20__namespace.default.createElement("div",null,color,colorDescription&&React20__namespace.default.createElement("span",null,colorDescription)))}function renderSwatchSpecimen(colors){if(Array.isArray(colors))return React20__namespace.default.createElement(SwatchSpecimen,null,React20__namespace.default.createElement(SwatchColors,null,colors.map((color,index)=>renderSwatch(color,index))),React20__namespace.default.createElement(SwatchLabels,null,colors.map((color,index)=>renderSwatchLabel(color,index))));let swatchElements=[],labelElements=[];for(let colorKey in colors){let colorValue=colors[colorKey];swatchElements.push(renderSwatch(colorValue,swatchElements.length)),labelElements.push(renderSwatchLabel(colorKey,labelElements.length,colorValue));}return React20__namespace.default.createElement(SwatchSpecimen,null,React20__namespace.default.createElement(SwatchColors,null,swatchElements),React20__namespace.default.createElement(SwatchLabels,null,labelElements))}var ColorItem=({title,subtitle,colors})=>React20__namespace.default.createElement(Item,null,React20__namespace.default.createElement(ItemDescription,null,React20__namespace.default.createElement(ItemTitle,null,title),React20__namespace.default.createElement(ItemSubtitle,null,subtitle)),React20__namespace.default.createElement(Swatches,null,renderSwatchSpecimen(colors))),ColorPalette=({children,...props})=>React20__namespace.default.createElement(components.ResetWrapper,null,React20__namespace.default.createElement(List,{...props,className:"docblock-colorpalette sb-unstyled"},React20__namespace.default.createElement(ListHeading,null,React20__namespace.default.createElement(ListName,null,"Name"),React20__namespace.default.createElement(ListSwatches,null,"Swatches")),children));var ItemLabel=theming.styled.div(({theme})=>({fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s1,color:theme.color.defaultText,marginLeft:10,lineHeight:1.2,display:"-webkit-box",overflow:"hidden",wordBreak:"break-word",textOverflow:"ellipsis",WebkitLineClamp:2,WebkitBoxOrient:"vertical"})),ItemSpecimen=theming.styled.div(({theme})=>({...getBlockBackgroundStyle(theme),overflow:"hidden",height:40,width:40,display:"flex",alignItems:"center",justifyContent:"center",flex:"none","> img, > svg":{width:20,height:20}})),Item2=theming.styled.div({display:"inline-flex",flexDirection:"row",alignItems:"center",width:"100%"}),List2=theming.styled.div({display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(140px, 1fr))",gridGap:"8px 16px",gridAutoFlow:"row dense",gridAutoRows:50}),IconItem=({name,children})=>React20__namespace.default.createElement(Item2,null,React20__namespace.default.createElement(ItemSpecimen,null,children),React20__namespace.default.createElement(ItemLabel,null,name)),IconGallery=({children,...props})=>React20__namespace.default.createElement(components.ResetWrapper,null,React20__namespace.default.createElement(List2,{...props,className:"docblock-icongallery sb-unstyled"},children));function build_html_default(options){let forEach=[].forEach,some=[].some,body=typeof window<"u"&&document.body,SPACE_CHAR=" ",tocElement,currentlyHighlighting=!0,eventCount=0;function createEl(d3,container){let link=container.appendChild(createLink(d3));if(d3.children.length){let list=createList(d3.isCollapsed);d3.children.forEach(child=>{createEl(child,list);}),link.appendChild(list);}}function render(parent,data){let container=createList(!1);if(data.forEach(d3=>{createEl(d3,container);}),tocElement=parent||tocElement,tocElement!==null)return tocElement.firstChild&&tocElement.removeChild(tocElement.firstChild),data.length===0?tocElement:tocElement.appendChild(container)}function createLink(data){let item=document.createElement("li"),a3=document.createElement("a");return options.listItemClass&&item.setAttribute("class",options.listItemClass),options.onClick&&(a3.onclick=options.onClick),options.includeTitleTags&&a3.setAttribute("title",data.textContent),options.includeHtml&&data.childNodes.length?forEach.call(data.childNodes,node=>{a3.appendChild(node.cloneNode(!0));}):a3.textContent=data.textContent,a3.setAttribute("href",`${options.basePath}#${data.id}`),a3.setAttribute("class",`${options.linkClass+SPACE_CHAR}node-name--${data.nodeName}${SPACE_CHAR}${options.extraLinkClasses}`),item.appendChild(a3),item}function createList(isCollapsed){let listElement=options.orderedList?"ol":"ul",list=document.createElement(listElement),classes=options.listClass+SPACE_CHAR+options.extraListClasses;return isCollapsed&&(classes=classes+SPACE_CHAR+options.collapsibleClass,classes=classes+SPACE_CHAR+options.isCollapsedClass),list.setAttribute("class",classes),list}function updateFixedSidebarClass(){let scrollTop=getScrollTop(),posFixedEl=document.querySelector(options.positionFixedSelector);options.fixedSidebarOffset==="auto"&&(options.fixedSidebarOffset=tocElement.offsetTop),scrollTop>options.fixedSidebarOffset?posFixedEl.className.indexOf(options.positionFixedClass)===-1&&(posFixedEl.className+=SPACE_CHAR+options.positionFixedClass):posFixedEl.className=posFixedEl.className.replace(SPACE_CHAR+options.positionFixedClass,"");}function getHeadingTopPos(obj){let position=0;return obj!==null&&(position=obj.offsetTop,options.hasInnerContainers&&(position+=getHeadingTopPos(obj.offsetParent))),position}function updateClassname(obj,className){return obj&&obj.className!==className&&(obj.className=className),obj}function updateToc(headingsArray,event){options.positionFixedSelector&&updateFixedSidebarClass();let headings=headingsArray,clickedHref=event?.target?.getAttribute?event?.target?.getAttribute("href"):null,isBottomMode=clickedHref&&clickedHref.charAt(0)==="#"?getIsHeaderBottomMode(clickedHref.replace("#","")):!1,shouldUpdate=currentlyHighlighting||isBottomMode;if(event&&eventCount<5&&eventCount++,shouldUpdate&&tocElement&&headings.length>0){let topHeader=getTopHeader(headings),oldActiveTocLink=tocElement.querySelector(`.${options.activeLinkClass}`),topHeaderId=topHeader.id.replace(/([ #;&,.+*~':"!^$[\]()=>|/\\@])/g,"\\$1"),hashId=window.location.hash.replace("#",""),activeId=topHeaderId,isPageBottomMode=getIsPageBottomMode();clickedHref&&isBottomMode?activeId=clickedHref.replace("#",""):hashId&&hashId!==topHeaderId&&isPageBottomMode&&(getIsHeaderBottomMode(topHeaderId)||eventCount<=2)&&(activeId=hashId);let activeTocLink=tocElement.querySelector(`.${options.linkClass}[href="${options.basePath}#${activeId}"]`);if(oldActiveTocLink===activeTocLink)return;let tocLinks=tocElement.querySelectorAll(`.${options.linkClass}`);forEach.call(tocLinks,tocLink=>{updateClassname(tocLink,tocLink.className.replace(SPACE_CHAR+options.activeLinkClass,""));});let tocLis=tocElement.querySelectorAll(`.${options.listItemClass}`);forEach.call(tocLis,tocLi=>{updateClassname(tocLi,tocLi.className.replace(SPACE_CHAR+options.activeListItemClass,""));}),activeTocLink&&activeTocLink.className.indexOf(options.activeLinkClass)===-1&&(activeTocLink.className+=SPACE_CHAR+options.activeLinkClass);let li=activeTocLink?.parentNode;li&&li.className.indexOf(options.activeListItemClass)===-1&&(li.className+=SPACE_CHAR+options.activeListItemClass);let tocLists=tocElement.querySelectorAll(`.${options.listClass}.${options.collapsibleClass}`);forEach.call(tocLists,list=>{list.className.indexOf(options.isCollapsedClass)===-1&&(list.className+=SPACE_CHAR+options.isCollapsedClass);}),activeTocLink?.nextSibling&&activeTocLink.nextSibling.className.indexOf(options.isCollapsedClass)!==-1&&updateClassname(activeTocLink.nextSibling,activeTocLink.nextSibling.className.replace(SPACE_CHAR+options.isCollapsedClass,"")),removeCollapsedFromParents(activeTocLink?.parentNode.parentNode);}}function removeCollapsedFromParents(element){return element&&element.className.indexOf(options.collapsibleClass)!==-1&&element.className.indexOf(options.isCollapsedClass)!==-1?(updateClassname(element,element.className.replace(SPACE_CHAR+options.isCollapsedClass,"")),removeCollapsedFromParents(element.parentNode.parentNode)):element}function disableTocAnimation(event){let target=event.target||event.srcElement;typeof target.className!="string"||target.className.indexOf(options.linkClass)===-1||(currentlyHighlighting=!1);}function enableTocAnimation(){currentlyHighlighting=!0;}function getCurrentlyHighlighting(){return currentlyHighlighting}function getIsHeaderBottomMode(headerId){let scrollEl=getScrollEl();return (document?.getElementById(headerId)).offsetTop>scrollEl.offsetHeight-scrollEl.clientHeight*1.4-options.bottomModeThreshold}function getIsPageBottomMode(){let scrollEl=getScrollEl(),isScrollable=scrollEl.scrollHeight>scrollEl.clientHeight,isBottomMode=getScrollTop()+scrollEl.clientHeight>scrollEl.offsetHeight-options.bottomModeThreshold;return isScrollable&&isBottomMode}function getScrollEl(){let el;return options.scrollContainer&&document.querySelector(options.scrollContainer)?el=document.querySelector(options.scrollContainer):el=document.documentElement||body,el}function getScrollTop(){return getScrollEl()?.scrollTop||0}function getTopHeader(headings,scrollTop=getScrollTop()){let topHeader;return some.call(headings,(heading,i3)=>{if(getHeadingTopPos(heading)>scrollTop+options.headingsOffset+10){let index=i3===0?i3:i3-1;return topHeader=headings[index],!0}if(i3===headings.length-1)return topHeader=headings[headings.length-1],!0}),topHeader}function updateUrlHashForHeader(headingsArray){let scrollTop=getScrollTop(),topHeader=getTopHeader(headingsArray,scrollTop),isPageBottomMode=getIsPageBottomMode();if((!topHeader||scrollTop<5)&&!isPageBottomMode)window.location.hash==="#"||window.location.hash===""||window.history.pushState(null,null,"#");else if(topHeader&&!isPageBottomMode){let newHash=`#${topHeader.id}`;window.location.hash!==newHash&&window.history.pushState(null,null,newHash);}}return {enableTocAnimation,disableTocAnimation,render,updateToc,getCurrentlyHighlighting,getTopHeader,getScrollTop,updateUrlHashForHeader}}var default_options_default={tocSelector:".js-toc",tocElement:null,contentSelector:".js-toc-content",contentElement:null,headingSelector:"h1, h2, h3",ignoreSelector:".js-toc-ignore",hasInnerContainers:!1,linkClass:"toc-link",extraLinkClasses:"",activeLinkClass:"is-active-link",listClass:"toc-list",extraListClasses:"",isCollapsedClass:"is-collapsed",collapsibleClass:"is-collapsible",listItemClass:"toc-list-item",activeListItemClass:"is-active-li",collapseDepth:0,scrollSmooth:!0,scrollSmoothDuration:420,scrollSmoothOffset:0,scrollEndCallback:function(e3){},headingsOffset:1,enableUrlHashUpdateOnScroll:!1,scrollHandlerType:"auto",scrollHandlerTimeout:50,throttleTimeout:50,positionFixedSelector:null,positionFixedClass:"is-position-fixed",fixedSidebarOffset:"auto",includeHtml:!1,includeTitleTags:!1,onClick:function(e3){},orderedList:!0,scrollContainer:null,skipRendering:!1,headingLabelCallback:!1,ignoreHiddenElements:!1,headingObjectCallback:null,basePath:"",disableTocScrollSync:!1,tocScrollingWrapper:null,tocScrollOffset:30,bottomModeThreshold:30};function parseContent(options){let reduce=[].reduce;function getLastItem(array2){return array2[array2.length-1]}function getHeadingLevel(heading){return +heading.nodeName.toUpperCase().replace("H","")}function isHTMLElement(maybeElement){try{return maybeElement instanceof window.HTMLElement||maybeElement instanceof window.parent.HTMLElement}catch{return maybeElement instanceof window.HTMLElement}}function getHeadingObject(heading){if(!isHTMLElement(heading))return heading;if(options.ignoreHiddenElements&&(!heading.offsetHeight||!heading.offsetParent))return null;let headingLabel=heading.getAttribute("data-heading-label")||(options.headingLabelCallback?String(options.headingLabelCallback(heading.innerText)):(heading.innerText||heading.textContent).trim()),obj={id:heading.id,children:[],nodeName:heading.nodeName,headingLevel:getHeadingLevel(heading),textContent:headingLabel};return options.includeHtml&&(obj.childNodes=heading.childNodes),options.headingObjectCallback?options.headingObjectCallback(obj,heading):obj}function addNode(node,nest){let obj=getHeadingObject(node),level=obj.headingLevel,array2=nest,lastItem=getLastItem(array2),lastItemLevel=lastItem?lastItem.headingLevel:0,counter=level-lastItemLevel;for(;counter>0&&(lastItem=getLastItem(array2),!(lastItem&&level===lastItem.headingLevel));)lastItem&&lastItem.children!==void 0&&(array2=lastItem.children),counter--;return level>=options.collapseDepth&&(obj.isCollapsed=!0),array2.push(obj),array2}function selectHeadings(contentElement,headingSelector){let selectors=headingSelector;options.ignoreSelector&&(selectors=headingSelector.split(",").map(function(selector){return `${selector.trim()}:not(${options.ignoreSelector})`}));try{return contentElement.querySelectorAll(selectors)}catch{return console.warn(`Headers not found with selector: ${selectors}`),null}}function nestHeadingsArray(headingsArray){return reduce.call(headingsArray,function(prev,curr){let currentHeading=getHeadingObject(curr);return currentHeading&&addNode(currentHeading,prev.nest),prev},{nest:[]})}return {nestHeadingsArray,selectHeadings}}function initSmoothScrolling(options){var duration=options.duration,offset=options.offset;if(typeof window>"u"||typeof location>"u")return;var pageUrl=location.hash?stripHash(location.href):location.href;delegatedLinkHijacking();function delegatedLinkHijacking(){document.body.addEventListener("click",onClick,!1);function onClick(e3){!isInPageLink(e3.target)||e3.target.className.indexOf("no-smooth-scroll")>-1||e3.target.href.charAt(e3.target.href.length-2)==="#"&&e3.target.href.charAt(e3.target.href.length-1)==="!"||e3.target.className.indexOf(options.linkClass)===-1||jump(e3.target.hash,{duration,offset,callback:function(){setFocus(e3.target.hash);}});}}function isInPageLink(n3){return n3.tagName.toLowerCase()==="a"&&(n3.hash.length>0||n3.href.charAt(n3.href.length-1)==="#")&&(stripHash(n3.href)===pageUrl||stripHash(n3.href)+"#"===pageUrl)}function stripHash(url){return url.slice(0,url.lastIndexOf("#"))}function setFocus(hash){var element=document.getElementById(hash.substring(1));element&&(/^(?:a|select|input|button|textarea)$/i.test(element.tagName)||(element.tabIndex=-1),element.focus());}}function jump(target,options){var start=window.pageYOffset,opt={duration:options.duration,offset:options.offset||0,callback:options.callback,easing:options.easing||easeInOutQuad},tgt=document.querySelector('[id="'+decodeURI(target).split("#").join("")+'"]')||document.querySelector('[id="'+target.split("#").join("")+'"]'),distance=typeof target=="string"?opt.offset+(target?tgt&&tgt.getBoundingClientRect().top||0:-(document.documentElement.scrollTop||document.body.scrollTop)):target,duration=typeof opt.duration=="function"?opt.duration(distance):opt.duration,timeStart,timeElapsed;requestAnimationFrame(function(time){timeStart=time,loop(time);});function loop(time){timeElapsed=time-timeStart,window.scrollTo(0,opt.easing(timeElapsed,start,distance,duration)),timeElapsed<duration?requestAnimationFrame(loop):end();}function end(){window.scrollTo(0,start+distance),typeof opt.callback=="function"&&opt.callback();}function easeInOutQuad(t3,b3,c3,d3){return t3/=d3/2,t3<1?c3/2*t3*t3+b3:(t3--,-c3/2*(t3*(t3-2)-1)+b3)}}function updateTocScroll(options){let toc=options.tocScrollingWrapper||options.tocElement||document.querySelector(options.tocSelector);if(toc&&toc.scrollHeight>toc.clientHeight){let activeItem=toc.querySelector(`.${options.activeListItemClass}`);if(activeItem){let scrollAmount=activeItem.offsetTop-options.tocScrollOffset;toc.scrollTop=scrollAmount>0?scrollAmount:0;}}}var _options={},_buildHtml,_parseContent,_headingsArray,_scrollListener,clickListener;function init(customOptions){let hasInitialized=!1;_options=extend(default_options_default,customOptions||{}),_options.scrollSmooth&&(_options.duration=_options.scrollSmoothDuration,_options.offset=_options.scrollSmoothOffset,initSmoothScrolling(_options)),_buildHtml=build_html_default(_options),_parseContent=parseContent(_options),destroy();let contentElement=getContentElement(_options);if(contentElement===null)return;let tocElement=getTocElement(_options);if(tocElement===null||(_headingsArray=_parseContent.selectHeadings(contentElement,_options.headingSelector),_headingsArray===null))return;let nestedHeadings=_parseContent.nestHeadingsArray(_headingsArray).nest;if(!_options.skipRendering)_buildHtml.render(tocElement,nestedHeadings);else return this;let isClick=!1,scrollHandlerTimeout=_options.scrollHandlerTimeout||_options.throttleTimeout;_scrollListener=((fn,delay)=>getScrollHandler(fn,delay,_options.scrollHandlerType))(e3=>{_buildHtml.updateToc(_headingsArray,e3),!_options.disableTocScrollSync&&!isClick&&updateTocScroll(_options),_options.enableUrlHashUpdateOnScroll&&hasInitialized&&_buildHtml.getCurrentlyHighlighting()&&_buildHtml.updateUrlHashForHeader(_headingsArray);let isTop=e3?.target?.scrollingElement?.scrollTop===0;(e3&&(e3.eventPhase===0||e3.currentTarget===null)||isTop)&&(_buildHtml.updateToc(_headingsArray),_options.scrollEndCallback?.(e3));},scrollHandlerTimeout),hasInitialized||(_scrollListener(),hasInitialized=!0),window.onhashchange=window.onscrollend=e3=>{_scrollListener(e3);},_options.scrollContainer&&document.querySelector(_options.scrollContainer)?(document.querySelector(_options.scrollContainer).addEventListener("scroll",_scrollListener,!1),document.querySelector(_options.scrollContainer).addEventListener("resize",_scrollListener,!1)):(document.addEventListener("scroll",_scrollListener,!1),document.addEventListener("resize",_scrollListener,!1));let timeout=null;clickListener=throttle(event=>{isClick=!0,_options.scrollSmooth&&_buildHtml.disableTocAnimation(event),_buildHtml.updateToc(_headingsArray,event),timeout&&clearTimeout(timeout),timeout=setTimeout(()=>{_buildHtml.enableTocAnimation();},_options.scrollSmoothDuration),setTimeout(()=>{isClick=!1;},_options.scrollSmoothDuration+100);},_options.throttleTimeout),_options.scrollContainer&&document.querySelector(_options.scrollContainer)?document.querySelector(_options.scrollContainer).addEventListener("click",clickListener,!1):document.addEventListener("click",clickListener,!1);}function destroy(){let tocElement=getTocElement(_options);tocElement!==null&&(_options.skipRendering||tocElement&&(tocElement.innerHTML=""),_options.scrollContainer&&document.querySelector(_options.scrollContainer)?(document.querySelector(_options.scrollContainer).removeEventListener("scroll",_scrollListener,!1),document.querySelector(_options.scrollContainer).removeEventListener("resize",_scrollListener,!1),_buildHtml&&document.querySelector(_options.scrollContainer).removeEventListener("click",clickListener,!1)):(document.removeEventListener("scroll",_scrollListener,!1),document.removeEventListener("resize",_scrollListener,!1),_buildHtml&&document.removeEventListener("click",clickListener,!1)));}function refresh(customOptions){destroy(),init(customOptions||_options);}var hasOwnProp=Object.prototype.hasOwnProperty;function extend(...args){let target={};for(let i3=0;i3<args.length;i3++){let source=args[i3];for(let key in source)hasOwnProp.call(source,key)&&(target[key]=source[key]);}return target}function throttle(fn,threshold,scope){threshold||(threshold=250);let last,deferTimer;return function(...args){let context=scope||this,now=+new Date;last&&now<last+threshold?(clearTimeout(deferTimer),deferTimer=setTimeout(()=>{last=now,fn.apply(context,args);},threshold)):(last=now,fn.apply(context,args));}}function debounce3(func,wait){let timeout;return (...args)=>{clearTimeout(timeout),timeout=setTimeout(()=>func.apply(this,args),wait);}}function getScrollHandler(func,timeout,type="auto"){switch(type){case"debounce":return debounce3(func,timeout);case"throttle":return throttle(func,timeout);default:return timeout<334?debounce3(func,timeout):throttle(func,timeout)}}function getContentElement(options){try{return options.contentElement||document.querySelector(options.contentSelector)}catch{return console.warn(`Contents element not found: ${options.contentSelector}`),null}}function getTocElement(options){try{return options.tocElement||document.querySelector(options.tocSelector)}catch{return console.warn(`TOC element not found: ${options.tocSelector}`),null}}var tocbot={destroy,init,refresh};var tocbot_default=tocbot;var Aside=theming.styled.aside(()=>({width:"10rem","@media (max-width: 768px)":{display:"none"}})),Nav=theming.styled.nav(({theme})=>({position:"fixed",bottom:0,top:0,width:"10rem",paddingTop:"4rem",paddingBottom:"2rem",overflowY:"auto",fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch","& *":{boxSizing:"border-box"},"& > .toc-wrapper > .toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`}}},"& .toc-list-item":{position:"relative",listStyleType:"none",marginLeft:20,paddingTop:3,paddingBottom:3},"& .toc-list-item::before":{content:'""',position:"absolute",height:"100%",top:0,left:0,transform:"translateX(calc(-2px - 20px))",borderLeft:`solid 2px ${theme.color.mediumdark}`,opacity:0,transition:"opacity 0.2s"},"& .toc-list-item.is-active-li::before":{opacity:1},"& .toc-list-item > a":{color:theme.color.defaultText,textDecoration:"none"},"& .toc-list-item.is-active-li > a":{fontWeight:600,color:theme.color.secondary,textDecoration:"none"}})),Heading=theming.styled.p(({theme})=>({fontWeight:600,fontSize:"0.875em",color:theme.textColor,textTransform:"uppercase",marginBottom:10})),Title2=({headingId,title})=>typeof title=="string"||!title?React20__namespace.default.createElement(Heading,{as:"h2",id:headingId,className:title?"":"sb-sr-only"},title||"Table of contents"):React20__namespace.default.createElement("div",{id:headingId},title),TableOfContents=({title,disable,headingSelector,contentsSelector,ignoreSelector,unsafeTocbotOptions,channel,className})=>{React20.useEffect(()=>{if(disable)return ()=>{};let configuration={tocSelector:".toc-wrapper",contentSelector:contentsSelector??".sbdocs-content",headingSelector:headingSelector??"h3",ignoreSelector:ignoreSelector??".docs-story *, .skip-toc",headingsOffset:40,scrollSmoothOffset:-40,orderedList:!1,onClick:e3=>{if(e3.preventDefault(),e3.currentTarget instanceof HTMLAnchorElement){let[,headerId]=e3.currentTarget.href.split("#");headerId&&channel.emit(coreEvents.NAVIGATE_URL,`#${headerId}`);}},...unsafeTocbotOptions},timeout=setTimeout(()=>tocbot_default.init(configuration),100);return ()=>{clearTimeout(timeout),tocbot_default.destroy();}},[channel,disable,ignoreSelector,contentsSelector,headingSelector,unsafeTocbotOptions]);let headingId=React20.useId();return React20__namespace.default.createElement(Aside,{className},disable?null:React20__namespace.default.createElement(Nav,{"aria-labelledby":headingId},React20__namespace.default.createElement(Title2,{headingId,title}),React20__namespace.default.createElement("div",{className:"toc-wrapper"})))};function t(){return t=Object.assign?Object.assign.bind():function(e3){for(var t3=1;t3<arguments.length;t3++){var n3=arguments[t3];for(var r3 in n3)Object.prototype.hasOwnProperty.call(n3,r3)&&(e3[r3]=n3[r3]);}return e3},t.apply(this,arguments)}var n=["children","options"],r={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},i;(function(e3){e3[e3.MAX=0]="MAX",e3[e3.HIGH=1]="HIGH",e3[e3.MED=2]="MED",e3[e3.LOW=3]="LOW",e3[e3.MIN=4]="MIN";})(i||(i={}));var l=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e3,t3)=>(e3[t3.toLowerCase()]=t3,e3),{class:"className",for:"htmlFor"}),o={amp:"&",apos:"'",gt:">",lt:"<",nbsp:"\xA0",quot:"\u201C"},a=["style","script"],c=["src","href","data","formAction","srcDoc","action"],s=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,d=/mailto:/i,u=/\n{2,}$/,p=/^(\s*>[\s\S]*?)(?=\n\n|$)/,f=/^ *> ?/gm,h=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,m=/^ {2,}\n/,g=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,y=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,k=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,x=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,b=/^(?:\n *)*\n/,v=/\r\n?/g,C=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,$=/^\[\^([^\]]+)]/,S=/\f/g,w=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,E=/^\s*?\[(x|\s)\]/,z=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,L=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,O=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,T=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,B=/^<!--[\s\S]*?(?:-->)/,M=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,R=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,I=/^\{.*\}$/,D=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,U=/^<([^ >]+@[^ >]+)>/,N=/^<([^ >]+:\/[^ >]+)>/,j=/-([a-z])?/gi,H=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,P=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,_=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,F=/^\[([^\]]*)\] ?\[([^\]]*)\]/,W=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,G=/\t/g,Z=/(^ *\||\| *$)/g,q=/^ *:-+: *$/,Q=/^ *:-+ *$/,V=/^ *-+: *$/,X="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",J=new RegExp(`^([*_])\\1${X}\\1\\1(?!\\1)`),K=new RegExp(`^([*_])${X}\\1(?!\\1)`),Y=new RegExp(`^(==)${X}\\1`),ee=new RegExp(`^(~~)${X}\\1`),te=/^\\([^0-9A-Za-z\s])/,ne=/\\([^0-9A-Za-z\s])/g,re=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,ie=/^\n+/,le=/^([ \t]*)/,oe=/\\([^\\])/g,ae=/(?:^|\n)( *)$/,ce="(?:\\d+\\.)",se="(?:[*+-])";function de(e3){return "( *)("+(e3===1?ce:se)+") +"}var ue=de(1),pe=de(2);function fe(e3){return new RegExp("^"+(e3===1?ue:pe))}var he=fe(1),me=fe(2);function ge(e3){return new RegExp("^"+(e3===1?ue:pe)+"[^\\n]*(?:\\n(?!\\1"+(e3===1?ce:se)+" )[^\\n]*)*(\\n|$)","gm")}var ye=ge(1),ke=ge(2);function xe(e3){let t3=e3===1?ce:se;return new RegExp("^( *)("+t3+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t3+" (?!"+t3+" ))\\n*|\\s*\\n*$)")}var be=xe(1),ve=xe(2);function Ce(e3,t3){let n3=t3===1,i3=n3?be:ve,l3=n3?ye:ke,o3=n3?he:me;return {match:Me(function(e4,t4){let n4=ae.exec(t4.prevCapture);return n4&&(t4.list||!t4.inline&&!t4.simple)?i3.exec(e4=n4[1]+e4):null}),order:1,parse(e4,t4,r3){let i4=n3?+e4[2]:void 0,a3=e4[0].replace(u,`
`).match(l3),c3=!1;return {items:a3.map(function(e5,n4){let i5=o3.exec(e5)[0].length,l4=new RegExp("^ {1,"+i5+"}","gm"),s3=e5.replace(l4,"").replace(o3,""),d3=n4===a3.length-1,u3=s3.indexOf(`

`)!==-1||d3&&c3;c3=u3;let p3=r3.inline,f3=r3.list,h3;r3.list=!0,u3?(r3.inline=!1,h3=ze(s3)+`

`):(r3.inline=!0,h3=ze(s3));let m3=t4(h3,r3);return r3.inline=p3,r3.list=f3,m3}),ordered:n3,start:i4}},render:(t4,n4,i4)=>e3(t4.ordered?"ol":"ul",{key:i4.key,start:t4.type===r.orderedList?t4.start:void 0},t4.items.map(function(t5,r3){return e3("li",{key:r3},n4(t5,i4))}))}}var $e=new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`),Se=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,we=[p,y,k,z,A,L,H,be,ve],Ee=[...we,/^[^\n]+(?:  \n|\n{2,})/,O,B,R];function ze(e3){let t3=e3.length;for(;t3>0&&e3[t3-1]<=" ";)t3--;return e3.slice(0,t3)}function Le(e3){return e3.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Ae(e3){return V.test(e3)?"right":q.test(e3)?"center":Q.test(e3)?"left":null}function Oe(e3,t3,n3,r3){let i3=n3.inTable;n3.inTable=!0;let l3=[[]],o3="";function a3(){if(!o3)return;let e4=l3[l3.length-1];e4.push.apply(e4,t3(o3,n3)),o3="";}return e3.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((e4,t4,n4)=>{e4.trim()==="|"&&(a3(),r3)?t4!==0&&t4!==n4.length-1&&l3.push([]):o3+=e4;}),a3(),n3.inTable=i3,l3}function Te(e3,t3,n3){n3.inline=!0;let i3=e3[2]?e3[2].replace(Z,"").split("|").map(Ae):[],l3=e3[3]?function(e4,t4,n4){return e4.trim().split(`
`).map(function(e5){return Oe(e5,t4,n4,!0)})}(e3[3],t3,n3):[],o3=Oe(e3[1],t3,n3,!!l3.length);return n3.inline=!1,l3.length?{align:i3,cells:l3,header:o3,type:r.table}:{children:o3,type:r.paragraph}}function Be(e3,t3){return e3.align[t3]==null?{}:{textAlign:e3.align[t3]}}function Me(e3){return e3.inline=1,e3}function Re(e3){return Me(function(t3,n3){return n3.inline?e3.exec(t3):null})}function Ie(e3){return Me(function(t3,n3){return n3.inline||n3.simple?e3.exec(t3):null})}function De(e3){return function(t3,n3){return n3.inline||n3.simple?null:e3.exec(t3)}}function Ue(e3){return Me(function(t3){return e3.exec(t3)})}function Ne(e3,t3){if(t3.inline||t3.simple)return null;let n3="";e3.split(`
`).every(e4=>(e4+=`
`,!we.some(t4=>t4.test(e4))&&(n3+=e4,!!e4.trim())));let r3=ze(n3);return r3==""?null:[n3,,r3]}var je=/(javascript|vbscript|data(?!:image)):/i;function He(e3){try{let t3=decodeURIComponent(e3).replace(/[^A-Za-z0-9/:]/g,"");if(je.test(t3))return null}catch{return null}return e3}function Pe(e3){return e3.replace(oe,"$1")}function _e(e3,t3,n3){let r3=n3.inline||!1,i3=n3.simple||!1;n3.inline=!0,n3.simple=!0;let l3=e3(t3,n3);return n3.inline=r3,n3.simple=i3,l3}function Fe(e3,t3,n3){let r3=n3.inline||!1,i3=n3.simple||!1;n3.inline=!1,n3.simple=!0;let l3=e3(t3,n3);return n3.inline=r3,n3.simple=i3,l3}function We(e3,t3,n3){let r3=n3.inline||!1;n3.inline=!1;let i3=e3(t3,n3);return n3.inline=r3,i3}var Ge=(e3,t3,n3)=>({children:_e(t3,e3[2],n3)});function Ze(){return {}}function qe(){return null}function Qe(...e3){return e3.filter(Boolean).join(" ")}function Ve(e3,t3,n3){let r3=e3,i3=t3.split(".");for(;i3.length&&(r3=r3[i3[0]],r3!==void 0);)i3.shift();return r3||n3}function Xe(n3="",i3={}){function u3(e3,n4,...r3){let l3=Ve(i3.overrides,`${e3}.props`,{});return i3.createElement(function(e4,t3){let n5=Ve(t3,e4);return n5?typeof n5=="function"||typeof n5=="object"&&"render"in n5?n5:Ve(t3,`${e4}.component`,e4):e4}(e3,i3.overrides),t({},n4,l3,{className:Qe(n4?.className,l3.className)||void 0}),...r3)}function Z3(e3){e3=e3.replace(w,"");let t3=!1;i3.forceInline?t3=!0:i3.forceBlock||(t3=W.test(e3)===!1);let n4=ae2(oe2(t3?e3:`${ze(e3).replace(ie,"")}

`,{inline:t3}));for(;typeof n4[n4.length-1]=="string"&&!n4[n4.length-1].trim();)n4.pop();if(i3.wrapper===null)return n4;let r3=i3.wrapper||(t3?"span":"div"),l3;if(n4.length>1||i3.forceWrapper)l3=n4;else {if(n4.length===1)return l3=n4[0],typeof l3=="string"?u3("span",{key:"outer"},l3):l3;l3=null;}return i3.createElement(r3,{key:"outer"},l3)}function q3(e3,t3){let n4=t3.match(s);return n4?n4.reduce(function(t4,n5){let r3=n5.indexOf("=");if(r3!==-1){let o3=function(e4){return e4.indexOf("-")!==-1&&e4.match(M)===null&&(e4=e4.replace(j,function(e5,t5){return t5.toUpperCase()})),e4}(n5.slice(0,r3)).trim(),a3=function(e4){let t5=e4[0];return (t5==='"'||t5==="'")&&e4.length>=2&&e4[e4.length-1]===t5?e4.slice(1,-1):e4}(n5.slice(r3+1).trim()),s3=l[o3]||o3;if(s3==="ref")return t4;let d3=t4[s3]=function(e4,t5,n6,r4){return t5==="style"?function(e5){let t6=[],n7="",r5=!1,i4=!1,l3="";if(!e5)return t6;for(let o5=0;o5<e5.length;o5++){let a4=e5[o5];if(a4!=='"'&&a4!=="'"||r5||(i4?a4===l3&&(i4=!1,l3=""):(i4=!0,l3=a4)),a4==="("&&n7.endsWith("url")?r5=!0:a4===")"&&r5&&(r5=!1),a4!==";"||i4||r5)n7+=a4;else {let e6=n7.trim();if(e6){let n8=e6.indexOf(":");if(n8>0){let r6=e6.slice(0,n8).trim(),i5=e6.slice(n8+1).trim();t6.push([r6,i5]);}}n7="";}}let o4=n7.trim();if(o4){let e6=o4.indexOf(":");if(e6>0){let n8=o4.slice(0,e6).trim(),r6=o4.slice(e6+1).trim();t6.push([n8,r6]);}}return t6}(n6).reduce(function(t6,[n7,i4]){return t6[n7.replace(/(-[a-z])/g,e5=>e5[1].toUpperCase())]=r4(i4,e4,n7),t6},{}):c.indexOf(t5)!==-1?r4(n6,e4,t5):(n6.match(I)&&(n6=n6.slice(1,n6.length-1)),n6==="true"||n6!=="false"&&n6)}(e3,o3,a3,i3.sanitizer);typeof d3=="string"&&(O.test(d3)||R.test(d3))&&(t4[s3]=Z3(d3.trim()));}else n5!=="style"&&(t4[l[n5]||n5]=!0);return t4},{}):null}i3.overrides=i3.overrides||{},i3.sanitizer=i3.sanitizer||He,i3.slugify=i3.slugify||Le,i3.namedCodesToUnicode=i3.namedCodesToUnicode?t({},o,i3.namedCodesToUnicode):o,i3.createElement=i3.createElement||React20__namespace.createElement;let Q3=[],V3={},X3={[r.blockQuote]:{match:De(p),order:1,parse(e3,t3,n4){let[,r3,i4]=e3[0].replace(f,"").match(h);return {alert:r3,children:t3(i4,n4)}},render(e3,t3,n4){let l3={key:n4.key};return e3.alert&&(l3.className="markdown-alert-"+i3.slugify(e3.alert.toLowerCase(),Le),e3.children.unshift({attrs:{},children:[{type:r.text,text:e3.alert}],noInnerParse:!0,type:r.htmlBlock,tag:"header"})),u3("blockquote",l3,t3(e3.children,n4))}},[r.breakLine]:{match:Ue(m),order:1,parse:Ze,render:(e3,t3,n4)=>u3("br",{key:n4.key})},[r.breakThematic]:{match:De(g),order:1,parse:Ze,render:(e3,t3,n4)=>u3("hr",{key:n4.key})},[r.codeBlock]:{match:De(k),order:0,parse:e3=>({lang:void 0,text:ze(e3[0].replace(/^ {4}/gm,"")).replace(ne,"$1")}),render:(e3,n4,r3)=>u3("pre",{key:r3.key},u3("code",t({},e3.attrs,{className:e3.lang?`lang-${e3.lang}`:""}),e3.text))},[r.codeFenced]:{match:De(y),order:0,parse:e3=>({attrs:q3("code",e3[3]||""),lang:e3[2]||void 0,text:e3[4],type:r.codeBlock})},[r.codeInline]:{match:Ie(x),order:3,parse:e3=>({text:e3[2].replace(ne,"$1")}),render:(e3,t3,n4)=>u3("code",{key:n4.key},e3.text)},[r.footnote]:{match:De(C),order:0,parse:e3=>(Q3.push({footnote:e3[2],identifier:e3[1]}),{}),render:qe},[r.footnoteReference]:{match:Re($),order:1,parse:e3=>({target:`#${i3.slugify(e3[1],Le)}`,text:e3[1]}),render:(e3,t3,n4)=>u3("a",{key:n4.key,href:i3.sanitizer(e3.target,"a","href")},u3("sup",{key:n4.key},e3.text))},[r.gfmTask]:{match:Re(E),order:1,parse:e3=>({completed:e3[1].toLowerCase()==="x"}),render:(e3,t3,n4)=>u3("input",{checked:e3.completed,key:n4.key,readOnly:!0,type:"checkbox"})},[r.heading]:{match:De(i3.enforceAtxHeadings?L:z),order:1,parse:(e3,t3,n4)=>({children:_e(t3,e3[2],n4),id:i3.slugify(e3[2],Le),level:e3[1].length}),render:(e3,t3,n4)=>u3(`h${e3.level}`,{id:e3.id,key:n4.key},t3(e3.children,n4))},[r.headingSetext]:{match:De(A),order:0,parse:(e3,t3,n4)=>({children:_e(t3,e3[1],n4),level:e3[2]==="="?1:2,type:r.heading})},[r.htmlBlock]:{match:Ue(O),order:1,parse(e3,t3,n4){let[,r3]=e3[3].match(le),i4=new RegExp(`^${r3}`,"gm"),l3=e3[3].replace(i4,""),o3=(c3=l3,Ee.some(e4=>e4.test(c3))?We:_e);var c3;let s3=e3[1].toLowerCase(),d3=a.indexOf(s3)!==-1,u4=(d3?s3:e3[1]).trim(),p3={attrs:q3(u4,e3[2]),noInnerParse:d3,tag:u4};return n4.inAnchor=n4.inAnchor||s3==="a",d3?p3.text=e3[3]:p3.children=o3(t3,l3,n4),n4.inAnchor=!1,p3},render:(e3,n4,r3)=>u3(e3.tag,t({key:r3.key},e3.attrs),e3.text||(e3.children?n4(e3.children,r3):""))},[r.htmlSelfClosing]:{match:Ue(R),order:1,parse(e3){let t3=e3[1].trim();return {attrs:q3(t3,e3[2]||""),tag:t3}},render:(e3,n4,r3)=>u3(e3.tag,t({},e3.attrs,{key:r3.key}))},[r.htmlComment]:{match:Ue(B),order:1,parse:()=>({}),render:qe},[r.image]:{match:Ie(Se),order:1,parse:e3=>({alt:e3[1],target:Pe(e3[2]),title:e3[3]}),render:(e3,t3,n4)=>u3("img",{key:n4.key,alt:e3.alt||void 0,title:e3.title||void 0,src:i3.sanitizer(e3.target,"img","src")})},[r.link]:{match:Re($e),order:3,parse:(e3,t3,n4)=>({children:Fe(t3,e3[1],n4),target:Pe(e3[2]),title:e3[3]}),render:(e3,t3,n4)=>u3("a",{key:n4.key,href:i3.sanitizer(e3.target,"a","href"),title:e3.title},t3(e3.children,n4))},[r.linkAngleBraceStyleDetector]:{match:Re(N),order:0,parse:e3=>({children:[{text:e3[1],type:r.text}],target:e3[1],type:r.link})},[r.linkBareUrlDetector]:{match:Me((e3,t3)=>t3.inAnchor||i3.disableAutoLink?null:Re(D)(e3,t3)),order:0,parse:e3=>({children:[{text:e3[1],type:r.text}],target:e3[1],title:void 0,type:r.link})},[r.linkMailtoDetector]:{match:Re(U),order:0,parse(e3){let t3=e3[1],n4=e3[1];return d.test(n4)||(n4="mailto:"+n4),{children:[{text:t3.replace("mailto:",""),type:r.text}],target:n4,type:r.link}}},[r.orderedList]:Ce(u3,1),[r.unorderedList]:Ce(u3,2),[r.newlineCoalescer]:{match:De(b),order:3,parse:Ze,render:()=>`
`},[r.paragraph]:{match:Me(Ne),order:3,parse:Ge,render:(e3,t3,n4)=>u3("p",{key:n4.key},t3(e3.children,n4))},[r.ref]:{match:Re(P),order:0,parse:e3=>(V3[e3[1]]={target:e3[2],title:e3[4]},{}),render:qe},[r.refImage]:{match:Ie(_),order:0,parse:e3=>({alt:e3[1]||void 0,ref:e3[2]}),render:(e3,t3,n4)=>V3[e3.ref]?u3("img",{key:n4.key,alt:e3.alt,src:i3.sanitizer(V3[e3.ref].target,"img","src"),title:V3[e3.ref].title}):null},[r.refLink]:{match:Re(F),order:0,parse:(e3,t3,n4)=>({children:t3(e3[1],n4),fallbackChildren:e3[0],ref:e3[2]}),render:(e3,t3,n4)=>V3[e3.ref]?u3("a",{key:n4.key,href:i3.sanitizer(V3[e3.ref].target,"a","href"),title:V3[e3.ref].title},t3(e3.children,n4)):u3("span",{key:n4.key},e3.fallbackChildren)},[r.table]:{match:De(H),order:1,parse:Te,render(e3,t3,n4){let r3=e3;return u3("table",{key:n4.key},u3("thead",null,u3("tr",null,r3.header.map(function(e4,i4){return u3("th",{key:i4,style:Be(r3,i4)},t3(e4,n4))}))),u3("tbody",null,r3.cells.map(function(e4,i4){return u3("tr",{key:i4},e4.map(function(e5,i5){return u3("td",{key:i5,style:Be(r3,i5)},t3(e5,n4))}))})))}},[r.text]:{match:Ue(re),order:4,parse:e3=>({text:e3[0].replace(T,(e4,t3)=>i3.namedCodesToUnicode[t3]?i3.namedCodesToUnicode[t3]:e4)}),render:e3=>e3.text},[r.textBolded]:{match:Ie(J),order:2,parse:(e3,t3,n4)=>({children:t3(e3[2],n4)}),render:(e3,t3,n4)=>u3("strong",{key:n4.key},t3(e3.children,n4))},[r.textEmphasized]:{match:Ie(K),order:3,parse:(e3,t3,n4)=>({children:t3(e3[2],n4)}),render:(e3,t3,n4)=>u3("em",{key:n4.key},t3(e3.children,n4))},[r.textEscaped]:{match:Ie(te),order:1,parse:e3=>({text:e3[1],type:r.text})},[r.textMarked]:{match:Ie(Y),order:3,parse:Ge,render:(e3,t3,n4)=>u3("mark",{key:n4.key},t3(e3.children,n4))},[r.textStrikethroughed]:{match:Ie(ee),order:3,parse:Ge,render:(e3,t3,n4)=>u3("del",{key:n4.key},t3(e3.children,n4))}};i3.disableParsingRawHTML===!0&&(delete X3[r.htmlBlock],delete X3[r.htmlSelfClosing]);let oe2=function(e3){let t3=Object.keys(e3);function n4(r3,i4){let l3,o3,a3=[],c3="",s3="";for(i4.prevCapture=i4.prevCapture||"";r3;){let d3=0;for(;d3<t3.length;){if(c3=t3[d3],l3=e3[c3],i4.inline&&!l3.match.inline){d3++;continue}let u4=l3.match(r3,i4);if(u4){s3=u4[0],i4.prevCapture+=s3,r3=r3.substring(s3.length),o3=l3.parse(u4,n4,i4),o3.type==null&&(o3.type=c3),a3.push(o3);break}d3++;}}return i4.prevCapture="",a3}return t3.sort(function(t4,n5){let r3=e3[t4].order,i4=e3[n5].order;return r3!==i4?r3-i4:t4<n5?-1:1}),function(e4,t4){return n4(function(e5){return e5.replace(v,`
`).replace(S,"").replace(G,"    ")}(e4),t4)}}(X3),ae2=(ce2=function(e3,t3){return function(n4,r3,i4){let l3=e3[n4.type].render;return t3?t3(()=>l3(n4,r3,i4),n4,r3,i4):l3(n4,r3,i4)}}(X3,i3.renderRule),function e3(t3,n4={}){if(Array.isArray(t3)){let r3=n4.key,i4=[],l3=!1;for(let r4=0;r4<t3.length;r4++){n4.key=r4;let o3=e3(t3[r4],n4),a3=typeof o3=="string";a3&&l3?i4[i4.length-1]+=o3:o3!==null&&i4.push(o3),l3=a3;}return n4.key=r3,i4}return ce2(t3,e3,n4)});var ce2;let se2=Z3(n3);return Q3.length?u3("div",null,se2,u3("footer",{key:"footer"},Q3.map(function(e3){return u3("div",{id:i3.slugify(e3.identifier,Le),key:e3.identifier},e3.identifier,ae2(oe2(e3.footnote,{inline:!0})))}))):se2}var index_modern_default=t3=>{let{children:r3="",options:i3}=t3,l3=function(e3,t4){if(e3==null)return {};var n3,r4,i4={},l4=Object.keys(e3);for(r4=0;r4<l4.length;r4++)t4.indexOf(n3=l4[r4])>=0||(i4[n3]=e3[n3]);return i4}(t3,n);return React20__namespace.cloneElement(Xe(r3,i3),l3)};init_helpers();var Label2=theming.styled.label(({theme})=>({lineHeight:"18px",alignItems:"center",marginBottom:8,display:"inline-block",position:"relative",whiteSpace:"nowrap",background:theme.boolean.background,borderRadius:"3em",padding:1,'&[aria-disabled="true"]':{opacity:.5,input:{cursor:"not-allowed"}},input:{appearance:"none",width:"100%",height:"100%",position:"absolute",left:0,top:0,margin:0,padding:0,border:"none",background:"transparent",cursor:"pointer",borderRadius:"3em","&:focus":{outline:"none",boxShadow:`${theme.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:"center",fontSize:theme.typography.size.s1,fontWeight:theme.typography.weight.bold,lineHeight:"1",cursor:"pointer",display:"inline-block",padding:"7px 15px",transition:"all 100ms ease-out",userSelect:"none",borderRadius:"3em",color:curriedTransparentize$1(.5,theme.color.defaultText),background:"transparent","&:hover":{boxShadow:`${curriedOpacify$1(.3,theme.appBorderColor)} 0 0 0 1px inset`},"&:active":{boxShadow:`${curriedOpacify$1(.05,theme.appBorderColor)} 0 0 0 2px inset`,color:curriedOpacify$1(1,theme.appBorderColor)},"&:first-of-type":{paddingRight:8},"&:last-of-type":{paddingLeft:8}},"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type":{background:theme.boolean.selectedBackground,boxShadow:theme.base==="light"?`${curriedOpacify$1(.1,theme.appBorderColor)} 0 0 2px`:`${theme.appBorderColor} 0 0 0 1px`,color:theme.color.defaultText,padding:"7px 15px"}})),parse=value2=>value2==="true",BooleanControl=({name,value:value2,onChange,onBlur,onFocus,argType})=>{let onSetFalse=React20.useCallback(()=>onChange(!1),[onChange]),readonly=!!argType?.table?.readonly;if(value2===void 0)return React20__namespace.default.createElement(components.Button,{variant:"outline",size:"medium",id:getControlSetterButtonId(name),onClick:onSetFalse,disabled:readonly},"Set boolean");let controlId=getControlId(name),parsedValue=typeof value2=="string"?parse(value2):value2;return React20__namespace.default.createElement(Label2,{"aria-disabled":readonly,htmlFor:controlId,"aria-label":name},React20__namespace.default.createElement("input",{id:controlId,type:"checkbox",onChange:e3=>onChange(e3.target.checked),checked:parsedValue,role:"switch",disabled:readonly,name,onBlur,onFocus}),React20__namespace.default.createElement("span",{"aria-hidden":"true"},"False"),React20__namespace.default.createElement("span",{"aria-hidden":"true"},"True"))};init_helpers();var parseDate=value2=>{let[year,month,day]=value2.split("-"),result=new Date;return result.setFullYear(parseInt(year,10),parseInt(month,10)-1,parseInt(day,10)),result},parseTime=value2=>{let[hours,minutes]=value2.split(":"),result=new Date;return result.setHours(parseInt(hours,10)),result.setMinutes(parseInt(minutes,10)),result},formatDate=value2=>{let date=new Date(value2),year=`000${date.getFullYear()}`.slice(-4),month=`0${date.getMonth()+1}`.slice(-2),day=`0${date.getDate()}`.slice(-2);return `${year}-${month}-${day}`},formatTime=value2=>{let date=new Date(value2),hours=`0${date.getHours()}`.slice(-2),minutes=`0${date.getMinutes()}`.slice(-2);return `${hours}:${minutes}`},FormInput=theming.styled(components.Form.Input)(({readOnly})=>({opacity:readOnly?.5:1})),FlexSpaced=theming.styled.div(({theme})=>({flex:1,display:"flex",input:{marginLeft:10,flex:1,height:32,"&::-webkit-calendar-picker-indicator":{opacity:.5,height:12,filter:theme.base==="light"?void 0:"invert(1)"}},"input:first-of-type":{marginLeft:0,flexGrow:4},"input:last-of-type":{flexGrow:3}})),DateControl=({name,value:value2,onChange,onFocus,onBlur,argType})=>{let[valid,setValid]=React20.useState(!0),dateRef=React20.useRef(),timeRef=React20.useRef(),readonly=!!argType?.table?.readonly;React20.useEffect(()=>{valid!==!1&&(dateRef&&dateRef.current&&(dateRef.current.value=value2?formatDate(value2):""),timeRef&&timeRef.current&&(timeRef.current.value=value2?formatTime(value2):""));},[value2]);let onDateChange=e3=>{if(!e3.target.value)return onChange();let parsed=parseDate(e3.target.value),result=new Date(value2??"");result.setFullYear(parsed.getFullYear(),parsed.getMonth(),parsed.getDate());let time=result.getTime();time&&onChange(time),setValid(!!time);},onTimeChange=e3=>{if(!e3.target.value)return onChange();let parsed=parseTime(e3.target.value),result=new Date(value2??"");result.setHours(parsed.getHours()),result.setMinutes(parsed.getMinutes());let time=result.getTime();time&&onChange(time),setValid(!!time);},controlId=getControlId(name);return React20__namespace.default.createElement(FlexSpaced,null,React20__namespace.default.createElement(FormInput,{type:"date",max:"9999-12-31",ref:dateRef,id:`${controlId}-date`,name:`${controlId}-date`,readOnly:readonly,onChange:onDateChange,onFocus,onBlur}),React20__namespace.default.createElement(FormInput,{type:"time",id:`${controlId}-time`,name:`${controlId}-time`,ref:timeRef,onChange:onTimeChange,readOnly:readonly,onFocus,onBlur}),valid?null:React20__namespace.default.createElement("div",null,"invalid"))};init_helpers();var Wrapper4=theming.styled.label({display:"flex"}),parse2=value2=>{let result=parseFloat(value2);return Number.isNaN(result)?void 0:result},format2=value2=>value2!=null?String(value2):"",FormInput2=theming.styled(components.Form.Input)(({readOnly})=>({opacity:readOnly?.5:1})),NumberControl=({name,value:value2,onChange,min,max,step,onBlur,onFocus,argType})=>{let[inputValue,setInputValue]=React20.useState(typeof value2=="number"?value2:""),[forceVisible,setForceVisible]=React20.useState(!1),[parseError,setParseError]=React20.useState(null),readonly=!!argType?.table?.readonly,handleChange=React20.useCallback(event=>{setInputValue(event.target.value);let result=parseFloat(event.target.value);Number.isNaN(result)?setParseError(new Error(`'${event.target.value}' is not a number`)):(onChange(result),setParseError(null));},[onChange,setParseError]),onForceVisible=React20.useCallback(()=>{setInputValue("0"),onChange(0),setForceVisible(!0);},[setForceVisible]),htmlElRef=React20.useRef(null);return React20.useEffect(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select();},[forceVisible]),React20.useEffect(()=>{let newInputValue=typeof value2=="number"?value2:"";inputValue!==newInputValue&&setInputValue(newInputValue);},[value2]),value2===void 0?React20__namespace.default.createElement(components.Button,{variant:"outline",size:"medium",id:getControlSetterButtonId(name),onClick:onForceVisible,disabled:readonly},"Set number"):React20__namespace.default.createElement(Wrapper4,null,React20__namespace.default.createElement(FormInput2,{ref:htmlElRef,id:getControlId(name),type:"number",onChange:handleChange,size:"flex",placeholder:"Edit number...",value:inputValue,valid:parseError?"error":void 0,autoFocus:forceVisible,readOnly:readonly,name,min,max,step,onFocus,onBlur}))};init_helpers();var selectedKey=(value2,options)=>{let entry=options&&Object.entries(options).find(([_key,val])=>val===value2);return entry?entry[0]:void 0},selectedKeys=(value2,options)=>value2&&options?Object.entries(options).filter(entry=>value2.includes(entry[1])).map(entry=>entry[0]):[],selectedValues=(keys,options)=>keys&&options&&keys.map(key=>options[key]);var Wrapper5=theming.styled.div(({isInline})=>isInline?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},props=>{if(props["aria-readonly"]==="true")return {input:{cursor:"not-allowed"}}}),Text=theming.styled.span({"[aria-readonly=true] &":{opacity:.5}}),Label3=theming.styled.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),CheckboxControl=({name,options,value:value2,onChange,isInline,argType})=>{if(!options)return clientLogger.logger.warn(`Checkbox with no options: ${name}`),React20__namespace.default.createElement(React20__namespace.default.Fragment,null,"-");let initial=selectedKeys(value2||[],options),[selected,setSelected]=React20.useState(initial),readonly=!!argType?.table?.readonly,handleChange=e3=>{let option=e3.target.value,updated=[...selected];updated.includes(option)?updated.splice(updated.indexOf(option),1):updated.push(option),onChange(selectedValues(updated,options)),setSelected(updated);};React20.useEffect(()=>{setSelected(selectedKeys(value2||[],options));},[value2]);let controlId=getControlId(name);return React20__namespace.default.createElement(Wrapper5,{"aria-readonly":readonly,isInline},Object.keys(options).map((key,index)=>{let id2=`${controlId}-${index}`;return React20__namespace.default.createElement(Label3,{key:id2,htmlFor:id2},React20__namespace.default.createElement("input",{type:"checkbox",disabled:readonly,id:id2,name:id2,value:key,onChange:handleChange,checked:selected?.includes(key)}),React20__namespace.default.createElement(Text,null,key))}))};init_helpers();var Wrapper6=theming.styled.div(({isInline})=>isInline?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},props=>{if(props["aria-readonly"]==="true")return {input:{cursor:"not-allowed"}}}),Text2=theming.styled.span({"[aria-readonly=true] &":{opacity:.5}}),Label4=theming.styled.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),RadioControl=({name,options,value:value2,onChange,isInline,argType})=>{if(!options)return clientLogger.logger.warn(`Radio with no options: ${name}`),React20__namespace.default.createElement(React20__namespace.default.Fragment,null,"-");let selection=selectedKey(value2,options),controlId=getControlId(name),readonly=!!argType?.table?.readonly;return React20__namespace.default.createElement(Wrapper6,{"aria-readonly":readonly,isInline},Object.keys(options).map((key,index)=>{let id2=`${controlId}-${index}`;return React20__namespace.default.createElement(Label4,{key:id2,htmlFor:id2},React20__namespace.default.createElement("input",{type:"radio",id:id2,name:controlId,disabled:readonly,value:key,onChange:e3=>onChange(options[e3.currentTarget.value]),checked:key===selection}),React20__namespace.default.createElement(Text2,null,key))}))};init_helpers();var styleResets={appearance:"none",border:"0 none",boxSizing:"inherit",display:" block",margin:" 0",background:"transparent",padding:0,fontSize:"inherit",position:"relative"},OptionsSelect=theming.styled.select(styleResets,({theme})=>({boxSizing:"border-box",position:"relative",padding:"6px 10px",width:"100%",color:theme.input.color||"inherit",background:theme.input.background,borderRadius:theme.input.borderRadius,boxShadow:`${theme.input.border} 0 0 0 1px inset`,fontSize:theme.typography.size.s2-1,lineHeight:"20px","&:focus":{boxShadow:`${theme.color.secondary} 0 0 0 1px inset`,outline:"none"},"&[disabled]":{cursor:"not-allowed",opacity:.5},"::placeholder":{color:theme.textMutedColor},"&[multiple]":{overflow:"auto",padding:0,option:{display:"block",padding:"6px 10px",marginLeft:1,marginRight:1}}})),SelectWrapper=theming.styled.span(({theme})=>({display:"inline-block",lineHeight:"normal",overflow:"hidden",position:"relative",verticalAlign:"top",width:"100%",svg:{position:"absolute",zIndex:1,pointerEvents:"none",height:"12px",marginTop:"-6px",right:"12px",top:"50%",fill:theme.textMutedColor,path:{fill:theme.textMutedColor}}})),NO_SELECTION="Choose option...",SingleSelect=({name,value:value2,options,onChange,argType})=>{let handleChange=e3=>{onChange(options[e3.currentTarget.value]);},selection=selectedKey(value2,options)||NO_SELECTION,controlId=getControlId(name),readonly=!!argType?.table?.readonly;return React20__namespace.default.createElement(SelectWrapper,null,React20__namespace.default.createElement(icons.ChevronSmallDownIcon,null),React20__namespace.default.createElement(OptionsSelect,{disabled:readonly,id:controlId,value:selection,onChange:handleChange},React20__namespace.default.createElement("option",{key:"no-selection",disabled:!0},NO_SELECTION),Object.keys(options).map(key=>React20__namespace.default.createElement("option",{key,value:key},key))))},MultiSelect=({name,value:value2,options,onChange,argType})=>{let handleChange=e3=>{let selection2=Array.from(e3.currentTarget.options).filter(option=>option.selected).map(option=>option.value);onChange(selectedValues(selection2,options));},selection=selectedKeys(value2,options),controlId=getControlId(name),readonly=!!argType?.table?.readonly;return React20__namespace.default.createElement(SelectWrapper,null,React20__namespace.default.createElement(OptionsSelect,{disabled:readonly,id:controlId,multiple:!0,value:selection,onChange:handleChange},Object.keys(options).map(key=>React20__namespace.default.createElement("option",{key,value:key},key))))},SelectControl=props=>{let{name,options}=props;return options?props.isMulti?React20__namespace.default.createElement(MultiSelect,{...props}):React20__namespace.default.createElement(SingleSelect,{...props}):(clientLogger.logger.warn(`Select with no options: ${name}`),React20__namespace.default.createElement(React20__namespace.default.Fragment,null,"-"))};var normalizeOptions=(options,labels)=>Array.isArray(options)?options.reduce((acc,item)=>(acc[labels?.[item]||String(item)]=item,acc),{}):options,Controls={check:CheckboxControl,"inline-check":CheckboxControl,radio:RadioControl,"inline-radio":RadioControl,select:SelectControl,"multi-select":SelectControl},OptionsControl=props=>{let{type="select",labels,argType}=props,normalized={...props,argType,options:argType?normalizeOptions(argType.options,labels):{},isInline:type.includes("inline"),isMulti:type.includes("multi")},Control=Controls[type];if(Control)return React20__namespace.default.createElement(Control,{...normalized});throw new Error(`Unknown options type: ${type}`)};init_compat();init_helpers();var ERROR="Error",OBJECT="Object",ARRAY="Array",STRING="String",NUMBER="Number",BOOLEAN="Boolean",DATE="Date",NULL="Null",UNDEFINED="Undefined",FUNCTION="Function",SYMBOL="Symbol";var ADD_DELTA_TYPE="ADD_DELTA_TYPE",REMOVE_DELTA_TYPE="REMOVE_DELTA_TYPE",UPDATE_DELTA_TYPE="UPDATE_DELTA_TYPE";var VALUE="value",KEY="key";function getObjectType(obj){return obj!==null&&typeof obj=="object"&&!Array.isArray(obj)&&typeof obj[Symbol.iterator]=="function"?"Iterable":Object.prototype.toString.call(obj).slice(8,-1)}function isComponentWillChange(oldValue,newValue){let oldType=getObjectType(oldValue),newType=getObjectType(newValue);return (oldType==="Function"||newType==="Function")&&newType!==oldType}var JsonAddValue=class extends React20.Component{constructor(props){super(props),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this);}componentDidMount(){let{inputRefKey,inputRefValue}=this.state,{onlyValue}=this.props;inputRefKey&&typeof inputRefKey.focus=="function"&&inputRefKey.focus(),onlyValue&&inputRefValue&&typeof inputRefValue.focus=="function"&&inputRefValue.focus(),document.addEventListener("keydown",this.onKeydown);}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown);}onKeydown(event){if(event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat)return;let{inputRefKey,inputRefValue}=this.state,{addButtonElement,handleCancel}=this.props;[inputRefKey,inputRefValue,addButtonElement].some(elm=>elm===event.target)&&((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.onSubmit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),handleCancel()));}onSubmit(){let{handleAdd,onlyValue,onSubmitValueParser,keyPath,deep}=this.props,{inputRefKey,inputRefValue}=this.state,result={};if(!onlyValue){if(!inputRefKey.value)return;result.key=inputRefKey.value;}result.newValue=onSubmitValueParser(!1,keyPath,deep,result.key,inputRefValue.value),handleAdd(result);}refInputKey(node){this.state.inputRefKey=node;}refInputValue(node){this.state.inputRefValue=node;}render(){let{handleCancel,onlyValue,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep}=this.props,addButtonElementLayout=addButtonElement&&React20.cloneElement(addButtonElement,{onClick:this.onSubmit}),cancelButtonElementLayout=cancelButtonElement&&React20.cloneElement(cancelButtonElement,{onClick:handleCancel}),inputElementValue=inputElementGenerator(VALUE,keyPath,deep),inputElementValueLayout=React20.cloneElement(inputElementValue,{placeholder:"Value",ref:this.refInputValue}),inputElementKeyLayout=null;if(!onlyValue){let inputElementKey=inputElementGenerator(KEY,keyPath,deep);inputElementKeyLayout=React20.cloneElement(inputElementKey,{placeholder:"Key",ref:this.refInputKey});}return React20__namespace.default.createElement("span",{className:"rejt-add-value-node"},inputElementKeyLayout,inputElementValueLayout,cancelButtonElementLayout,addButtonElementLayout)}};JsonAddValue.defaultProps={onlyValue:!1,addButtonElement:React20__namespace.default.createElement("button",null,"+"),cancelButtonElement:React20__namespace.default.createElement("button",null,"c")};var JsonArray=class extends React20.Component{constructor(props){super(props);let keyPath=[...props.keyPath||[],props.name];this.state={data:props.data,name:props.name,keyPath,deep:props.deep??0,nextDeep:(props.deep??0)+1,collapsed:props.isCollapsed(keyPath,props.deep??0,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath=[]}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data);}handleAddMode(){this.setState({addFormVisible:!0});}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}));}handleRemoveItem(index){return ()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[index];(beforeRemoveAction||Promise.resolve.bind(Promise))(index,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key:index,oldValue,type:REMOVE_DELTA_TYPE};data.splice(index,1),this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult);}).catch(logger4.error);}}handleAddValueAdd({key,newValue}){let{data,keyPath=[],nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;(beforeAddAction||Promise.resolve.bind(Promise))(key,keyPath,deep,newValue).then(()=>{data[key]=newValue,this.setState({data}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key,newValue});}).catch(logger4.error);}handleAddValueCancel(){this.setState({addFormVisible:!1});}handleEditValue({key,value:value2}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];(beforeUpdateAction||Promise.resolve.bind(Promise))(key,keyPath,deep,oldValue,value2).then(()=>{data[key]=value2,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key,newValue:value2,oldValue}),resolve(void 0);}).catch(reject);})}renderCollapsed(){let{name,data,keyPath,deep}=this.state,{handleRemove,readOnly,getStyle,dataType,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name,data,keyPath,deep,dataType),isReadOnly=readOnly(name,data,keyPath,deep,dataType),removeItemButton=minusMenuElement&&React20.cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return React20__namespace.default.createElement("span",{className:"rejt-collapsed"},React20__namespace.default.createElement("span",{className:"rejt-collapsed-text",style:collapsed,onClick:this.handleCollapseMode},"[...] ",data.length," ",data.length===1?"item":"items"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name,data,keyPath,deep,addFormVisible,nextDeep}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,delimiter,ul,addForm}=getStyle(name,data,keyPath,deep,dataType),isReadOnly=readOnly(name,data,keyPath,deep,dataType),addItemButton=plusMenuElement&&React20.cloneElement(plusMenuElement,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:plus}),removeItemButton=minusMenuElement&&React20.cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return React20__namespace.default.createElement("span",{className:"rejt-not-collapsed"},React20__namespace.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"["),!addFormVisible&&addItemButton,React20__namespace.default.createElement("ul",{className:"rejt-not-collapsed-list",style:ul},data.map((item,index)=>React20__namespace.default.createElement(JsonNode,{key:index,name:index.toString(),data:item,keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveItem(index),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}))),!isReadOnly&&addFormVisible&&React20__namespace.default.createElement("div",{className:"rejt-add-form",style:addForm},React20__namespace.default.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue:!0,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),React20__namespace.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"]"),!isReadOnly&&removeItemButton)}render(){let{name,collapsed,data,keyPath,deep}=this.state,{dataType,getStyle}=this.props,value2=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name,data,keyPath,deep,dataType);return React20__namespace.default.createElement("div",{className:"rejt-array-node"},React20__namespace.default.createElement("span",{onClick:this.handleCollapseMode},React20__namespace.default.createElement("span",{className:"rejt-name",style:style.name},name," :"," ")),value2)}};JsonArray.defaultProps={keyPath:[],deep:0,minusMenuElement:React20__namespace.default.createElement("span",null," - "),plusMenuElement:React20__namespace.default.createElement("span",null," + ")};var JsonFunctionValue=class extends React20.Component{constructor(props){super(props);let keyPath=[...props.keyPath||[],props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this);}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name,value:value2,keyPath,deep}=this.state,{readOnly,dataType}=this.props,readOnlyResult=readOnly(name,value2,keyPath,deep,dataType);editEnabled&&!readOnlyResult&&typeof inputRef.focus=="function"&&inputRef.focus();}componentDidMount(){document.addEventListener("keydown",this.onKeydown);}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown);}onKeydown(event){let{inputRef}=this.state;event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||inputRef!==event.target||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.handleEdit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.handleCancelEdit()));}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name,inputRef.value),result={value:newValue,key:name};(handleUpdateValue||Promise.resolve.bind(Promise))(result).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit();}).catch(logger4.error);}handleEditMode(){this.setState({editEnabled:!0});}refInput(node){this.state.inputRef=node;}handleCancelEdit(){this.setState({editEnabled:!1});}render(){let{name,value:value2,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,textareaElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name,originalValue,keyPath,deep,dataType),result=null,minusElement=null,resultOnlyResult=readOnly(name,originalValue,keyPath,deep,dataType);if(editEnabled&&!resultOnlyResult){let textareaElement=textareaElementGenerator(VALUE,comeFromKeyPath,deep,name,originalValue,dataType),editButtonElementLayout=editButtonElement&&React20.cloneElement(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=cancelButtonElement&&React20.cloneElement(cancelButtonElement,{onClick:this.handleCancelEdit}),textareaElementLayout=React20.cloneElement(textareaElement,{ref:this.refInput,defaultValue:originalValue});result=React20__namespace.default.createElement("span",{className:"rejt-edit-form",style:style.editForm},textareaElementLayout," ",cancelButtonElementLayout,editButtonElementLayout),minusElement=null;}else {result=React20__namespace.default.createElement("span",{className:"rejt-value",style:style.value,onClick:resultOnlyResult?void 0:this.handleEditMode},value2);let minusMenuLayout=minusMenuElement&&React20.cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:style.minus});minusElement=resultOnlyResult?null:minusMenuLayout;}return React20__namespace.default.createElement("li",{className:"rejt-function-value-node",style:style.li},React20__namespace.default.createElement("span",{className:"rejt-name",style:style.name},name," :"," "),result,minusElement)}};JsonFunctionValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>{},editButtonElement:React20__namespace.default.createElement("button",null,"e"),cancelButtonElement:React20__namespace.default.createElement("button",null,"c"),minusMenuElement:React20__namespace.default.createElement("span",null," - ")};var JsonNode=class extends React20.Component{constructor(props){super(props),this.state={data:props.data,name:props.name,keyPath:props.keyPath,deep:props.deep};}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}render(){let{data,name,keyPath,deep}=this.state,{isCollapsed,handleRemove,handleUpdateValue,onUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,readOnlyTrue=()=>!0,dataType=getObjectType(data);switch(dataType){case ERROR:return React20__namespace.default.createElement(JsonObject,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly:readOnlyTrue,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case OBJECT:return React20__namespace.default.createElement(JsonObject,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case ARRAY:return React20__namespace.default.createElement(JsonArray,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case STRING:return React20__namespace.default.createElement(JsonValue,{name,value:`"${data}"`,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NUMBER:return React20__namespace.default.createElement(JsonValue,{name,value:data,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case BOOLEAN:return React20__namespace.default.createElement(JsonValue,{name,value:data?"true":"false",originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case DATE:return React20__namespace.default.createElement(JsonValue,{name,value:data.toISOString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NULL:return React20__namespace.default.createElement(JsonValue,{name,value:"null",originalValue:"null",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case UNDEFINED:return React20__namespace.default.createElement(JsonValue,{name,value:"undefined",originalValue:"undefined",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case FUNCTION:return React20__namespace.default.createElement(JsonFunctionValue,{name,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,textareaElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case SYMBOL:return React20__namespace.default.createElement(JsonValue,{name,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});default:return null}}};JsonNode.defaultProps={keyPath:[],deep:0};var JsonObject=class extends React20.Component{constructor(props){super(props);let keyPath=props.deep===-1?[]:[...props.keyPath||[],props.name];this.state={name:props.name,data:props.data,keyPath,deep:props.deep??0,nextDeep:(props.deep??0)+1,collapsed:props.isCollapsed(keyPath,props.deep??0,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath=[]}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data);}handleAddMode(){this.setState({addFormVisible:!0});}handleAddValueCancel(){this.setState({addFormVisible:!1});}handleAddValueAdd({key,newValue}){let{data,keyPath=[],nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;(beforeAddAction||Promise.resolve.bind(Promise))(key,keyPath,deep,newValue).then(()=>{data[key]=newValue,this.setState({data}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key,newValue});}).catch(logger4.error);}handleRemoveValue(key){return ()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath=[],nextDeep:deep}=this.state,oldValue=data[key];(beforeRemoveAction||Promise.resolve.bind(Promise))(key,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key,oldValue,type:REMOVE_DELTA_TYPE};delete data[key],this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult);}).catch(logger4.error);}}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}));}handleEditValue({key,value:value2}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath=[],nextDeep:deep}=this.state,oldValue=data[key];(beforeUpdateAction||Promise.resolve.bind(Promise))(key,keyPath,deep,oldValue,value2).then(()=>{data[key]=value2,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key,newValue:value2,oldValue}),resolve();}).catch(reject);})}renderCollapsed(){let{name,keyPath,deep,data}=this.state,{handleRemove,readOnly,dataType,getStyle,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name,data,keyPath,deep,dataType),removeItemButton=minusMenuElement&&React20.cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return React20__namespace.default.createElement("span",{className:"rejt-collapsed"},React20__namespace.default.createElement("span",{className:"rejt-collapsed-text",style:collapsed,onClick:this.handleCollapseMode},"{...}"," ",keyList.length," ",keyList.length===1?"key":"keys"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name,data,keyPath,deep,nextDeep,addFormVisible}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,addForm,ul,delimiter}=getStyle(name,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name,data,keyPath,deep,dataType),addItemButton=plusMenuElement&&React20.cloneElement(plusMenuElement,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:plus}),removeItemButton=minusMenuElement&&React20.cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus}),list=keyList.map(key=>React20__namespace.default.createElement(JsonNode,{key,name:key,data:data[key],keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveValue(key),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}));return React20__namespace.default.createElement("span",{className:"rejt-not-collapsed"},React20__namespace.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"{"),!isReadOnly&&addItemButton,React20__namespace.default.createElement("ul",{className:"rejt-not-collapsed-list",style:ul},list),!isReadOnly&&addFormVisible&&React20__namespace.default.createElement("div",{className:"rejt-add-form",style:addForm},React20__namespace.default.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),React20__namespace.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"}"),!isReadOnly&&removeItemButton)}render(){let{name,collapsed,data,keyPath,deep}=this.state,{getStyle,dataType}=this.props,value2=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name,data,keyPath,deep,dataType);return React20__namespace.default.createElement("div",{className:"rejt-object-node"},React20__namespace.default.createElement("span",{onClick:this.handleCollapseMode},React20__namespace.default.createElement("span",{className:"rejt-name",style:style.name},name," :"," ")),value2)}};JsonObject.defaultProps={keyPath:[],deep:0,minusMenuElement:React20__namespace.default.createElement("span",null," - "),plusMenuElement:React20__namespace.default.createElement("span",null," + ")};var JsonValue=class extends React20.Component{constructor(props){super(props);let keyPath=[...props.keyPath||[],props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this);}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name,value:value2,keyPath,deep}=this.state,{readOnly,dataType}=this.props,isReadOnly=readOnly(name,value2,keyPath,deep,dataType);editEnabled&&!isReadOnly&&typeof inputRef.focus=="function"&&inputRef.focus();}componentDidMount(){document.addEventListener("keydown",this.onKeydown);}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown);}onKeydown(event){let{inputRef}=this.state;event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||inputRef!==event.target||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.handleEdit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.handleCancelEdit()));}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name,inputRef.value),result={value:newValue,key:name};(handleUpdateValue||Promise.resolve.bind(Promise))(result).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit();}).catch(logger4.error);}handleEditMode(){this.setState({editEnabled:!0});}refInput(node){this.state.inputRef=node;}handleCancelEdit(){this.setState({editEnabled:!1});}render(){let{name,value:value2,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,inputElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name,originalValue,keyPath,deep,dataType),isReadOnly=readOnly(name,originalValue,keyPath,deep,dataType),isEditing=editEnabled&&!isReadOnly,inputElement=inputElementGenerator(VALUE,comeFromKeyPath,deep,name,originalValue,dataType),editButtonElementLayout=editButtonElement&&React20.cloneElement(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=cancelButtonElement&&React20.cloneElement(cancelButtonElement,{onClick:this.handleCancelEdit}),inputElementLayout=React20.cloneElement(inputElement,{ref:this.refInput,defaultValue:JSON.stringify(originalValue)}),minusMenuLayout=minusMenuElement&&React20.cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:style.minus});return React20__namespace.default.createElement("li",{className:"rejt-value-node",style:style.li},React20__namespace.default.createElement("span",{className:"rejt-name",style:style.name},name," : "),isEditing?React20__namespace.default.createElement("span",{className:"rejt-edit-form",style:style.editForm},inputElementLayout," ",cancelButtonElementLayout,editButtonElementLayout):React20__namespace.default.createElement("span",{className:"rejt-value",style:style.value,onClick:isReadOnly?void 0:this.handleEditMode},String(value2)),!isReadOnly&&!isEditing&&minusMenuLayout)}};JsonValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>Promise.resolve(),editButtonElement:React20__namespace.default.createElement("button",null,"e"),cancelButtonElement:React20__namespace.default.createElement("button",null,"c"),minusMenuElement:React20__namespace.default.createElement("span",null," - ")};function parse3(string){let result=string;if(result.indexOf("function")===0)return (0, eval)(`(${result})`);try{result=JSON.parse(string);}catch{}return result}var object={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},array={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},value={minus:{color:"red"},editForm:{},value:{color:"#7bba3d"},li:{minHeight:"22px",lineHeight:"22px",outline:"0px"},name:{color:"#2287CD"}};var JsonTree=class extends React20.Component{constructor(props){super(props),this.state={data:props.data,rootName:props.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data||props.rootName!==state.rootName?{data:props.data,rootName:props.rootName}:null}onUpdate(key,data){this.setState({data}),this.props.onFullyUpdate?.(data);}removeRoot(){this.onUpdate(null,null);}render(){let{data,rootName}=this.state,{isCollapsed,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElement,textareaElement,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser,fallback=null}=this.props,dataType=getObjectType(data),readOnlyFunction=readOnly;getObjectType(readOnly)==="Boolean"&&(readOnlyFunction=()=>readOnly);let inputElementFunction=inputElement;inputElement&&getObjectType(inputElement)!=="Function"&&(inputElementFunction=()=>inputElement);let textareaElementFunction=textareaElement;return textareaElement&&getObjectType(textareaElement)!=="Function"&&(textareaElementFunction=()=>textareaElement),dataType==="Object"||dataType==="Array"?React20__namespace.default.createElement("div",{className:"rejt-tree"},React20__namespace.default.createElement(JsonNode,{data,name:rootName||"root",deep:-1,isCollapsed:isCollapsed??(()=>!1),onUpdate:this.onUpdate,onDeltaUpdate:onDeltaUpdate??(()=>{}),readOnly:readOnlyFunction,getStyle:getStyle??(()=>({})),addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator:inputElementFunction,textareaElementGenerator:textareaElementFunction,minusMenuElement,plusMenuElement,handleRemove:this.removeRoot,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4??{},onSubmitValueParser:onSubmitValueParser??(val=>val)})):fallback}};JsonTree.defaultProps={rootName:"root",isCollapsed:(keyPath,deep)=>deep!==-1,getStyle:(keyName,data,keyPath,deep,dataType)=>{switch(dataType){case"Object":case"Error":return object;case"Array":return array;default:return value}},readOnly:()=>!1,onFullyUpdate:()=>{},onDeltaUpdate:()=>{},beforeRemoveAction:()=>Promise.resolve(),beforeAddAction:()=>Promise.resolve(),beforeUpdateAction:()=>Promise.resolve(),logger:{error:()=>{}},onSubmitValueParser:(isEditMode,keyPath,deep,name,rawValue)=>parse3(rawValue),inputElement:()=>React20__namespace.default.createElement("input",null),textareaElement:()=>React20__namespace.default.createElement("textarea",null),fallback:null};var {window:globalWindow2}=globalThis,Wrapper7=theming.styled.div(({theme})=>({position:"relative",display:"flex",'&[aria-readonly="true"]':{opacity:.5},".rejt-tree":{marginLeft:"1rem",fontSize:"13px"},".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed":{"& > svg":{opacity:0,transition:"opacity 0.2s"}},".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed":{"& > svg":{opacity:1}},".rejt-edit-form button":{display:"none"},".rejt-add-form":{marginLeft:10},".rejt-add-value-node":{display:"inline-flex",alignItems:"center"},".rejt-name":{lineHeight:"22px"},".rejt-not-collapsed-delimiter":{lineHeight:"22px"},".rejt-plus-menu":{marginLeft:5},".rejt-object-node > span > *, .rejt-array-node > span > *":{position:"relative",zIndex:2},".rejt-object-node, .rejt-array-node":{position:"relative"},".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before":{content:'""',position:"absolute",top:0,display:"block",width:"100%",marginLeft:"-1rem",padding:"0 4px 0 1rem",height:22},".rejt-collapsed::before, .rejt-not-collapsed::before":{zIndex:1,background:"transparent",borderRadius:4,transition:"background 0.2s",pointerEvents:"none",opacity:.1},".rejt-object-node:hover, .rejt-array-node:hover":{"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before":{background:theme.color.secondary}},".rejt-collapsed::after, .rejt-not-collapsed::after":{content:'""',position:"absolute",display:"inline-block",pointerEvents:"none",width:0,height:0},".rejt-collapsed::after":{left:-8,top:8,borderTop:"3px solid transparent",borderBottom:"3px solid transparent",borderLeft:"3px solid rgba(153,153,153,0.6)"},".rejt-not-collapsed::after":{left:-10,top:10,borderTop:"3px solid rgba(153,153,153,0.6)",borderLeft:"3px solid transparent",borderRight:"3px solid transparent"},".rejt-value":{display:"inline-block",border:"1px solid transparent",borderRadius:4,margin:"1px 0",padding:"0 4px",cursor:"text",color:theme.color.defaultText},".rejt-value-node:hover > .rejt-value":{background:theme.color.lighter,borderColor:theme.appBorderColor}})),ButtonInline=theming.styled.button(({theme,primary})=>({border:0,height:20,margin:1,borderRadius:4,background:primary?theme.color.secondary:"transparent",color:primary?theme.color.lightest:theme.color.dark,fontWeight:primary?"bold":"normal",cursor:"pointer",order:primary?"initial":9})),ActionAddIcon=theming.styled(icons.AddIcon)(({theme,disabled})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?"not-allowed":"pointer",color:theme.textMutedColor,"&:hover":disabled?{}:{color:theme.color.ancillary},"svg + &":{marginLeft:0}})),ActionSubstractIcon=theming.styled(icons.SubtractIcon)(({theme,disabled})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?"not-allowed":"pointer",color:theme.textMutedColor,"&:hover":disabled?{}:{color:theme.color.negative},"svg + &":{marginLeft:0}})),Input=theming.styled.input(({theme,placeholder})=>({outline:0,margin:placeholder?1:"1px 0",padding:"3px 4px",color:theme.color.defaultText,background:theme.background.app,border:`1px solid ${theme.appBorderColor}`,borderRadius:4,lineHeight:"14px",width:placeholder==="Key"?80:120,"&:focus":{border:`1px solid ${theme.color.secondary}`}})),RawButton=theming.styled(components.IconButton)(({theme})=>({position:"absolute",zIndex:2,top:2,right:2,height:21,padding:"0 3px",background:theme.background.bar,border:`1px solid ${theme.appBorderColor}`,borderRadius:3,color:theme.textMutedColor,fontSize:"9px",fontWeight:"bold",textDecoration:"none",span:{marginLeft:3,marginTop:1}})),RawInput=theming.styled(components.Form.Textarea)(({theme})=>({flex:1,padding:"7px 6px",fontFamily:theme.typography.fonts.mono,fontSize:"12px",lineHeight:"18px","&::placeholder":{fontFamily:theme.typography.fonts.base,fontSize:"13px"},"&:placeholder-shown":{padding:"7px 10px"}})),ENTER_EVENT={bubbles:!0,cancelable:!0,key:"Enter",code:"Enter",keyCode:13},dispatchEnterKey=event=>{event.currentTarget.dispatchEvent(new globalWindow2.KeyboardEvent("keydown",ENTER_EVENT));},selectValue=event=>{event.currentTarget.select();},getCustomStyleFunction=theme=>()=>({name:{color:theme.color.secondary},collapsed:{color:theme.color.dark},ul:{listStyle:"none",margin:"0 0 0 1rem",padding:0},li:{outline:0}}),ObjectControl=({name,value:value2,onChange,argType})=>{let theme=theming.useTheme(),data=React20.useMemo(()=>value2&&cloneDeep(value2),[value2]),hasData=data!=null,[showRaw,setShowRaw]=React20.useState(!hasData),[parseError,setParseError]=React20.useState(null),readonly=!!argType?.table?.readonly,updateRaw=React20.useCallback(raw=>{try{raw&&onChange(JSON.parse(raw)),setParseError(null);}catch(e3){setParseError(e3);}},[onChange]),[forceVisible,setForceVisible]=React20.useState(!1),onForceVisible=React20.useCallback(()=>{onChange({}),setForceVisible(!0);},[setForceVisible]),htmlElRef=React20.useRef(null);if(React20.useEffect(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select();},[forceVisible]),!hasData)return React20__namespace.default.createElement(components.Button,{disabled:readonly,id:getControlSetterButtonId(name),onClick:onForceVisible},"Set object");let rawJSONForm=React20__namespace.default.createElement(RawInput,{ref:htmlElRef,id:getControlId(name),name,defaultValue:value2===null?"":JSON.stringify(value2,null,2),onBlur:event=>updateRaw(event.target.value),placeholder:"Edit JSON string...",autoFocus:forceVisible,valid:parseError?"error":void 0,readOnly:readonly}),isObjectOrArray=Array.isArray(value2)||typeof value2=="object"&&value2?.constructor===Object;return React20__namespace.default.createElement(Wrapper7,{"aria-readonly":readonly},isObjectOrArray&&React20__namespace.default.createElement(RawButton,{onClick:e3=>{e3.preventDefault(),setShowRaw(v3=>!v3);}},showRaw?React20__namespace.default.createElement(icons.EyeCloseIcon,null):React20__namespace.default.createElement(icons.EyeIcon,null),React20__namespace.default.createElement("span",null,"RAW")),showRaw?rawJSONForm:React20__namespace.default.createElement(JsonTree,{readOnly:readonly||!isObjectOrArray,isCollapsed:isObjectOrArray?void 0:()=>!0,data,rootName:name,onFullyUpdate:onChange,getStyle:getCustomStyleFunction(theme),cancelButtonElement:React20__namespace.default.createElement(ButtonInline,{type:"button"},"Cancel"),editButtonElement:React20__namespace.default.createElement(ButtonInline,{type:"submit"},"Save"),addButtonElement:React20__namespace.default.createElement(ButtonInline,{type:"submit",primary:!0},"Save"),plusMenuElement:React20__namespace.default.createElement(ActionAddIcon,null),minusMenuElement:React20__namespace.default.createElement(ActionSubstractIcon,null),inputElement:(_3,__,___,key)=>key?React20__namespace.default.createElement(Input,{onFocus:selectValue,onBlur:dispatchEnterKey}):React20__namespace.default.createElement(Input,null),fallback:rawJSONForm}))};init_helpers();var RangeInput=theming.styled.input(({theme,min,max,value:value2,disabled})=>({"&":{width:"100%",backgroundColor:"transparent",appearance:"none"},"&::-webkit-slider-runnable-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:disabled?"not-allowed":"pointer"},"&::-webkit-slider-thumb":{marginTop:"-6px",width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:disabled?"not-allowed":"grab",appearance:"none",background:`${theme.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${curriedDarken$1(.05,theme.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${theme.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:disabled?"not-allowed":"grab"}},"&:focus":{outline:"none","&::-webkit-slider-runnable-track":{borderColor:rgba(theme.color.secondary,.4)},"&::-webkit-slider-thumb":{borderColor:theme.color.secondary,boxShadow:`0 0px 5px 0px ${theme.color.secondary}`}},"&::-moz-range-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:disabled?"not-allowed":"pointer",outline:"none"},"&::-moz-range-thumb":{width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:disabled?"not-allowed":"grap",background:`${theme.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${curriedDarken$1(.05,theme.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${theme.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&::-ms-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,color:"transparent",width:"100%",height:"6px",cursor:"pointer"},"&::-ms-fill-lower":{borderRadius:6},"&::-ms-fill-upper":{borderRadius:6},"&::-ms-thumb":{width:16,height:16,background:`${theme.input.background}`,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:50,cursor:"grab",marginTop:0},"@supports (-ms-ime-align:auto)":{"input[type=range]":{margin:"0"}}})),RangeLabel=theming.styled.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:"nowrap",fontFeatureSettings:"tnum",fontVariantNumeric:"tabular-nums","[aria-readonly=true] &":{opacity:.5}}),RangeCurrentAndMaxLabel=theming.styled(RangeLabel)(({numberOFDecimalsPlaces,max})=>({width:`${numberOFDecimalsPlaces+max.toString().length*2+3}ch`,textAlign:"right",flexShrink:0})),RangeWrapper=theming.styled.div({display:"flex",alignItems:"center",width:"100%"});function getNumberOfDecimalPlaces(number){let match=number.toString().match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return match?Math.max(0,(match[1]?match[1].length:0)-(match[2]?+match[2]:0)):0}var RangeControl=({name,value:value2,onChange,min=0,max=100,step=1,onBlur,onFocus,argType})=>{let handleChange=event=>{onChange(parse2(event.target.value));},hasValue=value2!==void 0,numberOFDecimalsPlaces=React20.useMemo(()=>getNumberOfDecimalPlaces(step),[step]),readonly=!!argType?.table?.readonly;return React20__namespace.default.createElement(RangeWrapper,{"aria-readonly":readonly},React20__namespace.default.createElement(RangeLabel,null,min),React20__namespace.default.createElement(RangeInput,{id:getControlId(name),type:"range",disabled:readonly,onChange:handleChange,name,min,max,step,onFocus,onBlur,value:value2??min}),React20__namespace.default.createElement(RangeCurrentAndMaxLabel,{numberOFDecimalsPlaces,max},hasValue?value2.toFixed(numberOFDecimalsPlaces):"--"," / ",max))};init_helpers();var Wrapper8=theming.styled.label({display:"flex"}),MaxLength=theming.styled.div(({isMaxed})=>({marginLeft:"0.75rem",paddingTop:"0.35rem",color:isMaxed?"red":void 0})),TextControl=({name,value:value2,onChange,onFocus,onBlur,maxLength,argType})=>{let handleChange=event=>{onChange(event.target.value);},readonly=!!argType?.table?.readonly,[forceVisible,setForceVisible]=React20.useState(!1),onForceVisible=React20.useCallback(()=>{onChange(""),setForceVisible(!0);},[setForceVisible]);if(value2===void 0)return React20__namespace.default.createElement(components.Button,{variant:"outline",size:"medium",disabled:readonly,id:getControlSetterButtonId(name),onClick:onForceVisible},"Set string");let isValid=typeof value2=="string";return React20__namespace.default.createElement(Wrapper8,null,React20__namespace.default.createElement(components.Form.Textarea,{id:getControlId(name),maxLength,onChange:handleChange,disabled:readonly,size:"flex",placeholder:"Edit string...",autoFocus:forceVisible,valid:isValid?void 0:"error",name,value:isValid?value2:"",onFocus,onBlur}),maxLength&&React20__namespace.default.createElement(MaxLength,{isMaxed:value2?.length===maxLength},value2?.length??0," / ",maxLength))};init_helpers();var FileInput=theming.styled(components.Form.Input)({padding:10});function revokeOldUrls(urls){urls.forEach(url=>{url.startsWith("blob:")&&URL.revokeObjectURL(url);});}var FilesControl=({onChange,name,accept="image/*",value:value2,argType})=>{let inputElement=React20.useRef(null),readonly=argType?.control?.readOnly;function handleFileChange(e3){if(!e3.target.files)return;let fileUrls=Array.from(e3.target.files).map(file=>URL.createObjectURL(file));onChange(fileUrls),revokeOldUrls(value2||[]);}return React20.useEffect(()=>{value2==null&&inputElement.current&&(inputElement.current.value="");},[value2,name]),React20__namespace.default.createElement(FileInput,{ref:inputElement,id:getControlId(name),type:"file",name,multiple:!0,disabled:readonly,onChange:handleFileChange,accept,size:"flex"})};var LazyColorControl=React20.lazy(()=>Promise.resolve().then(()=>(init_Color(),Color_exports))),ColorControl2=props=>React20__namespace.default.createElement(React20.Suspense,{fallback:React20__namespace.default.createElement("div",null)},React20__namespace.default.createElement(LazyColorControl,{...props}));var Controls2={array:ObjectControl,object:ObjectControl,boolean:BooleanControl,color:ColorControl2,date:DateControl,number:NumberControl,check:OptionsControl,"inline-check":OptionsControl,radio:OptionsControl,"inline-radio":OptionsControl,select:OptionsControl,"multi-select":OptionsControl,range:RangeControl,text:TextControl,file:FilesControl},NoControl=()=>React20__namespace.default.createElement(React20__namespace.default.Fragment,null,"-"),ArgControl=({row,arg,updateArgs,isHovered})=>{let{key,control}=row,[isFocused,setFocused]=React20.useState(!1),[boxedValue,setBoxedValue]=React20.useState({value:arg});React20.useEffect(()=>{isFocused||setBoxedValue({value:arg});},[isFocused,arg]);let onChange=React20.useCallback(argVal=>(setBoxedValue({value:argVal}),updateArgs({[key]:argVal}),argVal),[updateArgs,key]),onBlur=React20.useCallback(()=>setFocused(!1),[]),onFocus=React20.useCallback(()=>setFocused(!0),[]);if(!control||control.disable){let canBeSetup=control?.disable!==!0&&row?.type?.name!=="function";return isHovered&&canBeSetup?React20__namespace.default.createElement(components.Link,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},"Setup controls"):React20__namespace.default.createElement(NoControl,null)}let props={name:key,argType:row,value:boxedValue.value,onChange,onBlur,onFocus},Control=Controls2[control.type]||NoControl;return React20__namespace.default.createElement(Control,{...props,...control,controlType:control.type})};var Table=theming.styled.table(({theme})=>({"&&":{borderCollapse:"collapse",borderSpacing:0,border:"none",tr:{border:"none !important",background:"none"},"td, th":{padding:0,border:"none",width:"auto!important"},marginTop:0,marginBottom:0,"th:first-of-type, td:first-of-type":{paddingLeft:0},"th:last-of-type, td:last-of-type":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,"&:not(:first-of-type)":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:"none",border:"none"},code:components.codeCommon({theme}),div:{span:{fontWeight:"bold"}},"& code":{margin:0,display:"inline-block",fontSize:theme.typography.size.s1}}})),ArgJsDoc=({tags})=>{let params=(tags.params||[]).filter(x3=>x3.description),hasDisplayableParams=params.length!==0,hasDisplayableDeprecated=tags.deprecated!=null,hasDisplayableReturns=tags.returns!=null&&tags.returns.description!=null;return !hasDisplayableParams&&!hasDisplayableReturns&&!hasDisplayableDeprecated?null:React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(Table,null,React20__namespace.default.createElement("tbody",null,hasDisplayableDeprecated&&React20__namespace.default.createElement("tr",{key:"deprecated"},React20__namespace.default.createElement("td",{colSpan:2},React20__namespace.default.createElement("strong",null,"Deprecated"),": ",tags.deprecated?.toString())),hasDisplayableParams&&params.map(x3=>React20__namespace.default.createElement("tr",{key:x3.name},React20__namespace.default.createElement("td",null,React20__namespace.default.createElement("code",null,x3.name)),React20__namespace.default.createElement("td",null,x3.description))),hasDisplayableReturns&&React20__namespace.default.createElement("tr",{key:"returns"},React20__namespace.default.createElement("td",null,React20__namespace.default.createElement("code",null,"Returns")),React20__namespace.default.createElement("td",null,tags.returns?.description)))))};init_compat();var import_memoizerific=__toESM(require_memoizerific());var ITEMS_BEFORE_EXPANSION=8,Summary=theming.styled.div(({isExpanded})=>({display:"flex",flexDirection:isExpanded?"column":"row",flexWrap:"wrap",alignItems:"flex-start",marginBottom:"-4px",minWidth:100})),Text3=theming.styled.span(components.codeCommon,({theme,simple=!1})=>({flex:"0 0 auto",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,wordBreak:"break-word",whiteSpace:"normal",maxWidth:"100%",margin:0,marginRight:"4px",marginBottom:"4px",paddingTop:"2px",paddingBottom:"2px",lineHeight:"13px",...simple&&{background:"transparent",border:"0 none",paddingLeft:0}})),ExpandButton=theming.styled.button(({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,marginBottom:"4px",background:"none",border:"none"})),Expandable=theming.styled.div(components.codeCommon,({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,fontSize:theme.typography.size.s1,margin:0,whiteSpace:"nowrap",display:"flex",alignItems:"center"})),Detail=theming.styled.div(({theme,width})=>({width,minWidth:200,maxWidth:800,padding:15,fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,boxSizing:"content-box","& code":{padding:"0 !important"}})),ChevronUpIcon=theming.styled(icons.ChevronSmallUpIcon)({marginLeft:4}),ChevronDownIcon=theming.styled(icons.ChevronSmallDownIcon)({marginLeft:4}),EmptyArg=()=>React20__namespace.default.createElement("span",null,"-"),ArgText=({text,simple})=>React20__namespace.default.createElement(Text3,{simple},text),calculateDetailWidth=(0, import_memoizerific.default)(1e3)(detail=>{let lines=detail.split(/\r?\n/);return `${Math.max(...lines.map(x3=>x3.length))}ch`}),getSummaryItems=summary=>{if(!summary)return [summary];let summaryItems=summary.split("|").map(value2=>value2.trim());return uniq2(summaryItems)},renderSummaryItems=(summaryItems,isExpanded=!0)=>{let items=summaryItems;return isExpanded||(items=summaryItems.slice(0,ITEMS_BEFORE_EXPANSION)),items.map(item=>React20__namespace.default.createElement(ArgText,{key:item,text:item===""?'""':item}))},ArgSummary=({value:value2,initialExpandedArgs})=>{let{summary,detail}=value2,[isOpen,setIsOpen]=React20.useState(!1),[isExpanded,setIsExpanded]=React20.useState(initialExpandedArgs||!1);if(summary==null)return null;let summaryAsString=typeof summary.toString=="function"?summary.toString():summary;if(detail==null){if(/[(){}[\]<>]/.test(summaryAsString))return React20__namespace.default.createElement(ArgText,{text:summaryAsString});let summaryItems=getSummaryItems(summaryAsString),itemsCount=summaryItems.length;return itemsCount>ITEMS_BEFORE_EXPANSION?React20__namespace.default.createElement(Summary,{isExpanded},renderSummaryItems(summaryItems,isExpanded),React20__namespace.default.createElement(ExpandButton,{onClick:()=>setIsExpanded(!isExpanded)},isExpanded?"Show less...":`Show ${itemsCount-ITEMS_BEFORE_EXPANSION} more...`)):React20__namespace.default.createElement(Summary,null,renderSummaryItems(summaryItems))}return React20__namespace.default.createElement(components.WithTooltipPure,{closeOnOutsideClick:!0,placement:"bottom",visible:isOpen,onVisibleChange:isVisible=>{setIsOpen(isVisible);},tooltip:React20__namespace.default.createElement(Detail,{width:calculateDetailWidth(detail)},React20__namespace.default.createElement(components.SyntaxHighlighter,{language:"jsx",format:!1},detail))},React20__namespace.default.createElement(Expandable,{className:"sbdocs-expandable"},React20__namespace.default.createElement("span",null,summaryAsString),isOpen?React20__namespace.default.createElement(ChevronUpIcon,null):React20__namespace.default.createElement(ChevronDownIcon,null)))},ArgValue=({value:value2,initialExpandedArgs})=>value2==null?React20__namespace.default.createElement(EmptyArg,null):React20__namespace.default.createElement(ArgSummary,{value:value2,initialExpandedArgs});var Name=theming.styled.span({fontWeight:"bold"}),Required=theming.styled.span(({theme})=>({color:theme.color.negative,fontFamily:theme.typography.fonts.mono,cursor:"help"})),Description=theming.styled.div(({theme})=>({"&&":{p:{margin:"0 0 10px 0"},a:{color:theme.color.secondary}},code:{...components.codeCommon({theme}),fontSize:12,fontFamily:theme.typography.fonts.mono},"& code":{margin:0,display:"inline-block"},"& pre > code":{whiteSpace:"pre-wrap"}})),Type=theming.styled.div(({theme,hasDescription})=>({color:theme.base==="light"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.2,theme.color.defaultText),marginTop:hasDescription?4:0})),TypeWithJsDoc=theming.styled.div(({theme,hasDescription})=>({color:theme.base==="light"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.2,theme.color.defaultText),marginTop:hasDescription?12:0,marginBottom:12})),StyledTd=theming.styled.td(({expandable})=>({paddingLeft:expandable?"40px !important":"20px !important"})),toSummary=value2=>value2&&{summary:typeof value2=="string"?value2:value2.name},ArgRow=props=>{let[isHovered,setIsHovered]=React20.useState(!1),{row,updateArgs,compact,expandable,initialExpandedArgs}=props,{name,description}=row,table=row.table||{},type=table.type||toSummary(row.type),defaultValue=table.defaultValue||row.defaultValue,required=row.type?.required,hasDescription=description!=null&&description!=="";return React20__namespace.default.createElement("tr",{onMouseEnter:()=>setIsHovered(!0),onMouseLeave:()=>setIsHovered(!1)},React20__namespace.default.createElement(StyledTd,{expandable:expandable??!1},React20__namespace.default.createElement(Name,null,name),required?React20__namespace.default.createElement(Required,{title:"Required"},"*"):null),compact?null:React20__namespace.default.createElement("td",null,hasDescription&&React20__namespace.default.createElement(Description,null,React20__namespace.default.createElement(index_modern_default,null,description)),table.jsDocTags!=null?React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(TypeWithJsDoc,{hasDescription},React20__namespace.default.createElement(ArgValue,{value:type,initialExpandedArgs})),React20__namespace.default.createElement(ArgJsDoc,{tags:table.jsDocTags})):React20__namespace.default.createElement(Type,{hasDescription},React20__namespace.default.createElement(ArgValue,{value:type,initialExpandedArgs}))),compact?null:React20__namespace.default.createElement("td",null,React20__namespace.default.createElement(ArgValue,{value:defaultValue,initialExpandedArgs})),updateArgs?React20__namespace.default.createElement("td",null,React20__namespace.default.createElement(ArgControl,{...props,isHovered})):null)};var Wrapper10=theming.styled.div(({inAddonPanel,theme})=>({height:inAddonPanel?"100%":"auto",display:"flex",border:inAddonPanel?"none":`1px solid ${theme.appBorderColor}`,borderRadius:inAddonPanel?0:theme.appBorderRadius,padding:inAddonPanel?0:40,alignItems:"center",justifyContent:"center",flexDirection:"column",gap:15,background:theme.background.content})),Links=theming.styled.div(({theme})=>({display:"flex",fontSize:theme.typography.size.s2-1,gap:25})),Empty=({inAddonPanel})=>{let[isLoading,setIsLoading]=React20.useState(!0);return React20.useEffect(()=>{let load=setTimeout(()=>{setIsLoading(!1);},100);return ()=>clearTimeout(load)},[]),isLoading?null:React20__namespace.default.createElement(Wrapper10,{inAddonPanel},React20__namespace.default.createElement(components.EmptyTabContent,{title:inAddonPanel?"Interactive story playground":"Args table with interactive controls couldn't be auto-generated",description:React20__namespace.default.createElement(React20__namespace.default.Fragment,null,"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically."),footer:React20__namespace.default.createElement(Links,null,inAddonPanel&&React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(components.Link,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},React20__namespace.default.createElement(icons.DocumentIcon,null)," Read docs")),!inAddonPanel&&React20__namespace.default.createElement(components.Link,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},React20__namespace.default.createElement(icons.DocumentIcon,null)," Learn how to set that up"))}))};var ExpanderIconDown=theming.styled(icons.ChevronDownIcon)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base==="light"?curriedTransparentize$1(.25,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),border:"none",display:"inline-block"})),ExpanderIconRight=theming.styled(icons.ChevronRightIcon)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base==="light"?curriedTransparentize$1(.25,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),border:"none",display:"inline-block"})),FlexWrapper=theming.styled.span(({theme})=>({display:"flex",lineHeight:"20px",alignItems:"center"})),Section=theming.styled.td(({theme})=>({position:"relative",letterSpacing:"0.35em",textTransform:"uppercase",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s1-1,color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText),background:`${theme.background.app} !important`,"& ~ td":{background:`${theme.background.app} !important`}})),Subsection=theming.styled.td(({theme})=>({position:"relative",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,background:theme.background.app})),StyledTd2=theming.styled.td({position:"relative"}),StyledTr=theming.styled.tr(({theme})=>({"&:hover > td":{backgroundColor:`${curriedLighten$1(.005,theme.background.app)} !important`,boxShadow:`${theme.color.mediumlight} 0 - 1px 0 0 inset`,cursor:"row-resize"}})),ClickIntercept=theming.styled.button({background:"none",border:"none",padding:"0",font:"inherit",position:"absolute",top:0,bottom:0,left:0,right:0,height:"100%",width:"100%",color:"transparent",cursor:"row-resize !important"}),SectionRow=({level="section",label,children,initialExpanded=!0,colSpan=3})=>{let[expanded,setExpanded]=React20.useState(initialExpanded),Level=level==="subsection"?Subsection:Section,itemCount=children?.length||0,caption=level==="subsection"?`${itemCount} item${itemCount!==1?"s":""}`:"",helperText=`${expanded?"Hide":"Show"} ${level==="subsection"?itemCount:label} item${itemCount!==1?"s":""}`;return React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(StyledTr,{title:helperText},React20__namespace.default.createElement(Level,{colSpan:1},React20__namespace.default.createElement(ClickIntercept,{onClick:e3=>setExpanded(!expanded),tabIndex:0},helperText),React20__namespace.default.createElement(FlexWrapper,null,expanded?React20__namespace.default.createElement(ExpanderIconDown,null):React20__namespace.default.createElement(ExpanderIconRight,null),label)),React20__namespace.default.createElement(StyledTd2,{colSpan:colSpan-1},React20__namespace.default.createElement(ClickIntercept,{onClick:e3=>setExpanded(!expanded),tabIndex:-1,style:{outline:"none"}},helperText),expanded?null:caption)),expanded?children:null)};var TableWrapper=theming.styled.div(({theme})=>({width:"100%",borderSpacing:0,color:theme.color.defaultText})),Row=theming.styled.div(({theme})=>({display:"flex",borderBottom:`1px solid ${theme.appBorderColor}`,"&:last-child":{borderBottom:0}})),Column=theming.styled.div(({position,theme})=>{let baseStyles={display:"flex",flexDirection:"column",gap:5,padding:"10px 15px",alignItems:"flex-start"};switch(position){case"first":return {...baseStyles,width:"25%",paddingLeft:20};case"second":return {...baseStyles,width:"35%"};case"third":return {...baseStyles,width:"15%"};case"last":return {...baseStyles,width:"25%",paddingRight:20}}}),SkeletonText=theming.styled.div(({theme,width,height})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,width:width||"100%",height:height||16,borderRadius:3})),Skeleton=()=>React20__namespace.default.createElement(TableWrapper,null,React20__namespace.default.createElement(Row,null,React20__namespace.default.createElement(Column,{position:"first"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"second"},React20__namespace.default.createElement(SkeletonText,{width:"30%"})),React20__namespace.default.createElement(Column,{position:"third"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"last"},React20__namespace.default.createElement(SkeletonText,{width:"60%"}))),React20__namespace.default.createElement(Row,null,React20__namespace.default.createElement(Column,{position:"first"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"second"},React20__namespace.default.createElement(SkeletonText,{width:"80%"}),React20__namespace.default.createElement(SkeletonText,{width:"30%"})),React20__namespace.default.createElement(Column,{position:"third"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"last"},React20__namespace.default.createElement(SkeletonText,{width:"60%"}))),React20__namespace.default.createElement(Row,null,React20__namespace.default.createElement(Column,{position:"first"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"second"},React20__namespace.default.createElement(SkeletonText,{width:"80%"}),React20__namespace.default.createElement(SkeletonText,{width:"30%"})),React20__namespace.default.createElement(Column,{position:"third"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"last"},React20__namespace.default.createElement(SkeletonText,{width:"60%"}))),React20__namespace.default.createElement(Row,null,React20__namespace.default.createElement(Column,{position:"first"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"second"},React20__namespace.default.createElement(SkeletonText,{width:"80%"}),React20__namespace.default.createElement(SkeletonText,{width:"30%"})),React20__namespace.default.createElement(Column,{position:"third"},React20__namespace.default.createElement(SkeletonText,{width:"60%"})),React20__namespace.default.createElement(Column,{position:"last"},React20__namespace.default.createElement(SkeletonText,{width:"60%"}))));var TableWrapper2=theming.styled.table(({theme,compact,inAddonPanel})=>({"&&":{borderSpacing:0,color:theme.color.defaultText,"td, th":{padding:0,border:"none",verticalAlign:"top",textOverflow:"ellipsis"},fontSize:theme.typography.size.s2-1,lineHeight:"20px",textAlign:"left",width:"100%",marginTop:inAddonPanel?0:25,marginBottom:inAddonPanel?0:40,"thead th:first-of-type, td:first-of-type":{width:"25%"},"th:first-of-type, td:first-of-type":{paddingLeft:20},"th:nth-of-type(2), td:nth-of-type(2)":{...compact?null:{width:"35%"}},"td:nth-of-type(3)":{...compact?null:{width:"15%"}},"th:last-of-type, td:last-of-type":{paddingRight:20,...compact?null:{width:"25%"}},th:{color:theme.base==="light"?curriedTransparentize$1(.25,theme.color.defaultText):curriedTransparentize$1(.45,theme.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:"10px",paddingBottom:"10px","&:not(:first-of-type)":{paddingLeft:15,paddingRight:15},"&:last-of-type":{paddingRight:20}},marginLeft:inAddonPanel?0:1,marginRight:inAddonPanel?0:1,tbody:{...inAddonPanel?null:{filter:theme.base==="light"?"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))":"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))"},"> tr > *":{background:theme.background.content,borderTop:`1px solid ${theme.appBorderColor}`},...inAddonPanel?null:{"> tr:first-of-type > *":{borderBlockStart:`1px solid ${theme.appBorderColor}`},"> tr:last-of-type > *":{borderBlockEnd:`1px solid ${theme.appBorderColor}`},"> tr > *:first-of-type":{borderInlineStart:`1px solid ${theme.appBorderColor}`},"> tr > *:last-of-type":{borderInlineEnd:`1px solid ${theme.appBorderColor}`},"> tr:first-of-type > td:first-of-type":{borderTopLeftRadius:theme.appBorderRadius},"> tr:first-of-type > td:last-of-type":{borderTopRightRadius:theme.appBorderRadius},"> tr:last-of-type > td:first-of-type":{borderBottomLeftRadius:theme.appBorderRadius},"> tr:last-of-type > td:last-of-type":{borderBottomRightRadius:theme.appBorderRadius}}}}})),StyledIconButton=theming.styled(components.IconButton)(({theme})=>({margin:"-4px -12px -4px 0"})),ControlHeadingWrapper=theming.styled.span({display:"flex",justifyContent:"space-between"});var sortFns={alpha:(a3,b3)=>(a3.name??"").localeCompare(b3.name??""),requiredFirst:(a3,b3)=>+!!b3.type?.required-+!!a3.type?.required||(a3.name??"").localeCompare(b3.name??""),none:null},groupRows=(rows,sort)=>{let sections={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!rows)return sections;Object.entries(rows).forEach(([key,row])=>{let{category,subcategory}=row?.table||{};if(category){let section=sections.sections[category]||{ungrouped:[],subsections:{}};if(!subcategory)section.ungrouped.push({key,...row});else {let subsection=section.subsections[subcategory]||[];subsection.push({key,...row}),section.subsections[subcategory]=subsection;}sections.sections[category]=section;}else if(subcategory){let subsection=sections.ungroupedSubsections[subcategory]||[];subsection.push({key,...row}),sections.ungroupedSubsections[subcategory]=subsection;}else sections.ungrouped.push({key,...row});});let sortFn=sortFns[sort],sortSubsection=record=>sortFn?Object.keys(record).reduce((acc,cur)=>({...acc,[cur]:record[cur].sort(sortFn)}),{}):record;return {ungrouped:sortFn?sections.ungrouped.sort(sortFn):sections.ungrouped,ungroupedSubsections:sortSubsection(sections.ungroupedSubsections),sections:Object.keys(sections.sections).reduce((acc,cur)=>({...acc,[cur]:{ungrouped:sortFn?sections.sections[cur].ungrouped.sort(sortFn):sections.sections[cur].ungrouped,subsections:sortSubsection(sections.sections[cur].subsections)}}),{})}},safeIncludeConditionalArg=(row,args,globals)=>{try{return csf.includeConditionalArg(row,args,globals)}catch(err){return clientLogger.once.warn(err.message),!1}},ArgsTable=props=>{let{updateArgs,resetArgs,compact,inAddonPanel,initialExpandedArgs,sort="none",isLoading}=props;if("error"in props){let{error}=props;return React20__namespace.default.createElement(EmptyBlock,null,error,"\xA0",React20__namespace.default.createElement(components.Link,{href:"http://storybook.js.org/docs/",target:"_blank",withArrow:!0},React20__namespace.default.createElement(icons.DocumentIcon,null)," Read the docs"))}if(isLoading)return React20__namespace.default.createElement(Skeleton,null);let{rows,args,globals}="rows"in props?props:{rows:void 0,args:void 0,globals:void 0},groups=groupRows(pickBy(rows||{},row=>!row?.table?.disable&&safeIncludeConditionalArg(row,args||{},globals||{})),sort),hasNoUngrouped=groups.ungrouped.length===0,hasNoSections=Object.entries(groups.sections).length===0,hasNoUngroupedSubsections=Object.entries(groups.ungroupedSubsections).length===0;if(hasNoUngrouped&&hasNoSections&&hasNoUngroupedSubsections)return React20__namespace.default.createElement(Empty,{inAddonPanel});let colSpan=1;updateArgs&&(colSpan+=1),compact||(colSpan+=2);let expandable=Object.keys(groups.sections).length>0,common={updateArgs,compact,inAddonPanel,initialExpandedArgs};return React20__namespace.default.createElement(components.ResetWrapper,null,React20__namespace.default.createElement(TableWrapper2,{compact,inAddonPanel,className:"docblock-argstable sb-unstyled"},React20__namespace.default.createElement("thead",{className:"docblock-argstable-head"},React20__namespace.default.createElement("tr",null,React20__namespace.default.createElement("th",null,React20__namespace.default.createElement("span",null,"Name")),compact?null:React20__namespace.default.createElement("th",null,React20__namespace.default.createElement("span",null,"Description")),compact?null:React20__namespace.default.createElement("th",null,React20__namespace.default.createElement("span",null,"Default")),updateArgs?React20__namespace.default.createElement("th",null,React20__namespace.default.createElement(ControlHeadingWrapper,null,"Control"," ",!isLoading&&resetArgs&&React20__namespace.default.createElement(StyledIconButton,{onClick:()=>resetArgs(),title:"Reset controls"},React20__namespace.default.createElement(icons.UndoIcon,{"aria-hidden":!0})))):null)),React20__namespace.default.createElement("tbody",{className:"docblock-argstable-body"},groups.ungrouped.map(row=>React20__namespace.default.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],...common})),Object.entries(groups.ungroupedSubsections).map(([subcategory,subsection])=>React20__namespace.default.createElement(SectionRow,{key:subcategory,label:subcategory,level:"subsection",colSpan},subsection.map(row=>React20__namespace.default.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],expandable,...common})))),Object.entries(groups.sections).map(([category,section])=>React20__namespace.default.createElement(SectionRow,{key:category,label:category,level:"section",colSpan},section.ungrouped.map(row=>React20__namespace.default.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],...common})),Object.entries(section.subsections).map(([subcategory,subsection])=>React20__namespace.default.createElement(SectionRow,{key:subcategory,label:subcategory,level:"subsection",colSpan},subsection.map(row=>React20__namespace.default.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],expandable,...common})))))))))};var anchorBlockIdFromId=storyId=>`anchor--${storyId}`,Anchor=({storyId,children})=>React20__namespace.default.createElement("div",{id:anchorBlockIdFromId(storyId),className:"sb-anchor"},children);globalThis&&globalThis.__DOCS_CONTEXT__===void 0&&(globalThis.__DOCS_CONTEXT__=React20.createContext(null),globalThis.__DOCS_CONTEXT__.displayName="DocsContext");var DocsContext=globalThis?globalThis.__DOCS_CONTEXT__:React20.createContext(null);var useOf=(moduleExportOrType,validTypes)=>React20.useContext(DocsContext).resolveOf(moduleExportOrType,validTypes);var titleCase=str=>str.split("-").map(part=>part.charAt(0).toUpperCase()+part.slice(1)).join(""),getComponentName=component=>{if(component)return typeof component=="string"?component.includes("-")?titleCase(component):component:component.__docgenInfo&&component.__docgenInfo.displayName?component.__docgenInfo.displayName:component.name};function scrollToElement(element,block="start"){element.scrollIntoView({behavior:"smooth",block,inline:"nearest"});}function extractComponentArgTypes(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error("Args unsupported. See Args documentation for your framework.");return extractArgTypes(component)}function getArgTypesFromResolved(resolved){if(resolved.type==="component"){let{component:component2,projectAnnotations:{parameters:parameters2}}=resolved;return {argTypes:extractComponentArgTypes(component2,parameters2),parameters:parameters2,component:component2}}if(resolved.type==="meta"){let{preparedMeta:{argTypes:argTypes2,parameters:parameters2,component:component2,subcomponents:subcomponents2}}=resolved;return {argTypes:argTypes2,parameters:parameters2,component:component2,subcomponents:subcomponents2}}let{story:{argTypes,parameters,component,subcomponents}}=resolved;return {argTypes,parameters,component,subcomponents}}var ArgTypes=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let resolved=useOf(of||"meta"),{argTypes,parameters,component,subcomponents}=getArgTypesFromResolved(resolved),argTypesParameters=parameters?.docs?.argTypes||{},include=props.include??argTypesParameters.include,exclude=props.exclude??argTypesParameters.exclude,sort=props.sort??argTypesParameters.sort,filteredArgTypes=previewApi.filterArgTypes(argTypes,include,exclude);if(!(!!subcomponents&&Object.keys(subcomponents||{}).length>0))return React20__namespace.default.createElement(ArgsTable,{rows:filteredArgTypes,sort});let mainComponentName=getComponentName(component)||"Main",subcomponentTabs=Object.fromEntries(Object.entries(subcomponents||{}).map(([key,comp])=>[key,{rows:previewApi.filterArgTypes(extractComponentArgTypes(comp,parameters),include,exclude),sort}])),tabs={[mainComponentName]:{rows:filteredArgTypes,sort},...subcomponentTabs};return React20__namespace.default.createElement(TabbedArgsTable,{tabs,sort})};var __create2=Object.create,__defProp2=Object.defineProperty,__getOwnPropDesc2=Object.getOwnPropertyDescriptor,__getOwnPropNames2=Object.getOwnPropertyNames,__getProtoOf2=Object.getPrototypeOf,__hasOwnProp2=Object.prototype.hasOwnProperty,__commonJS2=(cb,mod)=>function(){return mod||(0, cb[__getOwnPropNames2(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports},__copyProps2=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames2(from))!__hasOwnProp2.call(to,key)&&key!==except&&__defProp2(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc2(from,key))||desc.enumerable});return to},__toESM2=(mod,isNodeMode,target)=>(target=mod!=null?__create2(__getProtoOf2(mod)):{},__copyProps2(isNodeMode||!mod||!mod.__esModule?__defProp2(target,"default",{value:mod,enumerable:!0}):target,mod)),eventProperties=["bubbles","cancelBubble","cancelable","composed","currentTarget","defaultPrevented","eventPhase","isTrusted","returnValue","srcElement","target","timeStamp","type"],customEventSpecificProperties=["detail"];function extractEventHiddenProperties(event){let rebuildEvent=eventProperties.filter(value2=>event[value2]!==void 0).reduce((acc,value2)=>(acc[value2]=event[value2],acc),{});if(event instanceof CustomEvent)for(let value2 of customEventSpecificProperties.filter(value22=>event[value22]!==void 0))rebuildEvent[value2]=event[value2];return rebuildEvent}var require_es_object_atoms=__commonJS2({"node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js"(exports,module){module.exports=Object;}}),require_es_errors=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/index.js"(exports,module){module.exports=Error;}}),require_eval=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/eval.js"(exports,module){module.exports=EvalError;}}),require_range=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/range.js"(exports,module){module.exports=RangeError;}}),require_ref=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/ref.js"(exports,module){module.exports=ReferenceError;}}),require_syntax=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/syntax.js"(exports,module){module.exports=SyntaxError;}}),require_type=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/type.js"(exports,module){module.exports=TypeError;}}),require_uri=__commonJS2({"node_modules/.pnpm/es-errors@1.3.0/node_modules/es-errors/uri.js"(exports,module){module.exports=URIError;}}),require_abs=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/abs.js"(exports,module){module.exports=Math.abs;}}),require_floor=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/floor.js"(exports,module){module.exports=Math.floor;}}),require_max=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/max.js"(exports,module){module.exports=Math.max;}}),require_min=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/min.js"(exports,module){module.exports=Math.min;}}),require_pow=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/pow.js"(exports,module){module.exports=Math.pow;}}),require_round=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/round.js"(exports,module){module.exports=Math.round;}}),require_isNaN=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/isNaN.js"(exports,module){module.exports=Number.isNaN||function(a3){return a3!==a3};}}),require_sign=__commonJS2({"node_modules/.pnpm/math-intrinsics@1.1.0/node_modules/math-intrinsics/sign.js"(exports,module){var $isNaN=require_isNaN();module.exports=function(number){return $isNaN(number)||number===0?number:number<0?-1:1};}}),require_gOPD=__commonJS2({"node_modules/.pnpm/gopd@1.2.0/node_modules/gopd/gOPD.js"(exports,module){module.exports=Object.getOwnPropertyDescriptor;}}),require_gopd=__commonJS2({"node_modules/.pnpm/gopd@1.2.0/node_modules/gopd/index.js"(exports,module){var $gOPD=require_gOPD();if($gOPD)try{$gOPD([],"length");}catch{$gOPD=null;}module.exports=$gOPD;}}),require_es_define_property=__commonJS2({"node_modules/.pnpm/es-define-property@1.0.1/node_modules/es-define-property/index.js"(exports,module){var $defineProperty=Object.defineProperty||!1;if($defineProperty)try{$defineProperty({},"a",{value:1});}catch{$defineProperty=!1;}module.exports=$defineProperty;}}),require_shams=__commonJS2({"node_modules/.pnpm/has-symbols@1.1.0/node_modules/has-symbols/shams.js"(exports,module){module.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return !1;if(typeof Symbol.iterator=="symbol")return !0;var obj={},sym=Symbol("test"),symObj=Object(sym);if(typeof sym=="string"||Object.prototype.toString.call(sym)!=="[object Symbol]"||Object.prototype.toString.call(symObj)!=="[object Symbol]")return !1;var symVal=42;obj[sym]=symVal;for(var _3 in obj)return !1;if(typeof Object.keys=="function"&&Object.keys(obj).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(obj).length!==0)return !1;var syms=Object.getOwnPropertySymbols(obj);if(syms.length!==1||syms[0]!==sym||!Object.prototype.propertyIsEnumerable.call(obj,sym))return !1;if(typeof Object.getOwnPropertyDescriptor=="function"){var descriptor=Object.getOwnPropertyDescriptor(obj,sym);if(descriptor.value!==symVal||descriptor.enumerable!==!0)return !1}return !0};}}),require_has_symbols=__commonJS2({"node_modules/.pnpm/has-symbols@1.1.0/node_modules/has-symbols/index.js"(exports,module){var origSymbol=typeof Symbol<"u"&&Symbol,hasSymbolSham=require_shams();module.exports=function(){return typeof origSymbol!="function"||typeof Symbol!="function"||typeof origSymbol("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:hasSymbolSham()};}}),require_Reflect_getPrototypeOf=__commonJS2({"node_modules/.pnpm/get-proto@1.0.1/node_modules/get-proto/Reflect.getPrototypeOf.js"(exports,module){module.exports=typeof Reflect<"u"&&Reflect.getPrototypeOf||null;}}),require_Object_getPrototypeOf=__commonJS2({"node_modules/.pnpm/get-proto@1.0.1/node_modules/get-proto/Object.getPrototypeOf.js"(exports,module){var $Object=require_es_object_atoms();module.exports=$Object.getPrototypeOf||null;}}),require_implementation=__commonJS2({"node_modules/.pnpm/function-bind@1.1.2/node_modules/function-bind/implementation.js"(exports,module){var ERROR_MESSAGE="Function.prototype.bind called on incompatible ",toStr=Object.prototype.toString,max=Math.max,funcType="[object Function]",concatty=function(a3,b3){for(var arr=[],i3=0;i3<a3.length;i3+=1)arr[i3]=a3[i3];for(var j2=0;j2<b3.length;j2+=1)arr[j2+a3.length]=b3[j2];return arr},slicy=function(arrLike,offset){for(var arr=[],i3=offset||0,j2=0;i3<arrLike.length;i3+=1,j2+=1)arr[j2]=arrLike[i3];return arr},joiny=function(arr,joiner){for(var str="",i3=0;i3<arr.length;i3+=1)str+=arr[i3],i3+1<arr.length&&(str+=joiner);return str};module.exports=function(that){var target=this;if(typeof target!="function"||toStr.apply(target)!==funcType)throw new TypeError(ERROR_MESSAGE+target);for(var args=slicy(arguments,1),bound,binder=function(){if(this instanceof bound){var result=target.apply(this,concatty(args,arguments));return Object(result)===result?result:this}return target.apply(that,concatty(args,arguments))},boundLength=max(0,target.length-args.length),boundArgs=[],i3=0;i3<boundLength;i3++)boundArgs[i3]="$"+i3;if(bound=Function("binder","return function ("+joiny(boundArgs,",")+"){ return binder.apply(this,arguments); }")(binder),target.prototype){var Empty2=function(){};Empty2.prototype=target.prototype,bound.prototype=new Empty2,Empty2.prototype=null;}return bound};}}),require_function_bind=__commonJS2({"node_modules/.pnpm/function-bind@1.1.2/node_modules/function-bind/index.js"(exports,module){var implementation=require_implementation();module.exports=Function.prototype.bind||implementation;}}),require_functionCall=__commonJS2({"node_modules/.pnpm/call-bind-apply-helpers@1.0.2/node_modules/call-bind-apply-helpers/functionCall.js"(exports,module){module.exports=Function.prototype.call;}}),require_functionApply=__commonJS2({"node_modules/.pnpm/call-bind-apply-helpers@1.0.2/node_modules/call-bind-apply-helpers/functionApply.js"(exports,module){module.exports=Function.prototype.apply;}}),require_reflectApply=__commonJS2({"node_modules/.pnpm/call-bind-apply-helpers@1.0.2/node_modules/call-bind-apply-helpers/reflectApply.js"(exports,module){module.exports=typeof Reflect<"u"&&Reflect&&Reflect.apply;}}),require_actualApply=__commonJS2({"node_modules/.pnpm/call-bind-apply-helpers@1.0.2/node_modules/call-bind-apply-helpers/actualApply.js"(exports,module){var bind=require_function_bind(),$apply=require_functionApply(),$call=require_functionCall(),$reflectApply=require_reflectApply();module.exports=$reflectApply||bind.call($call,$apply);}}),require_call_bind_apply_helpers=__commonJS2({"node_modules/.pnpm/call-bind-apply-helpers@1.0.2/node_modules/call-bind-apply-helpers/index.js"(exports,module){var bind=require_function_bind(),$TypeError=require_type(),$call=require_functionCall(),$actualApply=require_actualApply();module.exports=function(args){if(args.length<1||typeof args[0]!="function")throw new $TypeError("a function is required");return $actualApply(bind,$call,args)};}}),require_get=__commonJS2({"node_modules/.pnpm/dunder-proto@1.0.1/node_modules/dunder-proto/get.js"(exports,module){var callBind=require_call_bind_apply_helpers(),gOPD=require_gopd(),hasProtoAccessor;try{hasProtoAccessor=[].__proto__===Array.prototype;}catch(e3){if(!e3||typeof e3!="object"||!("code"in e3)||e3.code!=="ERR_PROTO_ACCESS")throw e3}var desc=!!hasProtoAccessor&&gOPD&&gOPD(Object.prototype,"__proto__"),$Object=Object,$getPrototypeOf=$Object.getPrototypeOf;module.exports=desc&&typeof desc.get=="function"?callBind([desc.get]):typeof $getPrototypeOf=="function"?function(value2){return $getPrototypeOf(value2==null?value2:$Object(value2))}:!1;}}),require_get_proto=__commonJS2({"node_modules/.pnpm/get-proto@1.0.1/node_modules/get-proto/index.js"(exports,module){var reflectGetProto=require_Reflect_getPrototypeOf(),originalGetProto=require_Object_getPrototypeOf(),getDunderProto=require_get();module.exports=reflectGetProto?function(O2){return reflectGetProto(O2)}:originalGetProto?function(O2){if(!O2||typeof O2!="object"&&typeof O2!="function")throw new TypeError("getProto: not an object");return originalGetProto(O2)}:getDunderProto?function(O2){return getDunderProto(O2)}:null;}}),require_hasown=__commonJS2({"node_modules/.pnpm/hasown@2.0.2/node_modules/hasown/index.js"(exports,module){var call=Function.prototype.call,$hasOwn=Object.prototype.hasOwnProperty,bind=require_function_bind();module.exports=bind.call(call,$hasOwn);}}),require_get_intrinsic=__commonJS2({"node_modules/.pnpm/get-intrinsic@1.3.0/node_modules/get-intrinsic/index.js"(exports,module){var undefined2,$Object=require_es_object_atoms(),$Error=require_es_errors(),$EvalError=require_eval(),$RangeError=require_range(),$ReferenceError=require_ref(),$SyntaxError=require_syntax(),$TypeError=require_type(),$URIError=require_uri(),abs=require_abs(),floor=require_floor(),max=require_max(),min=require_min(),pow=require_pow(),round=require_round(),sign=require_sign(),$Function=Function,getEvalledConstructor=function(expressionSyntax){try{return $Function('"use strict"; return ('+expressionSyntax+").constructor;")()}catch{}},$gOPD=require_gopd(),$defineProperty=require_es_define_property(),throwTypeError=function(){throw new $TypeError},ThrowTypeError=$gOPD?function(){try{return arguments.callee,throwTypeError}catch{try{return $gOPD(arguments,"callee").get}catch{return throwTypeError}}}():throwTypeError,hasSymbols=require_has_symbols()(),getProto=require_get_proto(),$ObjectGPO=require_Object_getPrototypeOf(),$ReflectGPO=require_Reflect_getPrototypeOf(),$apply=require_functionApply(),$call=require_functionCall(),needsEval={},TypedArray=typeof Uint8Array>"u"||!getProto?undefined2:getProto(Uint8Array),INTRINSICS={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?undefined2:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?undefined2:ArrayBuffer,"%ArrayIteratorPrototype%":hasSymbols&&getProto?getProto([][Symbol.iterator]()):undefined2,"%AsyncFromSyncIteratorPrototype%":undefined2,"%AsyncFunction%":needsEval,"%AsyncGenerator%":needsEval,"%AsyncGeneratorFunction%":needsEval,"%AsyncIteratorPrototype%":needsEval,"%Atomics%":typeof Atomics>"u"?undefined2:Atomics,"%BigInt%":typeof BigInt>"u"?undefined2:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?undefined2:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?undefined2:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?undefined2:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":$Error,"%eval%":eval,"%EvalError%":$EvalError,"%Float16Array%":typeof Float16Array>"u"?undefined2:Float16Array,"%Float32Array%":typeof Float32Array>"u"?undefined2:Float32Array,"%Float64Array%":typeof Float64Array>"u"?undefined2:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?undefined2:FinalizationRegistry,"%Function%":$Function,"%GeneratorFunction%":needsEval,"%Int8Array%":typeof Int8Array>"u"?undefined2:Int8Array,"%Int16Array%":typeof Int16Array>"u"?undefined2:Int16Array,"%Int32Array%":typeof Int32Array>"u"?undefined2:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":hasSymbols&&getProto?getProto(getProto([][Symbol.iterator]())):undefined2,"%JSON%":typeof JSON=="object"?JSON:undefined2,"%Map%":typeof Map>"u"?undefined2:Map,"%MapIteratorPrototype%":typeof Map>"u"||!hasSymbols||!getProto?undefined2:getProto(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":$Object,"%Object.getOwnPropertyDescriptor%":$gOPD,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?undefined2:Promise,"%Proxy%":typeof Proxy>"u"?undefined2:Proxy,"%RangeError%":$RangeError,"%ReferenceError%":$ReferenceError,"%Reflect%":typeof Reflect>"u"?undefined2:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?undefined2:Set,"%SetIteratorPrototype%":typeof Set>"u"||!hasSymbols||!getProto?undefined2:getProto(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?undefined2:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":hasSymbols&&getProto?getProto(""[Symbol.iterator]()):undefined2,"%Symbol%":hasSymbols?Symbol:undefined2,"%SyntaxError%":$SyntaxError,"%ThrowTypeError%":ThrowTypeError,"%TypedArray%":TypedArray,"%TypeError%":$TypeError,"%Uint8Array%":typeof Uint8Array>"u"?undefined2:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?undefined2:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?undefined2:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?undefined2:Uint32Array,"%URIError%":$URIError,"%WeakMap%":typeof WeakMap>"u"?undefined2:WeakMap,"%WeakRef%":typeof WeakRef>"u"?undefined2:WeakRef,"%WeakSet%":typeof WeakSet>"u"?undefined2:WeakSet,"%Function.prototype.call%":$call,"%Function.prototype.apply%":$apply,"%Object.defineProperty%":$defineProperty,"%Object.getPrototypeOf%":$ObjectGPO,"%Math.abs%":abs,"%Math.floor%":floor,"%Math.max%":max,"%Math.min%":min,"%Math.pow%":pow,"%Math.round%":round,"%Math.sign%":sign,"%Reflect.getPrototypeOf%":$ReflectGPO};if(getProto)try{null.error;}catch(e3){errorProto=getProto(getProto(e3)),INTRINSICS["%Error.prototype%"]=errorProto;}var errorProto,doEval=function doEval2(name){var value2;if(name==="%AsyncFunction%")value2=getEvalledConstructor("async function () {}");else if(name==="%GeneratorFunction%")value2=getEvalledConstructor("function* () {}");else if(name==="%AsyncGeneratorFunction%")value2=getEvalledConstructor("async function* () {}");else if(name==="%AsyncGenerator%"){var fn=doEval2("%AsyncGeneratorFunction%");fn&&(value2=fn.prototype);}else if(name==="%AsyncIteratorPrototype%"){var gen=doEval2("%AsyncGenerator%");gen&&getProto&&(value2=getProto(gen.prototype));}return INTRINSICS[name]=value2,value2},LEGACY_ALIASES={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},bind=require_function_bind(),hasOwn=require_hasown(),$concat=bind.call($call,Array.prototype.concat),$spliceApply=bind.call($apply,Array.prototype.splice),$replace=bind.call($call,String.prototype.replace),$strSlice=bind.call($call,String.prototype.slice),$exec=bind.call($call,RegExp.prototype.exec),rePropName2=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,reEscapeChar2=/\\(\\)?/g,stringToPath2=function(string){var first=$strSlice(string,0,1),last=$strSlice(string,-1);if(first==="%"&&last!=="%")throw new $SyntaxError("invalid intrinsic syntax, expected closing `%`");if(last==="%"&&first!=="%")throw new $SyntaxError("invalid intrinsic syntax, expected opening `%`");var result=[];return $replace(string,rePropName2,function(match,number,quote,subString){result[result.length]=quote?$replace(subString,reEscapeChar2,"$1"):number||match;}),result},getBaseIntrinsic=function(name,allowMissing){var intrinsicName=name,alias;if(hasOwn(LEGACY_ALIASES,intrinsicName)&&(alias=LEGACY_ALIASES[intrinsicName],intrinsicName="%"+alias[0]+"%"),hasOwn(INTRINSICS,intrinsicName)){var value2=INTRINSICS[intrinsicName];if(value2===needsEval&&(value2=doEval(intrinsicName)),typeof value2>"u"&&!allowMissing)throw new $TypeError("intrinsic "+name+" exists, but is not available. Please file an issue!");return {alias,name:intrinsicName,value:value2}}throw new $SyntaxError("intrinsic "+name+" does not exist!")};module.exports=function(name,allowMissing){if(typeof name!="string"||name.length===0)throw new $TypeError("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof allowMissing!="boolean")throw new $TypeError('"allowMissing" argument must be a boolean');if($exec(/^%?[^%]*%?$/,name)===null)throw new $SyntaxError("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var parts=stringToPath2(name),intrinsicBaseName=parts.length>0?parts[0]:"",intrinsic=getBaseIntrinsic("%"+intrinsicBaseName+"%",allowMissing),intrinsicRealName=intrinsic.name,value2=intrinsic.value,skipFurtherCaching=!1,alias=intrinsic.alias;alias&&(intrinsicBaseName=alias[0],$spliceApply(parts,$concat([0,1],alias)));for(var i3=1,isOwn=!0;i3<parts.length;i3+=1){var part=parts[i3],first=$strSlice(part,0,1),last=$strSlice(part,-1);if((first==='"'||first==="'"||first==="`"||last==='"'||last==="'"||last==="`")&&first!==last)throw new $SyntaxError("property names with quotes must have matching quotes");if((part==="constructor"||!isOwn)&&(skipFurtherCaching=!0),intrinsicBaseName+="."+part,intrinsicRealName="%"+intrinsicBaseName+"%",hasOwn(INTRINSICS,intrinsicRealName))value2=INTRINSICS[intrinsicRealName];else if(value2!=null){if(!(part in value2)){if(!allowMissing)throw new $TypeError("base intrinsic for "+name+" exists, but the property is not available.");return}if($gOPD&&i3+1>=parts.length){var desc=$gOPD(value2,part);isOwn=!!desc,isOwn&&"get"in desc&&!("originalValue"in desc.get)?value2=desc.get:value2=value2[part];}else isOwn=hasOwn(value2,part),value2=value2[part];isOwn&&!skipFurtherCaching&&(INTRINSICS[intrinsicRealName]=value2);}}return value2};}}),require_call_bound=__commonJS2({"node_modules/.pnpm/call-bound@1.0.4/node_modules/call-bound/index.js"(exports,module){var GetIntrinsic=require_get_intrinsic(),callBindBasic=require_call_bind_apply_helpers(),$indexOf=callBindBasic([GetIntrinsic("%String.prototype.indexOf%")]);module.exports=function(name,allowMissing){var intrinsic=GetIntrinsic(name,!!allowMissing);return typeof intrinsic=="function"&&$indexOf(name,".prototype.")>-1?callBindBasic([intrinsic]):intrinsic};}}),require_shams2=__commonJS2({"node_modules/.pnpm/has-tostringtag@1.0.2/node_modules/has-tostringtag/shams.js"(exports,module){var hasSymbols=require_shams();module.exports=function(){return hasSymbols()&&!!Symbol.toStringTag};}}),require_is_regex=__commonJS2({"node_modules/.pnpm/is-regex@1.2.1/node_modules/is-regex/index.js"(exports,module){var callBound=require_call_bound(),hasToStringTag=require_shams2()(),hasOwn=require_hasown(),gOPD=require_gopd(),fn;hasToStringTag?($exec=callBound("RegExp.prototype.exec"),isRegexMarker={},throwRegexMarker=function(){throw isRegexMarker},badStringifier={toString:throwRegexMarker,valueOf:throwRegexMarker},typeof Symbol.toPrimitive=="symbol"&&(badStringifier[Symbol.toPrimitive]=throwRegexMarker),fn=function(value2){if(!value2||typeof value2!="object")return !1;var descriptor=gOPD(value2,"lastIndex"),hasLastIndexDataProperty=descriptor&&hasOwn(descriptor,"value");if(!hasLastIndexDataProperty)return !1;try{$exec(value2,badStringifier);}catch(e3){return e3===isRegexMarker}}):($toString=callBound("Object.prototype.toString"),regexClass="[object RegExp]",fn=function(value2){return !value2||typeof value2!="object"&&typeof value2!="function"?!1:$toString(value2)===regexClass});var $exec,isRegexMarker,throwRegexMarker,badStringifier,$toString,regexClass;module.exports=fn;}}),require_is_function=__commonJS2({"node_modules/.pnpm/is-function@1.0.2/node_modules/is-function/index.js"(exports,module){module.exports=isFunction3;var toString2=Object.prototype.toString;function isFunction3(fn){if(!fn)return !1;var string=toString2.call(fn);return string==="[object Function]"||typeof fn=="function"&&string!=="[object RegExp]"||typeof window<"u"&&(fn===window.setTimeout||fn===window.alert||fn===window.confirm||fn===window.prompt)}}}),require_safe_regex_test=__commonJS2({"node_modules/.pnpm/safe-regex-test@1.1.0/node_modules/safe-regex-test/index.js"(exports,module){var callBound=require_call_bound(),isRegex=require_is_regex(),$exec=callBound("RegExp.prototype.exec"),$TypeError=require_type();module.exports=function(regex2){if(!isRegex(regex2))throw new $TypeError("`regex` must be a RegExp");return function(s3){return $exec(regex2,s3)!==null}};}}),require_is_symbol=__commonJS2({"node_modules/.pnpm/is-symbol@1.1.1/node_modules/is-symbol/index.js"(exports,module){var callBound=require_call_bound(),$toString=callBound("Object.prototype.toString"),hasSymbols=require_has_symbols()(),safeRegexTest=require_safe_regex_test();hasSymbols?($symToStr=callBound("Symbol.prototype.toString"),isSymString=safeRegexTest(/^Symbol\(.*\)$/),isSymbolObject=function(value2){return typeof value2.valueOf()!="symbol"?!1:isSymString($symToStr(value2))},module.exports=function(value2){if(typeof value2=="symbol")return !0;if(!value2||typeof value2!="object"||$toString(value2)!=="[object Symbol]")return !1;try{return isSymbolObject(value2)}catch{return !1}}):module.exports=function(value2){return !1};var $symToStr,isSymString,isSymbolObject;}}),import_is_regex=__toESM2(require_is_regex()),import_is_function=__toESM2(require_is_function()),import_is_symbol=__toESM2(require_is_symbol());function isObject(val){return val!=null&&typeof val=="object"&&Array.isArray(val)===!1}var freeGlobal=typeof global=="object"&&global&&global.Object===Object&&global,freeGlobal_default=freeGlobal,freeSelf=typeof self=="object"&&self&&self.Object===Object&&self,root=freeGlobal_default||freeSelf||Function("return this")(),root_default=root,Symbol2=root_default.Symbol,Symbol_default=Symbol2,objectProto=Object.prototype,hasOwnProperty=objectProto.hasOwnProperty,nativeObjectToString=objectProto.toString,symToStringTag=Symbol_default?Symbol_default.toStringTag:void 0;function getRawTag(value2){var isOwn=hasOwnProperty.call(value2,symToStringTag),tag=value2[symToStringTag];try{value2[symToStringTag]=void 0;var unmasked=!0;}catch{}var result=nativeObjectToString.call(value2);return unmasked&&(isOwn?value2[symToStringTag]=tag:delete value2[symToStringTag]),result}var getRawTag_default=getRawTag,objectProto2=Object.prototype,nativeObjectToString2=objectProto2.toString;function objectToString(value2){return nativeObjectToString2.call(value2)}var objectToString_default=objectToString,nullTag="[object Null]",undefinedTag="[object Undefined]",symToStringTag2=Symbol_default?Symbol_default.toStringTag:void 0;function baseGetTag(value2){return value2==null?value2===void 0?undefinedTag:nullTag:symToStringTag2&&symToStringTag2 in Object(value2)?getRawTag_default(value2):objectToString_default(value2)}var baseGetTag_default=baseGetTag;var symbolProto=Symbol_default?Symbol_default.prototype:void 0;symbolProto?symbolProto.toString:void 0;function isObject2(value2){var type=typeof value2;return value2!=null&&(type=="object"||type=="function")}var isObject_default=isObject2,asyncTag="[object AsyncFunction]",funcTag="[object Function]",genTag="[object GeneratorFunction]",proxyTag="[object Proxy]";function isFunction(value2){if(!isObject_default(value2))return !1;var tag=baseGetTag_default(value2);return tag==funcTag||tag==genTag||tag==asyncTag||tag==proxyTag}var isFunction_default=isFunction,coreJsData=root_default["__core-js_shared__"],coreJsData_default=coreJsData,maskSrcKey=function(){var uid=/[^.]+$/.exec(coreJsData_default&&coreJsData_default.keys&&coreJsData_default.keys.IE_PROTO||"");return uid?"Symbol(src)_1."+uid:""}();function isMasked(func){return !!maskSrcKey&&maskSrcKey in func}var isMasked_default=isMasked,funcProto=Function.prototype,funcToString=funcProto.toString;function toSource(func){if(func!=null){try{return funcToString.call(func)}catch{}try{return func+""}catch{}}return ""}var toSource_default=toSource,reRegExpChar=/[\\^$.*+?()[\]{}|]/g,reIsHostCtor=/^\[object .+?Constructor\]$/,funcProto2=Function.prototype,objectProto3=Object.prototype,funcToString2=funcProto2.toString,hasOwnProperty2=objectProto3.hasOwnProperty,reIsNative=RegExp("^"+funcToString2.call(hasOwnProperty2).replace(reRegExpChar,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function baseIsNative(value2){if(!isObject_default(value2)||isMasked_default(value2))return !1;var pattern=isFunction_default(value2)?reIsNative:reIsHostCtor;return pattern.test(toSource_default(value2))}var baseIsNative_default=baseIsNative;function getValue(object2,key){return object2?.[key]}var getValue_default=getValue;function getNative(object2,key){var value2=getValue_default(object2,key);return baseIsNative_default(value2)?value2:void 0}var getNative_default=getNative;function eq(value2,other){return value2===other||value2!==value2&&other!==other}var eq_default=eq;var nativeCreate=getNative_default(Object,"create"),nativeCreate_default=nativeCreate;function hashClear(){this.__data__=nativeCreate_default?nativeCreate_default(null):{},this.size=0;}var hashClear_default=hashClear;function hashDelete(key){var result=this.has(key)&&delete this.__data__[key];return this.size-=result?1:0,result}var hashDelete_default=hashDelete,HASH_UNDEFINED="__lodash_hash_undefined__",objectProto4=Object.prototype,hasOwnProperty3=objectProto4.hasOwnProperty;function hashGet(key){var data=this.__data__;if(nativeCreate_default){var result=data[key];return result===HASH_UNDEFINED?void 0:result}return hasOwnProperty3.call(data,key)?data[key]:void 0}var hashGet_default=hashGet,objectProto5=Object.prototype,hasOwnProperty4=objectProto5.hasOwnProperty;function hashHas(key){var data=this.__data__;return nativeCreate_default?data[key]!==void 0:hasOwnProperty4.call(data,key)}var hashHas_default=hashHas,HASH_UNDEFINED2="__lodash_hash_undefined__";function hashSet(key,value2){var data=this.__data__;return this.size+=this.has(key)?0:1,data[key]=nativeCreate_default&&value2===void 0?HASH_UNDEFINED2:value2,this}var hashSet_default=hashSet;function Hash(entries){var index=-1,length=entries==null?0:entries.length;for(this.clear();++index<length;){var entry=entries[index];this.set(entry[0],entry[1]);}}Hash.prototype.clear=hashClear_default;Hash.prototype.delete=hashDelete_default;Hash.prototype.get=hashGet_default;Hash.prototype.has=hashHas_default;Hash.prototype.set=hashSet_default;var Hash_default=Hash;function listCacheClear(){this.__data__=[],this.size=0;}var listCacheClear_default=listCacheClear;function assocIndexOf(array2,key){for(var length=array2.length;length--;)if(eq_default(array2[length][0],key))return length;return -1}var assocIndexOf_default=assocIndexOf,arrayProto=Array.prototype,splice=arrayProto.splice;function listCacheDelete(key){var data=this.__data__,index=assocIndexOf_default(data,key);if(index<0)return !1;var lastIndex=data.length-1;return index==lastIndex?data.pop():splice.call(data,index,1),--this.size,!0}var listCacheDelete_default=listCacheDelete;function listCacheGet(key){var data=this.__data__,index=assocIndexOf_default(data,key);return index<0?void 0:data[index][1]}var listCacheGet_default=listCacheGet;function listCacheHas(key){return assocIndexOf_default(this.__data__,key)>-1}var listCacheHas_default=listCacheHas;function listCacheSet(key,value2){var data=this.__data__,index=assocIndexOf_default(data,key);return index<0?(++this.size,data.push([key,value2])):data[index][1]=value2,this}var listCacheSet_default=listCacheSet;function ListCache(entries){var index=-1,length=entries==null?0:entries.length;for(this.clear();++index<length;){var entry=entries[index];this.set(entry[0],entry[1]);}}ListCache.prototype.clear=listCacheClear_default;ListCache.prototype.delete=listCacheDelete_default;ListCache.prototype.get=listCacheGet_default;ListCache.prototype.has=listCacheHas_default;ListCache.prototype.set=listCacheSet_default;var ListCache_default=ListCache,Map2=getNative_default(root_default,"Map"),Map_default=Map2;function mapCacheClear(){this.size=0,this.__data__={hash:new Hash_default,map:new(Map_default||ListCache_default),string:new Hash_default};}var mapCacheClear_default=mapCacheClear;function isKeyable(value2){var type=typeof value2;return type=="string"||type=="number"||type=="symbol"||type=="boolean"?value2!=="__proto__":value2===null}var isKeyable_default=isKeyable;function getMapData(map,key){var data=map.__data__;return isKeyable_default(key)?data[typeof key=="string"?"string":"hash"]:data.map}var getMapData_default=getMapData;function mapCacheDelete(key){var result=getMapData_default(this,key).delete(key);return this.size-=result?1:0,result}var mapCacheDelete_default=mapCacheDelete;function mapCacheGet(key){return getMapData_default(this,key).get(key)}var mapCacheGet_default=mapCacheGet;function mapCacheHas(key){return getMapData_default(this,key).has(key)}var mapCacheHas_default=mapCacheHas;function mapCacheSet(key,value2){var data=getMapData_default(this,key),size=data.size;return data.set(key,value2),this.size+=data.size==size?0:1,this}var mapCacheSet_default=mapCacheSet;function MapCache(entries){var index=-1,length=entries==null?0:entries.length;for(this.clear();++index<length;){var entry=entries[index];this.set(entry[0],entry[1]);}}MapCache.prototype.clear=mapCacheClear_default;MapCache.prototype.delete=mapCacheDelete_default;MapCache.prototype.get=mapCacheGet_default;MapCache.prototype.has=mapCacheHas_default;MapCache.prototype.set=mapCacheSet_default;var MapCache_default=MapCache,FUNC_ERROR_TEXT="Expected a function";function memoize2(func,resolver){if(typeof func!="function"||resolver!=null&&typeof resolver!="function")throw new TypeError(FUNC_ERROR_TEXT);var memoized=function(){var args=arguments,key=resolver?resolver.apply(this,args):args[0],cache=memoized.cache;if(cache.has(key))return cache.get(key);var result=func.apply(this,args);return memoized.cache=cache.set(key,result)||cache,result};return memoized.cache=new(memoize2.Cache||MapCache_default),memoized}memoize2.Cache=MapCache_default;var memoize_default=memoize2,MAX_MEMOIZE_SIZE=500;function memoizeCapped(func){var result=memoize_default(func,function(key){return cache.size===MAX_MEMOIZE_SIZE&&cache.clear(),key}),cache=result.cache;return result}var memoizeCapped_default=memoizeCapped,rePropName=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,reEscapeChar=/\\(\\)?/g;memoizeCapped_default(function(string){var result=[];return string.charCodeAt(0)===46&&result.push(""),string.replace(rePropName,function(match,number,quote,subString){result.push(quote?subString.replace(reEscapeChar,"$1"):number||match);}),result});var isObject3=isObject,dateFormat=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/;function convertUnconventionalData(data){if(!isObject3(data))return data;let result=data,wasMutated=!1;return typeof Event<"u"&&data instanceof Event&&(result=extractEventHiddenProperties(result),wasMutated=!0),result=Object.keys(result).reduce((acc,key)=>{try{result[key]&&result[key].toJSON,acc[key]=result[key];}catch{wasMutated=!0;}return acc},{}),wasMutated?result:data}var replacer=function(options){let objects,map,stack,keys;return function(key,value2){try{if(key==="")return keys=[],objects=new Map([[value2,"[]"]]),map=new Map,stack=[],value2;let origin=map.get(this)||this;for(;stack.length&&origin!==stack[0];)stack.shift(),keys.pop();if(typeof value2=="boolean")return value2;if(value2===void 0)return options.allowUndefined?"_undefined_":void 0;if(value2===null)return null;if(typeof value2=="number")return value2===Number.NEGATIVE_INFINITY?"_-Infinity_":value2===Number.POSITIVE_INFINITY?"_Infinity_":Number.isNaN(value2)?"_NaN_":value2;if(typeof value2=="bigint")return `_bigint_${value2.toString()}`;if(typeof value2=="string")return dateFormat.test(value2)?options.allowDate?`_date_${value2}`:void 0:value2;if((0,import_is_regex.default)(value2))return options.allowRegExp?`_regexp_${value2.flags}|${value2.source}`:void 0;if((0,import_is_function.default)(value2))return;if((0,import_is_symbol.default)(value2)){if(!options.allowSymbol)return;let globalRegistryKey=Symbol.keyFor(value2);return globalRegistryKey!==void 0?`_gsymbol_${globalRegistryKey}`:`_symbol_${value2.toString().slice(7,-1)}`}if(stack.length>=options.maxDepth)return Array.isArray(value2)?`[Array(${value2.length})]`:"[Object]";if(value2===this)return `_duplicate_${JSON.stringify(keys)}`;if(value2 instanceof Error&&options.allowError)return {__isConvertedError__:!0,errorProperties:{...value2.cause?{cause:value2.cause}:{},...value2,name:value2.name,message:value2.message,stack:value2.stack,"_constructor-name_":value2.constructor.name}};if(value2?.constructor?.name&&value2.constructor.name!=="Object"&&!Array.isArray(value2)){let found2=objects.get(value2);if(!found2){let plainObject={__isClassInstance__:!0,__className__:value2.constructor.name,...Object.getOwnPropertyNames(value2).reduce((acc,prop)=>{try{acc[prop]=value2[prop];}catch{}return acc},{})};return keys.push(key),stack.unshift(plainObject),objects.set(value2,JSON.stringify(keys)),value2!==plainObject&&map.set(value2,plainObject),plainObject}return `_duplicate_${found2}`}let found=objects.get(value2);if(!found){let converted=Array.isArray(value2)?value2:convertUnconventionalData(value2);return keys.push(key),stack.unshift(converted),objects.set(value2,JSON.stringify(keys)),value2!==converted&&map.set(value2,converted),converted}return `_duplicate_${found}`}catch{return}}};var defaultOptions={maxDepth:10,space:void 0,allowRegExp:!0,allowDate:!0,allowError:!0,allowUndefined:!0,allowSymbol:!0},stringify=(data,options={})=>{let mergedOptions={...defaultOptions,...options};return JSON.stringify(convertUnconventionalData(data),replacer(mergedOptions),options.space)};function argsHash(args){return stringify(args,{maxDepth:50})}var SourceContext=React20.createContext({sources:{}}),UNKNOWN_ARGS_HASH="--unknown--",SourceContainer=({children,channel})=>{let[sources,setSources]=React20.useState({});return React20.useEffect(()=>{let handleSnippetRendered=(idOrEvent,inputSource=null,inputFormat=!1)=>{let{id:id2,args=void 0,source,format:format3}=typeof idOrEvent=="string"?{id:idOrEvent,source:inputSource,format:inputFormat}:idOrEvent,hash=args?argsHash(args):UNKNOWN_ARGS_HASH;setSources(current=>({...current,[id2]:{...current[id2],[hash]:{code:source||"",format:format3}}}));};return channel.on(docsTools.SNIPPET_RENDERED,handleSnippetRendered),()=>channel.off(docsTools.SNIPPET_RENDERED,handleSnippetRendered)},[]),React20__namespace.default.createElement(SourceContext.Provider,{value:{sources}},children)};function useTransformCode(source,transform,storyContext){let[transformedCode,setTransformedCode]=React20.useState("Transforming..."),transformed=transform?transform?.(source,storyContext):source;return React20.useEffect(()=>{async function getTransformedCode(){let transformResult=await transformed;transformResult!==transformedCode&&setTransformedCode(transformResult);}getTransformedCode();}),typeof transformed=="object"&&typeof transformed.then=="function"?transformedCode:transformed}var getStorySource=(storyId,args,sourceContext)=>{let{sources}=sourceContext,sourceMap=sources?.[storyId];return sourceMap?.[argsHash(args)]||sourceMap?.[UNKNOWN_ARGS_HASH]||{code:""}},useCode=({snippet,storyContext,typeFromProps,transformFromProps})=>{let parameters=storyContext.parameters??{},{__isArgsStory:isArgsStory}=parameters,sourceParameters=parameters.docs?.source||{},type=typeFromProps||sourceParameters.type||docsTools.SourceType.AUTO,code=type===docsTools.SourceType.DYNAMIC||type===docsTools.SourceType.AUTO&&snippet&&isArgsStory?snippet:sourceParameters.originalSource||"",transformer=transformFromProps??sourceParameters.transform,transformedCode=transformer?useTransformCode(code,transformer,storyContext):code;return sourceParameters.code!==void 0?sourceParameters.code:transformedCode},useSourceProps=(props,docsContext,sourceContext)=>{let{of}=props,story=React20.useMemo(()=>{if(of)return docsContext.resolveOf(of,["story"]).story;try{return docsContext.storyById()}catch{}},[docsContext,of]),storyContext=story?docsContext.getStoryContext(story):{},argsForSource=props.__forceInitialArgs?storyContext.initialArgs:storyContext.unmappedArgs,source=story?getStorySource(story.id,argsForSource,sourceContext):null,transformedCode=useCode({snippet:source?source.code:"",storyContext:{...storyContext,args:argsForSource},typeFromProps:props.type,transformFromProps:props.transform});if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let sourceParameters=story?.parameters?.docs?.source||{},format3=props.format,language=props.language??sourceParameters.language??"jsx",dark=props.dark??sourceParameters.dark??!1;return !props.code&&!story?{error:"Oh no! The source is not available."}:props.code?{code:props.code,format:format3,language,dark}:(format3=source?.format??!0,{code:transformedCode,format:format3,language,dark})},Source2=props=>{let sourceContext=React20.useContext(SourceContext),docsContext=React20.useContext(DocsContext),sourceProps=useSourceProps(props,docsContext,sourceContext);return React20__namespace.default.createElement(Source,{...sourceProps})};function useStory(storyId,context){let stories=useStories([storyId],context);return stories&&stories[0]}function useStories(storyIds,context){let[storiesById,setStories]=React20.useState({});return React20.useEffect(()=>{Promise.all(storyIds.map(async storyId=>{let story=await context.loadStory(storyId);setStories(current=>current[storyId]===story?current:{...current,[storyId]:story});}));}),storyIds.map(storyId=>{if(storiesById[storyId])return storiesById[storyId];try{return context.storyById(storyId)}catch{return}})}var getStoryId2=(props,context)=>{let{of,meta}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");return meta&&context.referenceMeta(meta,!1),context.resolveOf(of||"story",["story"]).story.id},getStoryProps=(props,story,context)=>{let{parameters={}}=story||{},{docs={}}=parameters,storyParameters=docs.story||{};if(docs.disable)return null;if(props.inline??storyParameters.inline??!1){let height2=props.height??storyParameters.height,autoplay=props.autoplay??storyParameters.autoplay??!1;return {story,inline:!0,height:height2,autoplay,forceInitialArgs:!!props.__forceInitialArgs,primary:!!props.__primary,renderStoryToElement:context.renderStoryToElement}}let height=props.height??storyParameters.height??storyParameters.iframeHeight??"100px";return {story,inline:!1,height,primary:!!props.__primary}},Story2=(props={__forceInitialArgs:!1,__primary:!1})=>{let context=React20.useContext(DocsContext),storyId=getStoryId2(props,context),story=useStory(storyId,context);if(!story)return React20__namespace.default.createElement(StorySkeleton,null);let storyProps=getStoryProps(props,story,context);return storyProps?React20__namespace.default.createElement(Story,{...storyProps}):null};var Canvas=props=>{let docsContext=React20.useContext(DocsContext),sourceContext=React20.useContext(SourceContext),{of,source}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let{story}=useOf(of||"story",["story"]),sourceProps=useSourceProps({...source,...of&&{of}},docsContext,sourceContext),layout=props.layout??story.parameters.layout??story.parameters.docs?.canvas?.layout??"padded",withToolbar=props.withToolbar??story.parameters.docs?.canvas?.withToolbar??!1,additionalActions=props.additionalActions??story.parameters.docs?.canvas?.additionalActions,sourceState=props.sourceState??story.parameters.docs?.canvas?.sourceState??"hidden",className=props.className??story.parameters.docs?.canvas?.className,inline=props.story?.inline??story.parameters?.docs?.story?.inline??!1;return React20__namespace.default.createElement(Preview,{withSource:sourceState==="none"?void 0:sourceProps,isExpanded:sourceState==="shown",withToolbar,additionalActions,className,layout,inline},React20__namespace.default.createElement(Story2,{of:of||story.moduleExport,meta:props.meta,...props.story}))};var useArgs=(story,context)=>{let result=useArgsIfDefined(story,context);if(!result)throw new Error("No result when story was defined");return result},useArgsIfDefined=(story,context)=>{let storyContext=story?context.getStoryContext(story):{args:{}},{id:storyId}=story||{id:"none"},[args,setArgs]=React20.useState(storyContext.args);React20.useEffect(()=>{let onArgsUpdated=changed=>{changed.storyId===storyId&&setArgs(changed.args);};return context.channel.on(coreEvents.STORY_ARGS_UPDATED,onArgsUpdated),()=>context.channel.off(coreEvents.STORY_ARGS_UPDATED,onArgsUpdated)},[storyId,context.channel]);let updateArgs=React20.useCallback(updatedArgs=>context.channel.emit(coreEvents.UPDATE_STORY_ARGS,{storyId,updatedArgs}),[storyId,context.channel]),resetArgs=React20.useCallback(argNames=>context.channel.emit(coreEvents.RESET_STORY_ARGS,{storyId,argNames}),[storyId,context.channel]);return story&&[args,updateArgs,resetArgs]};var useGlobals=(story,context)=>{let storyContext=context.getStoryContext(story),[globals,setGlobals]=React20.useState(storyContext.globals);return React20.useEffect(()=>{let onGlobalsUpdated=changed=>{setGlobals(changed.globals);};return context.channel.on(coreEvents.GLOBALS_UPDATED,onGlobalsUpdated),()=>context.channel.off(coreEvents.GLOBALS_UPDATED,onGlobalsUpdated)},[context.channel]),[globals]};function extractComponentArgTypes2(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error("Args unsupported. See Args documentation for your framework.");return extractArgTypes(component)}var Controls3=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let context=React20.useContext(DocsContext),{story}=context.resolveOf(of||"story",["story"]),{parameters,argTypes,component,subcomponents}=story,controlsParameters=parameters.docs?.controls||{},include=props.include??controlsParameters.include,exclude=props.exclude??controlsParameters.exclude,sort=props.sort??controlsParameters.sort,[args,updateArgs,resetArgs]=useArgs(story,context),[globals]=useGlobals(story,context),filteredArgTypes=previewApi.filterArgTypes(argTypes,include,exclude);if(!(!!subcomponents&&Object.keys(subcomponents||{}).length>0))return Object.keys(filteredArgTypes).length>0||Object.keys(args).length>0?React20__namespace.default.createElement(ArgsTable,{rows:filteredArgTypes,sort,args,globals,updateArgs,resetArgs}):null;let mainComponentName=getComponentName(component)||"Story",subcomponentTabs=Object.fromEntries(Object.entries(subcomponents||{}).map(([key,comp])=>[key,{rows:previewApi.filterArgTypes(extractComponentArgTypes2(comp,parameters),include,exclude),sort}])),tabs={[mainComponentName]:{rows:filteredArgTypes,sort},...subcomponentTabs};return React20__namespace.default.createElement(TabbedArgsTable,{tabs,sort,args,globals,updateArgs,resetArgs})};var {document:document2}=globalThis,assertIsFn=val=>{if(typeof val!="function")throw new Error(`Expected story function, got: ${val}`);return val},AddContext=props=>{let{children,...rest}=props,parentContext=React20__namespace.default.useContext(DocsContext);return React20__namespace.default.createElement(DocsContext.Provider,{value:{...parentContext,...rest}},children)},CodeOrSourceMdx=({className,children,...rest})=>{if(typeof className!="string"&&(typeof children!="string"||!children.match(/[\n\r]/g)))return React20__namespace.default.createElement(components.Code,null,children);let language=className&&className.split("-");return React20__namespace.default.createElement(Source,{language:language&&language[1]||"text",format:!1,code:children,...rest})};function navigate(context,url){context.channel.emit(coreEvents.NAVIGATE_URL,url);}var A2=components.components.a,AnchorInPage=({hash,children})=>{let context=React20.useContext(DocsContext);return React20__namespace.default.createElement(A2,{href:hash,target:"_self",onClick:event=>{let id2=hash.substring(1);document2.getElementById(id2)&&navigate(context,hash);}},children)},AnchorMdx=props=>{let{href,target,children,...rest}=props,context=React20.useContext(DocsContext);return !href||target==="_blank"||/^https?:\/\//.test(href)?React20__namespace.default.createElement(A2,{...props}):href.startsWith("#")?React20__namespace.default.createElement(AnchorInPage,{hash:href},children):React20__namespace.default.createElement(A2,{href,onClick:event=>{event.button===0&&!event.altKey&&!event.ctrlKey&&!event.metaKey&&!event.shiftKey&&(event.preventDefault(),navigate(context,event.currentTarget.getAttribute("href")||""));},target,...rest},children)},SUPPORTED_MDX_HEADERS=["h1","h2","h3","h4","h5","h6"],OcticonHeaders=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:theming.styled(headerType)({"& svg":{position:"relative",top:"-0.1em",visibility:"hidden"},"&:hover svg":{visibility:"visible"}})}),{}),OcticonAnchor=theming.styled.a(()=>({float:"left",lineHeight:"inherit",paddingRight:"10px",marginLeft:"-24px",color:"inherit"})),HeaderWithOcticonAnchor=({as,id:id2,children,...rest})=>{let context=React20.useContext(DocsContext),OcticonHeader=OcticonHeaders[as],hash=`#${id2}`;return React20__namespace.default.createElement(OcticonHeader,{id:id2,...rest},React20__namespace.default.createElement(OcticonAnchor,{"aria-hidden":"true",href:hash,tabIndex:-1,target:"_self",onClick:event=>{document2.getElementById(id2)&&navigate(context,hash);}},React20__namespace.default.createElement(icons.LinkIcon,null)),children)},HeaderMdx=props=>{let{as,id:id2,children,...rest}=props;if(id2)return React20__namespace.default.createElement(HeaderWithOcticonAnchor,{as,id:id2,...rest},children);let Component4=as,{as:omittedAs,...withoutAs}=props;return React20__namespace.default.createElement(Component4,{...components.nameSpaceClassNames(withoutAs,as)})},HeadersMdx=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:props=>React20__namespace.default.createElement(HeaderMdx,{as:headerType,...props})}),{});var Markdown=props=>{if(!props.children)return null;if(typeof props.children!="string")throw new Error(tsDedent.dedent`The Markdown block only accepts children as a single string, but children were of type: '${typeof props.children}'
        This is often caused by not wrapping the child in a template string.
        
        This is invalid:
        <Markdown>
          # Some heading
          A paragraph
        </Markdown>

        Instead do:
        <Markdown>
        {\`
          # Some heading
          A paragraph
        \`}
        </Markdown>
      `);return React20__namespace.default.createElement(index_modern_default,{...props,options:{forceBlock:!0,overrides:{code:CodeOrSourceMdx,a:AnchorMdx,...HeadersMdx,...props?.options?.overrides},...props?.options}})};var DescriptionType=(DescriptionType2=>(DescriptionType2.INFO="info",DescriptionType2.NOTES="notes",DescriptionType2.DOCGEN="docgen",DescriptionType2.AUTO="auto",DescriptionType2))(DescriptionType||{}),getDescriptionFromResolvedOf=resolvedOf=>{switch(resolvedOf.type){case"story":return resolvedOf.story.parameters.docs?.description?.story||null;case"meta":{let{parameters,component}=resolvedOf.preparedMeta,metaDescription=parameters.docs?.description?.component;return metaDescription||parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}case"component":{let{component,projectAnnotations:{parameters}}=resolvedOf;return parameters?.docs?.extractComponentDescription?.(component,{component,parameters})||null}default:throw new Error(`Unrecognized module type resolved from 'useOf', got: ${resolvedOf.type}`)}},DescriptionContainer=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let resolvedOf=useOf(of||"meta"),markdown=getDescriptionFromResolvedOf(resolvedOf);return markdown?React20__namespace.default.createElement(Markdown,null,markdown):null};var {document:document3,window:globalWindow3}=globalThis,DocsContainer=({context,theme,children})=>{let toc;try{toc=context.resolveOf("meta",["meta"]).preparedMeta.parameters?.docs?.toc;}catch{toc=context?.projectAnnotations?.parameters?.docs?.toc;}return React20.useEffect(()=>{let url;try{if(url=new URL(globalWindow3.parent.location.toString()),url.hash){let element=document3.getElementById(decodeURIComponent(url.hash.substring(1)));element&&setTimeout(()=>{scrollToElement(element);},200);}}catch{}}),React20__namespace.default.createElement(DocsContext.Provider,{value:context},React20__namespace.default.createElement(SourceContainer,{channel:context.channel},React20__namespace.default.createElement(theming.ThemeProvider,{theme:theming.ensure(theme)},React20__namespace.default.createElement(DocsPageWrapper,{toc:toc?React20__namespace.default.createElement(TableOfContents,{className:"sbdocs sbdocs-toc--custom",channel:context.channel,...toc}):null},children))))};var regex=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g;var own=Object.hasOwnProperty,BananaSlug=class{constructor(){this.occurrences,this.reset();}slug(value2,maintainCase){let self2=this,result=slug(value2,maintainCase===!0),originalSlug=result;for(;own.call(self2.occurrences,result);)self2.occurrences[originalSlug]++,result=originalSlug+"-"+self2.occurrences[originalSlug];return self2.occurrences[result]=0,result}reset(){this.occurrences=Object.create(null);}};function slug(value2,maintainCase){return typeof value2!="string"?"":(maintainCase||(value2=value2.toLowerCase()),value2.replace(regex,"").replace(/ /g,"-"))}var slugs=new BananaSlug,Heading2=({children,disableAnchor,...props})=>{if(disableAnchor||typeof children!="string")return React20__namespace.default.createElement(components.H2,null,children);let tagID=slugs.slug(children.toLowerCase());return React20__namespace.default.createElement(HeaderMdx,{as:"h2",id:tagID,...props},children)};var Subheading=({children,disableAnchor})=>{if(disableAnchor||typeof children!="string")return React20__namespace.default.createElement(components.H3,null,children);let tagID=slugs.slug(children.toLowerCase());return React20__namespace.default.createElement(HeaderMdx,{as:"h3",id:tagID},children)};var DocsStory=({of,expanded=!0,withToolbar:withToolbarProp=!1,__forceInitialArgs=!1,__primary=!1})=>{let{story}=useOf(of||"story",["story"]),withToolbar=story.parameters.docs?.canvas?.withToolbar??withToolbarProp;return React20__namespace.default.createElement(Anchor,{storyId:story.id},expanded&&React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(Subheading,null,story.name),React20__namespace.default.createElement(DescriptionContainer,{of})),React20__namespace.default.createElement(Canvas,{of,withToolbar,story:{__forceInitialArgs,__primary},source:{__forceInitialArgs}}))};var Primary=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let{csfFile}=useOf(of||"meta",["meta"]),primaryStory=React20.useContext(DocsContext).componentStoriesFromCSFFile(csfFile)[0];return primaryStory?React20__namespace.default.createElement(DocsStory,{of:primaryStory.moduleExport,expanded:!1,__primary:!0,withToolbar:!0}):null};var StyledHeading=theming.styled(Heading2)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,fontWeight:theme.typography.weight.bold,lineHeight:"16px",letterSpacing:"0.35em",textTransform:"uppercase",color:theme.textMutedColor,border:0,marginBottom:"12px","&:first-of-type":{marginTop:"56px"}})),Stories=({title="Stories",includePrimary=!0})=>{let{componentStories,projectAnnotations,getStoryContext}=React20.useContext(DocsContext),stories=componentStories(),{stories:{filter}={filter:void 0}}=projectAnnotations.parameters?.docs||{};return filter&&(stories=stories.filter(story=>filter(story,getStoryContext(story)))),stories.some(story=>story.tags?.includes("autodocs"))&&(stories=stories.filter(story=>story.tags?.includes("autodocs")&&!story.usesMount)),includePrimary||(stories=stories.slice(1)),!stories||stories.length===0?null:React20__namespace.default.createElement(React20__namespace.default.Fragment,null,typeof title=="string"?React20__namespace.default.createElement(StyledHeading,null,title):title,stories.map(story=>story&&React20__namespace.default.createElement(DocsStory,{key:story.id,of:story.moduleExport,expanded:!0,__forceInitialArgs:!0})))};var DEPRECATION_MIGRATION_LINK="https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#subtitle-block-and-parameterscomponentsubtitle",Subtitle2=props=>{let{of,children}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let preparedMeta;try{preparedMeta=useOf(of||"meta",["meta"]).preparedMeta;}catch(error){if(children&&!error.message.includes("did you forget to use <Meta of={} />?"))throw error}let{componentSubtitle,docs}=preparedMeta?.parameters||{};componentSubtitle&&clientLogger.deprecate(`Using 'parameters.componentSubtitle' property to subtitle stories is deprecated. See ${DEPRECATION_MIGRATION_LINK}`);let content=children||docs?.subtitle||componentSubtitle;return content?React20__namespace.default.createElement(Subtitle,{className:"sbdocs-subtitle sb-unstyled"},content):null};var STORY_KIND_PATH_SEPARATOR=/\s*\/\s*/,extractTitle=title=>{let groups=title.trim().split(STORY_KIND_PATH_SEPARATOR);return groups?.[groups?.length-1]||title},Title3=props=>{let{children,of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let preparedMeta;try{preparedMeta=useOf(of||"meta",["meta"]).preparedMeta;}catch(error){if(children&&error instanceof Error&&!error.message.includes("did you forget to use <Meta of={} />?"))throw error}let content=children||extractTitle(preparedMeta?.title||"");return content?React20__namespace.default.createElement(Title,{className:"sbdocs-title sb-unstyled"},content):null};var DocsPage=()=>{let resolvedOf=useOf("meta",["meta"]),{stories}=resolvedOf.csfFile,isSingleStory=Object.keys(stories).length===1;return React20__namespace.default.createElement(React20__namespace.default.Fragment,null,React20__namespace.default.createElement(Title3,null),React20__namespace.default.createElement(Subtitle2,null),React20__namespace.default.createElement(DescriptionContainer,{of:"meta"}),isSingleStory?React20__namespace.default.createElement(DescriptionContainer,{of:"story"}):null,React20__namespace.default.createElement(Primary,null),React20__namespace.default.createElement(Controls3,null),isSingleStory?null:React20__namespace.default.createElement(Stories,null))};function Docs({context,docsParameter}){let Container=docsParameter.container||DocsContainer,Page=docsParameter.page||DocsPage;return React20__namespace.default.createElement(Container,{context,theme:docsParameter.theme},React20__namespace.default.createElement(Page,null))}var ExternalDocsContext=class extends previewApi.DocsContext{constructor(channel,store,renderStoryToElement,processMetaExports){super(channel,store,renderStoryToElement,[]);this.channel=channel;this.store=store;this.renderStoryToElement=renderStoryToElement;this.processMetaExports=processMetaExports;this.referenceMeta=(metaExports,attach)=>{let csfFile=this.processMetaExports(metaExports);this.referenceCSFFile(csfFile),super.referenceMeta(metaExports,attach);};}};var ConstantMap=class{constructor(prefix){this.prefix=prefix;this.entries=new Map;}get(key){return this.entries.has(key)||this.entries.set(key,`${this.prefix}${this.entries.size}`),this.entries.get(key)}},ExternalPreview=class extends previewApi.Preview{constructor(projectAnnotations){super(path=>Promise.resolve(this.moduleExportsByImportPath[path]),()=>previewApi.composeConfigs([{parameters:{docs:{story:{inline:!0}}}},this.projectAnnotations]),new channels.Channel({}));this.projectAnnotations=projectAnnotations;this.importPaths=new ConstantMap("./importPath/");this.titles=new ConstantMap("title-");this.storyIndex={v:5,entries:{}};this.moduleExportsByImportPath={};this.processMetaExports=metaExports=>{let importPath=this.importPaths.get(metaExports);this.moduleExportsByImportPath[importPath]=metaExports;let title=metaExports.default.title||this.titles.get(metaExports),csfFile=this.storyStoreValue.processCSFFileWithCache(metaExports,importPath,title);return Object.values(csfFile.stories).forEach(({id:id2,name})=>{this.storyIndex.entries[id2]={id:id2,importPath,title,name,type:"story"};}),this.onStoriesChanged({storyIndex:this.storyIndex}),csfFile};this.docsContext=()=>new ExternalDocsContext(this.channel,this.storyStoreValue,this.renderStoryToElement.bind(this),this.processMetaExports.bind(this));}async getStoryIndexFromServer(){return this.storyIndex}};function usePreview(projectAnnotations){let previewRef=React20.useRef();return previewRef.current||(previewRef.current=new ExternalPreview(projectAnnotations)),previewRef.current}function ExternalDocs({projectAnnotationsList,children}){let projectAnnotations=previewApi.composeConfigs(projectAnnotationsList),preview2=usePreview(projectAnnotations),docsParameter={...projectAnnotations.parameters?.docs,page:()=>children};return React20__namespace.default.createElement(Docs,{docsParameter,context:preview2.docsContext()})}var preview,ExternalDocsContainer=({projectAnnotations,children})=>(preview||(preview=new ExternalPreview(projectAnnotations)),React20__namespace.default.createElement(DocsContext.Provider,{value:preview.docsContext()},React20__namespace.default.createElement(theming.ThemeProvider,{theme:theming.ensure(theming.themes.light)},children)));var Meta=({of})=>{let context=React20.useContext(DocsContext);of&&context.referenceMeta(of,!0);try{let primary=context.storyById();return React20__namespace.default.createElement(Anchor,{storyId:primary.id})}catch{return null}};var Unstyled=props=>React20__namespace.default.createElement("div",{...props,className:"sb-unstyled"});var Wrapper11=({children})=>React20__namespace.default.createElement("div",{style:{fontFamily:"sans-serif"}},children);var PRIMARY_STORY="^";

exports.AddContext = AddContext;
exports.Anchor = Anchor;
exports.AnchorMdx = AnchorMdx;
exports.ArgTypes = ArgTypes;
exports.BooleanControl = BooleanControl;
exports.Canvas = Canvas;
exports.CodeOrSourceMdx = CodeOrSourceMdx;
exports.ColorControl = ColorControl2;
exports.ColorItem = ColorItem;
exports.ColorPalette = ColorPalette;
exports.Controls = Controls3;
exports.DateControl = DateControl;
exports.Description = DescriptionContainer;
exports.DescriptionType = DescriptionType;
exports.Docs = Docs;
exports.DocsContainer = DocsContainer;
exports.DocsContext = DocsContext;
exports.DocsPage = DocsPage;
exports.DocsStory = DocsStory;
exports.ExternalDocs = ExternalDocs;
exports.ExternalDocsContainer = ExternalDocsContainer;
exports.FilesControl = FilesControl;
exports.HeaderMdx = HeaderMdx;
exports.HeadersMdx = HeadersMdx;
exports.Heading = Heading2;
exports.IconGallery = IconGallery;
exports.IconItem = IconItem;
exports.Markdown = Markdown;
exports.Meta = Meta;
exports.NumberControl = NumberControl;
exports.ObjectControl = ObjectControl;
exports.OptionsControl = OptionsControl;
exports.PRIMARY_STORY = PRIMARY_STORY;
exports.Primary = Primary;
exports.PureArgsTable = ArgsTable;
exports.RangeControl = RangeControl;
exports.Source = Source2;
exports.SourceContainer = SourceContainer;
exports.SourceContext = SourceContext;
exports.Stories = Stories;
exports.Story = Story2;
exports.Subheading = Subheading;
exports.Subtitle = Subtitle2;
exports.TableOfContents = TableOfContents;
exports.TextControl = TextControl;
exports.Title = Title3;
exports.Typeset = Typeset;
exports.UNKNOWN_ARGS_HASH = UNKNOWN_ARGS_HASH;
exports.Unstyled = Unstyled;
exports.Wrapper = Wrapper11;
exports.anchorBlockIdFromId = anchorBlockIdFromId;
exports.argsHash = argsHash;
exports.assertIsFn = assertIsFn;
exports.extractTitle = extractTitle;
exports.format = format2;
exports.formatDate = formatDate;
exports.formatTime = formatTime;
exports.getStoryId = getStoryId2;
exports.getStoryProps = getStoryProps;
exports.parse = parse2;
exports.parseDate = parseDate;
exports.parseTime = parseTime;
exports.slugs = slugs;
exports.useOf = useOf;
exports.useSourceProps = useSourceProps;
