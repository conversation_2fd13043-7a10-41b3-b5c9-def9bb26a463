import React, { lazy, Suspense } from 'react';
import ReactD<PERSON> from 'react-dom';
import { ADDON_ID } from 'storybook/internal/controls';
import { STORY_SPECIFIED } from 'storybook/internal/core-events';
import { addons } from 'storybook/manager-api';

var Onboarding=lazy(()=>import('./Onboarding-XGKXPXUL.js'));addons.register("@storybook/addon-onboarding",async api=>{let urlState=api.getUrlState(),isOnboarding=urlState.path==="/onboarding"||urlState.queryParams.onboarding==="true";api.once(STORY_SPECIFIED,()=>{if(!(!!api.getData("example-button--primary")||!!document.getElementById("example-button--primary"))){console.warn("[@storybook/addon-onboarding] It seems like you have finished the onboarding experience in Storybook! Therefore this addon is not necessary anymore and will not be loaded. You are free to remove it from your project. More info: https://github.com/storybookjs/storybook/tree/next/code/addons/onboarding#uninstalling");return}if(!isOnboarding||window.innerWidth<730)return;api.togglePanel(!0),api.togglePanelPosition("bottom"),api.setSelectedPanel(ADDON_ID);let domNode=document.createElement("div");domNode.id="storybook-addon-onboarding",document.body.appendChild(domNode),ReactDOM.render(React.createElement(Suspense,{fallback:React.createElement("div",null)},React.createElement(Onboarding,{api})),domNode);});});
