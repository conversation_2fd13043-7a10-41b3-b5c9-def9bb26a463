# v1.0.11 (<PERSON><PERSON> Jan 23 2024)

#### 🐛 Bug Fix

- Fix z-index bug by adding a wrapper [#82](https://github.com/storybookjs/addon-onboarding/pull/82) ([@ndelangen](https://github.com/ndelangen))
- Make selectors Storybook 8 compatible [#81](https://github.com/storybookjs/addon-onboarding/pull/81) ([@yannbf](https://github.com/yannbf))
- UI: Fix z-index in modal elements [#78](https://github.com/storybookjs/addon-onboarding/pull/78) ([@cdedreuille](https://github.com/cdedreuille) [@yannbf](https://github.com/yannbf))

#### Authors: 3

- <PERSON> ([@cdedreuille](https://github.com/cdedreuille))
- <PERSON><PERSON> ([@ndelangen](https://github.com/ndelangen))
- <PERSON><PERSON> ([@yannbf](https://github.com/yannbf))

---

# v1.0.10 (Mon Dec 11 2023)

#### 🐛 Bug Fix

- Fix Yarn remove command in README [#80](https://github.com/storybookjs/addon-onboarding/pull/80) ([@githrdw](https://github.com/githrdw))

#### Authors: 1

- [@githrdw](https://github.com/githrdw)

---

# v1.0.9 (Fri Dec 01 2023)

#### 🐛 Bug Fix

- update telemetry version [#79](https://github.com/storybookjs/addon-onboarding/pull/79) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v1.0.8 (Thu Jul 20 2023)

#### ⚠️ Pushed to `main`

- Create CODEOWNERS ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v1.0.7 (Tue Jul 11 2023)

#### 🐛 Bug Fix

- Replace chevron icon on Configure page to avoid @storybook/components usage [#77](https://github.com/storybookjs/addon-onboarding/pull/77) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v1.0.6 (Mon Jul 10 2023)

#### 🐛 Bug Fix

- Fix language detection [#76](https://github.com/storybookjs/addon-onboarding/pull/76) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v1.0.5 (Mon Jul 10 2023)

#### 🐛 Bug Fix

- Remove nextjs-specific code [#75](https://github.com/storybookjs/addon-onboarding/pull/75) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v1.0.4 (Thu Jul 06 2023)

#### 🐛 Bug Fix

- Update Configure page design [#74](https://github.com/storybookjs/addon-onboarding/pull/74) ([@JReinhold](https://github.com/JReinhold) [@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Jeppe Reinhold ([@JReinhold](https://github.com/JReinhold))

---

# v1.0.3 (Fri Jun 23 2023)

#### 🐛 Bug Fix

- Display the button story filename in tooltip [#73](https://github.com/storybookjs/addon-onboarding/pull/73) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v1.0.2 (Thu Jun 22 2023)

#### 🐛 Bug Fix

- Fix package.json version extraction [#72](https://github.com/storybookjs/addon-onboarding/pull/72) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v1.0.1 (Thu Jun 22 2023)

#### 🐛 Bug Fix

- Fix build assets [#71](https://github.com/storybookjs/addon-onboarding/pull/71) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v1.0.0 (Thu Jun 22 2023)

#### 💥 Breaking Change

- Release 1.0.0 [#70](https://github.com/storybookjs/addon-onboarding/pull/70) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### 🐛 Bug Fix

- Fix text for Javascript Projects [#68](https://github.com/storybookjs/addon-onboarding/pull/68) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.43 (Thu Jun 22 2023)

#### 🐛 Bug Fix

- Fix react joyride in yarn pnp mode [#69](https://github.com/storybookjs/addon-onboarding/pull/69) ([@valentinpalkovic](https://github.com/valentinpalkovic))
- fix links in configure.mdx [#67](https://github.com/storybookjs/addon-onboarding/pull/67) ([@yannbf](https://github.com/yannbf))

#### Authors: 2

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.42 (Wed Jun 21 2023)

#### 🐛 Bug Fix

- Fix nextjs code steps [#66](https://github.com/storybookjs/addon-onboarding/pull/66) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.41 (Wed Jun 21 2023)

#### 🐛 Bug Fix

- Minor improvements [#65](https://github.com/storybookjs/addon-onboarding/pull/65) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.40 (Tue Jun 20 2023)

#### 🐛 Bug Fix

- Fix preset.js entry point [#63](https://github.com/storybookjs/addon-onboarding/pull/63) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.39 (Tue Jun 20 2023)

#### 🐛 Bug Fix

- Fix comment in code section [#64](https://github.com/storybookjs/addon-onboarding/pull/64) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.38 (Mon Jun 19 2023)

#### 🐛 Bug Fix

- Pass addon version in telemetry event [#62](https://github.com/storybookjs/addon-onboarding/pull/62) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.37 (Mon Jun 19 2023)

#### 🐛 Bug Fix

- UI Fixes [#61](https://github.com/storybookjs/addon-onboarding/pull/61) ([@yannbf](https://github.com/yannbf) [@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.36 (Fri Jun 16 2023)

#### 🐛 Bug Fix

- Improve dark mode [#58](https://github.com/storybookjs/addon-onboarding/pull/58) ([@cdedreuille](https://github.com/cdedreuille) [@yannbf](https://github.com/yannbf))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.35 (Fri Jun 16 2023)

#### 🐛 Bug Fix

- Add dark mode styles to tooltip [#54](https://github.com/storybookjs/addon-onboarding/pull/54) ([@yannbf](https://github.com/yannbf))
- UI fixes [#56](https://github.com/storybookjs/addon-onboarding/pull/56) ([@yannbf](https://github.com/yannbf))
- Fix watch mode [#55](https://github.com/storybookjs/addon-onboarding/pull/55) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.34 (Wed Jun 14 2023)

#### 🐛 Bug Fix

- Fix configure page's layout [#53](https://github.com/storybookjs/addon-onboarding/pull/53) ([@cdedreuille](https://github.com/cdedreuille))

#### Authors: 1

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))

---

# v0.0.33 (Wed Jun 14 2023)

#### 🐛 Bug Fix

- Improve story detection [#52](https://github.com/storybookjs/addon-onboarding/pull/52) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.32 (Wed Jun 14 2023)

#### 🐛 Bug Fix

- Fixes on syntax highlighter [#51](https://github.com/storybookjs/addon-onboarding/pull/51) ([@cdedreuille](https://github.com/cdedreuille) [@yannbf](https://github.com/yannbf))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.31 (Wed Jun 14 2023)

#### 🐛 Bug Fix

- Improve instructions [#50](https://github.com/storybookjs/addon-onboarding/pull/50) ([@shilman](https://github.com/shilman))

#### Authors: 1

- Michael Shilman ([@shilman](https://github.com/shilman))

---

# v0.0.30 (Tue Jun 13 2023)

#### 🐛 Bug Fix

- Add core server events for telemetry [#40](https://github.com/storybookjs/addon-onboarding/pull/40) ([@valentinpalkovic](https://github.com/valentinpalkovic) [@yannbf](https://github.com/yannbf))

#### Authors: 2

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.29 (Tue Jun 13 2023)

#### ⚠️ Pushed to `main`

- cleanup dependencies ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.28 (Mon Jun 12 2023)

#### 🐛 Bug Fix

- Feat/overall improvements [#49](https://github.com/storybookjs/addon-onboarding/pull/49) ([@yannbf](https://github.com/yannbf) [@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.27 (Fri Jun 09 2023)

#### 🐛 Bug Fix

- Add previous button to write stories modal [#48](https://github.com/storybookjs/addon-onboarding/pull/48) ([@yannbf](https://github.com/yannbf) [@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.26 (Thu Jun 08 2023)

#### 🐛 Bug Fix

- Remove animation modal [#47](https://github.com/storybookjs/addon-onboarding/pull/47) ([@cdedreuille](https://github.com/cdedreuille))

#### Authors: 1

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))

---

# v0.0.25 (Thu Jun 08 2023)

#### 🐛 Bug Fix

- Improve addon bootstrapping [#46](https://github.com/storybookjs/addon-onboarding/pull/46) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.24 (Thu Jun 08 2023)

#### 🐛 Bug Fix

- Use the correct event to detect args change [#45](https://github.com/storybookjs/addon-onboarding/pull/45) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.23 (Thu Jun 08 2023)

#### 🐛 Bug Fix

- Various improvements [#44](https://github.com/storybookjs/addon-onboarding/pull/44) ([@yannbf](https://github.com/yannbf) [@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.22 (Thu Jun 08 2023)

#### 🐛 Bug Fix

- Improve confetti colors and shapes [#43](https://github.com/storybookjs/addon-onboarding/pull/43) ([@yannbf](https://github.com/yannbf) [@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.21 (Thu Jun 08 2023)

#### 🐛 Bug Fix

- Improve modal animation [#42](https://github.com/storybookjs/addon-onboarding/pull/42) ([@cdedreuille](https://github.com/cdedreuille))

#### Authors: 1

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))

---

# v0.0.20 (Wed Jun 07 2023)

#### 🐛 Bug Fix

- Change code for Javascript projects [#41](https://github.com/storybookjs/addon-onboarding/pull/41) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.19 (Wed Jun 07 2023)

#### 🐛 Bug Fix

- Improve write a story modal [#38](https://github.com/storybookjs/addon-onboarding/pull/38) ([@cdedreuille](https://github.com/cdedreuille))

#### Authors: 1

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))

---

# v0.0.18 (Wed Jun 07 2023)

#### 🐛 Bug Fix

- Implement "How to write your Story" flow [#33](https://github.com/storybookjs/addon-onboarding/pull/33) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.17 (Wed Jun 07 2023)

#### 🐛 Bug Fix

- use onboarding parameter in conjunction with onboarding path [#37](https://github.com/storybookjs/addon-onboarding/pull/37) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.16 (Tue Jun 06 2023)

#### 🐛 Bug Fix

- Improve guided tour [#35](https://github.com/storybookjs/addon-onboarding/pull/35) ([@cdedreuille](https://github.com/cdedreuille) [@yannbf](https://github.com/yannbf))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.15 (Tue Jun 06 2023)

#### 🐛 Bug Fix

- Trigger via `/onboarding` path instead of query parameter [#21](https://github.com/storybookjs/addon-onboarding/pull/21) ([@yannbf](https://github.com/yannbf))
- Welcome modal animation [#34](https://github.com/storybookjs/addon-onboarding/pull/34) ([@cdedreuille](https://github.com/cdedreuille))

#### Authors: 2

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))
- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.14 (Mon Jun 05 2023)

#### 🐛 Bug Fix

- Modal improvements [#22](https://github.com/storybookjs/addon-onboarding/pull/22) ([@cdedreuille](https://github.com/cdedreuille))

#### Authors: 1

- Charles de Dreuille ([@cdedreuille](https://github.com/cdedreuille))

---

# v0.0.13 (Mon Jun 05 2023)

#### 🐛 Bug Fix

- Implement syntax highlighter [#20](https://github.com/storybookjs/addon-onboarding/pull/20) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.12 (Thu Jun 01 2023)

#### 🐛 Bug Fix

- Implemented bare minimum list component [#19](https://github.com/storybookjs/addon-onboarding/pull/19) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.11 (Thu Jun 01 2023)

#### 🐛 Bug Fix

- Add guided tour example [#5](https://github.com/storybookjs/addon-onboarding/pull/5) ([@yannbf](https://github.com/yannbf))

#### Authors: 1

- Yann Braga ([@yannbf](https://github.com/yannbf))

---

# v0.0.10 (Tue May 30 2023)

#### ⚠️ Pushed to `main`

- Rename confett to Confetti ([@valentinpalkovic](https://github.com/valentinpalkovic))
- Rename confetti to confett ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.9 (Tue May 30 2023)

#### 🐛 Bug Fix

- Implement bare minimum modal component [#18](https://github.com/storybookjs/addon-onboarding/pull/18) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.8 (Tue May 30 2023)

#### 🐛 Bug Fix

- Implement bare minimum confetti component [#7](https://github.com/storybookjs/addon-onboarding/pull/7) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.7 (Thu May 25 2023)

#### 🐛 Bug Fix

- Init Storybook Theme Provider [#6](https://github.com/storybookjs/addon-onboarding/pull/6) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.6 (Tue May 23 2023)

#### 🐛 Bug Fix

- Add minimum React Application [#3](https://github.com/storybookjs/addon-onboarding/pull/3) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.5 (Tue May 23 2023)

#### ⚠️ Pushed to `main`

- Add clean package.json ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.4 (Tue May 23 2023)

#### ⚠️ Pushed to `main`

- Cleanup ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.3 (Tue May 23 2023)

#### 🐛 Bug Fix

- Setup Chromatic [#2](https://github.com/storybookjs/addon-onboarding/pull/2) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.2 (Tue May 23 2023)

#### 🐛 Bug Fix

- Remove unnecessary files [#1](https://github.com/storybookjs/addon-onboarding/pull/1) ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))

---

# v0.0.1 (Tue May 23 2023)

#### ⚠️ Pushed to `main`

- Fix manager.ts and add export {} ([@valentinpalkovic](https://github.com/valentinpalkovic))
- Add auto.config.js ([@valentinpalkovic](https://github.com/valentinpalkovic))
- Init Addon Onboarding ([@valentinpalkovic](https://github.com/valentinpalkovic))
- project setup ([@valentinpalkovic](https://github.com/valentinpalkovic))
- Initial commit ([@valentinpalkovic](https://github.com/valentinpalkovic))

#### Authors: 1

- Valentin Palkovic ([@valentinpalkovic](https://github.com/valentinpalkovic))
