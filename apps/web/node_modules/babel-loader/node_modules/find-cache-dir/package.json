{"name": "find-cache-dir", "version": "4.0.0", "description": "Finds the common standard cache directory", "license": "MIT", "repository": "sindresorhus/find-cache-dir", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cache", "directory", "dir", "caching", "find", "search"], "dependencies": {"common-path-prefix": "^3.0.0", "pkg-dir": "^7.0.0"}, "devDependencies": {"ava": "^5.0.1", "del": "^7.0.0", "tempy": "^3.0.0", "xo": "^0.52.4"}, "ava": {"workerThreads": false}}