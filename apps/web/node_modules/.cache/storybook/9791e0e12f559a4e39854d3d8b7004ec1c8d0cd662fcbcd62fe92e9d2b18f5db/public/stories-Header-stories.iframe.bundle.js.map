{"version": 3, "file": "stories-Header-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA", "sources": ["webpack://web/./src/stories/Header.stories.ts"], "sourcesContent": ["import type { Meta, StoryObj } from '@storybook/nextjs';\n\nimport { fn } from 'storybook/test';\n\nimport { Head<PERSON> } from './Header';\n\nconst meta = {\n  title: 'Example/Header',\n  component: Header,\n  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs\n  tags: ['autodocs'],\n  parameters: {\n    // More on how to position stories at: https://storybook.js.org/docs/configure/story-layout\n    layout: 'fullscreen',\n  },\n  args: {\n    onLogin: fn(),\n    onLogout: fn(),\n    onCreateAccount: fn(),\n  },\n} satisfies Meta<typeof Header>;\n\nexport default meta;\ntype Story = StoryObj<typeof meta>;\n\nexport const LoggedIn: Story = {\n  args: {\n    user: {\n      name: '<PERSON>',\n    },\n  },\n};\n\nexport const LoggedOut: Story = {};\n"], "names": [], "sourceRoot": ""}