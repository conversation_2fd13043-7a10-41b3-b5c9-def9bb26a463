{"version": 3, "file": "stories-Page-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;ACzEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAGA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChCA;AAEA;AACA;AAMA;;AACA;AAEA;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;;;;AAGA;AAAA;;AACA;AAAA;;;;;;AACA;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;;;;;;;;;AACA;AAAA;;;;;;;AAGA;AAAA;;;;;;AAKA;;AACA;AAAA;;;;;;AAIA;AAAA;;;;;;;;;;;;AAKA;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA;;;;;;;AAGA;AAAA;;AACA;AAAA;AAAA;;;;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAGA;;;;;;;;;;;;;;;;;;;AAMA;;AA/DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://web/./src/stories/page.css", "webpack://web/./src/stories/Page.stories.ts", "webpack://web/./src/stories/Page.tsx", "webpack://web/./src/stories/page.css?a253"], "sourcesContent": ["// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.storybook-page {\n  margin: 0 auto;\n  padding: 48px 20px;\n  max-width: 600px;\n  color: #333;\n  font-size: 14px;\n  line-height: 24px;\n  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n}\n\n.storybook-page h2 {\n  display: inline-block;\n  vertical-align: top;\n  margin: 0 0 4px;\n  font-weight: 700;\n  font-size: 32px;\n  line-height: 1;\n}\n\n.storybook-page p {\n  margin: 1em 0;\n}\n\n.storybook-page a {\n  color: inherit;\n}\n\n.storybook-page ul {\n  margin: 1em 0;\n  padding-left: 30px;\n}\n\n.storybook-page li {\n  margin-bottom: 8px;\n}\n\n.storybook-page .tip {\n  display: inline-block;\n  vertical-align: top;\n  margin-right: 10px;\n  border-radius: 1em;\n  background: #e7fdd8;\n  padding: 4px 12px;\n  color: #357a14;\n  font-weight: 700;\n  font-size: 11px;\n  line-height: 12px;\n}\n\n.storybook-page .tip-wrapper {\n  margin-top: 40px;\n  margin-bottom: 40px;\n  font-size: 13px;\n  line-height: 20px;\n}\n\n.storybook-page .tip-wrapper svg {\n  display: inline-block;\n  vertical-align: top;\n  margin-top: 3px;\n  margin-right: 4px;\n  width: 12px;\n  height: 12px;\n}\n\n.storybook-page .tip-wrapper svg path {\n  fill: #1ea7fd;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/stories/page.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,cAAc;EACd,kBAAkB;EAClB,gBAAgB;EAChB,WAAW;EACX,eAAe;EACf,iBAAiB;EACjB,0EAA0E;AAC5E;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,eAAe;EACf,gBAAgB;EAChB,eAAe;EACf,cAAc;AAChB;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,aAAa;EACb,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,mBAAmB;EACnB,iBAAiB;EACjB,cAAc;EACd,gBAAgB;EAChB,eAAe;EACf,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;EAChB,mBAAmB;EACnB,eAAe;EACf,iBAAiB;AACnB;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,eAAe;EACf,iBAAiB;EACjB,WAAW;EACX,YAAY;AACd;;AAEA;EACE,aAAa;AACf\",\"sourcesContent\":[\".storybook-page {\\n  margin: 0 auto;\\n  padding: 48px 20px;\\n  max-width: 600px;\\n  color: #333;\\n  font-size: 14px;\\n  line-height: 24px;\\n  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;\\n}\\n\\n.storybook-page h2 {\\n  display: inline-block;\\n  vertical-align: top;\\n  margin: 0 0 4px;\\n  font-weight: 700;\\n  font-size: 32px;\\n  line-height: 1;\\n}\\n\\n.storybook-page p {\\n  margin: 1em 0;\\n}\\n\\n.storybook-page a {\\n  color: inherit;\\n}\\n\\n.storybook-page ul {\\n  margin: 1em 0;\\n  padding-left: 30px;\\n}\\n\\n.storybook-page li {\\n  margin-bottom: 8px;\\n}\\n\\n.storybook-page .tip {\\n  display: inline-block;\\n  vertical-align: top;\\n  margin-right: 10px;\\n  border-radius: 1em;\\n  background: #e7fdd8;\\n  padding: 4px 12px;\\n  color: #357a14;\\n  font-weight: 700;\\n  font-size: 11px;\\n  line-height: 12px;\\n}\\n\\n.storybook-page .tip-wrapper {\\n  margin-top: 40px;\\n  margin-bottom: 40px;\\n  font-size: 13px;\\n  line-height: 20px;\\n}\\n\\n.storybook-page .tip-wrapper svg {\\n  display: inline-block;\\n  vertical-align: top;\\n  margin-top: 3px;\\n  margin-right: 4px;\\n  width: 12px;\\n  height: 12px;\\n}\\n\\n.storybook-page .tip-wrapper svg path {\\n  fill: #1ea7fd;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "import type { Meta, StoryObj } from '@storybook/nextjs';\n\nimport { expect, userEvent, within } from 'storybook/test';\n\nimport { Page } from './Page';\n\nconst meta = {\n  title: 'Example/Page',\n  component: Page,\n  parameters: {\n    // More on how to position stories at: https://storybook.js.org/docs/configure/story-layout\n    layout: 'fullscreen',\n  },\n} satisfies Meta<typeof Page>;\n\nexport default meta;\ntype Story = StoryObj<typeof meta>;\n\nexport const LoggedOut: Story = {};\n\n// More on component testing: https://storybook.js.org/docs/writing-tests/interaction-testing\nexport const LoggedIn: Story = {\n  play: async ({ canvasElement }) => {\n    const canvas = within(canvasElement);\n    const loginButton = canvas.getByRole('button', { name: /Log in/i });\n    await expect(loginButton).toBeInTheDocument();\n    await userEvent.click(loginButton);\n    await expect(loginButton).not.toBeInTheDocument();\n\n    const logoutButton = canvas.getByRole('button', { name: /Log out/i });\n    await expect(logoutButton).toBeInTheDocument();\n  },\n};\n", "import React from 'react';\n\nimport { Head<PERSON> } from './Header';\nimport './page.css';\n\ntype User = {\n  name: string;\n};\n\nexport const Page: React.FC = () => {\n  const [user, setUser] = React.useState<User>();\n\n  return (\n    <article>\n      <Header\n        user={user}\n        onLogin={() => setUser({ name: '<PERSON>' })}\n        onLogout={() => setUser(undefined)}\n        onCreateAccount={() => setUser({ name: '<PERSON>' })}\n      />\n\n      <section className=\"storybook-page\">\n        <h2>Pages in Storybook</h2>\n        <p>\n          We recommend building UIs with a{' '}\n          <a href=\"https://componentdriven.org\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <strong>component-driven</strong>\n          </a>{' '}\n          process starting with atomic components and ending with pages.\n        </p>\n        <p>\n          Render pages with mock data. This makes it easy to build and review page states without\n          needing to navigate to them in your app. Here are some handy patterns for managing page\n          data in Storybook:\n        </p>\n        <ul>\n          <li>\n            Use a higher-level connected component. Storybook helps you compose such data from the\n            \"args\" of child component stories\n          </li>\n          <li>\n            Assemble data in the page component from your services. You can mock these services out\n            using Storybook.\n          </li>\n        </ul>\n        <p>\n          Get a guided tutorial on component-driven development at{' '}\n          <a href=\"https://storybook.js.org/tutorials/\" target=\"_blank\" rel=\"noopener noreferrer\">\n            Storybook tutorials\n          </a>\n          . Read more in the{' '}\n          <a href=\"https://storybook.js.org/docs\" target=\"_blank\" rel=\"noopener noreferrer\">\n            docs\n          </a>\n          .\n        </p>\n        <div className=\"tip-wrapper\">\n          <span className=\"tip\">Tip</span> Adjust the width of the canvas with the{' '}\n          <svg width=\"10\" height=\"10\" viewBox=\"0 0 12 12\" xmlns=\"http://www.w3.org/2000/svg\">\n            <g fill=\"none\" fillRule=\"evenodd\">\n              <path\n                d=\"M1.5 5.2h4.8c.3 0 .5.2.5.4v5.1c-.1.2-.3.3-.4.3H1.4a.5.5 0 01-.5-.4V5.7c0-.3.2-.5.5-.5zm0-2.1h6.9c.3 0 .5.2.5.4v7a.5.5 0 01-1 0V4H1.5a.5.5 0 010-1zm0-2.1h9c.3 0 .5.2.5.4v9.1a.5.5 0 01-1 0V2H1.5a.5.5 0 010-1zm4.3 5.2H2V10h3.8V6.2z\"\n                id=\"a\"\n                fill=\"#999\"\n              />\n            </g>\n          </svg>\n          Viewports addon in the toolbar\n        </div>\n      </section>\n    </article>\n  );\n};\n", "\n      import API from \"!../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./page.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\nif (module.hot) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n  if (!a && b || a && !b) {\n    return false;\n  }\n  var p;\n  for (p in a) {\n    if (isNamedExport && p === \"default\") {\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (a[p] !== b[p]) {\n      return false;\n    }\n  }\n  for (p in b) {\n    if (isNamedExport && p === \"default\") {\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (!a[p]) {\n      return false;\n    }\n  }\n  return true;\n};\n    var isNamedExport = !content.locals;\n    var oldLocals = isNamedExport ? namedExport : content.locals;\n\n    module.hot.accept(\n      \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./page.css\",\n      function () {\n        if (!isEqualLocals(oldLocals, isNamedExport ? namedExport : content.locals, isNamedExport)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = isNamedExport ? namedExport : content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\n\nexport * from \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./page.css\";\n       export default content && content.locals ? content.locals : undefined;\n"], "names": [], "sourceRoot": ""}