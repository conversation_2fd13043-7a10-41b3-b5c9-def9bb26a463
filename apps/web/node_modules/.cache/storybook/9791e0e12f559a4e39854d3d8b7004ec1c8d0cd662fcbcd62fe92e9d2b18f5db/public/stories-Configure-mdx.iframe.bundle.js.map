{"version": 3, "file": "stories-Configure-mdx.iframe.bundle.js", "mappings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jiBA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA", "sources": ["webpack://web/./node_modules/@storybook/addon-docs/dist/ sync", "webpack://web/./node_modules/storybook/dist/components/ sync", "webpack://web/./node_modules/storybook/dist/theming/ sync", "webpack://web/./src/stories/Configure.mdx", "webpack://web/./src/stories/assets/accessibility.png", "webpack://web/./src/stories/assets/addon-library.png", "webpack://web/./src/stories/assets/assets.png", "webpack://web/./src/stories/assets/context.png", "webpack://web/./src/stories/assets/discord.svg", "webpack://web/./src/stories/assets/docs.png", "webpack://web/./src/stories/assets/figma-plugin.png", "webpack://web/./src/stories/assets/github.svg", "webpack://web/./src/stories/assets/share.png", "webpack://web/./src/stories/assets/styling.png", "webpack://web/./src/stories/assets/testing.png", "webpack://web/./src/stories/assets/theming.png", "webpack://web/./src/stories/assets/tutorials.svg", "webpack://web/./src/stories/assets/youtube.svg"], "sourcesContent": ["function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = \"./node_modules/@storybook/addon-docs/dist sync recursive\";\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = \"./node_modules/storybook/dist/components sync recursive\";\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = \"./node_modules/storybook/dist/theming sync recursive\";\nmodule.exports = webpackEmptyContext;", "\nimport React from 'react';\n\nimport {Fragment as _Fragment, jsx as _jsx, jsxs as _jsxs} from \"react/jsx-runtime\";\nimport {useMDXComponents as _provideComponents} from \"/Users/<USER>/modern-action-2.0/apps/web/node_modules/@storybook/addon-docs/dist/shims/mdx-react-shim.mjs\";\nimport {Meta} from \"@storybook/addon-docs/blocks\";\nimport Image from \"next/image\";\nimport Github from \"./assets/github.svg\";\nimport Discord from \"./assets/discord.svg\";\nimport Youtube from \"./assets/youtube.svg\";\nimport Tutorials from \"./assets/tutorials.svg\";\nimport Styling from \"./assets/styling.png\";\nimport Context from \"./assets/context.png\";\nimport Assets from \"./assets/assets.png\";\nimport Docs from \"./assets/docs.png\";\nimport Share from \"./assets/share.png\";\nimport FigmaPlugin from \"./assets/figma-plugin.png\";\nimport Testing from \"./assets/testing.png\";\nimport Accessibility from \"./assets/accessibility.png\";\nimport Theming from \"./assets/theming.png\";\nimport AddonLibrary from \"./assets/addon-library.png\";\nexport const RightArrow = () => _jsx(\"svg\", {\n  viewBox: \"0 0 14 14\",\n  width: \"8px\",\n  height: \"14px\",\n  style: {\n    marginLeft: '4px',\n    display: 'inline-block',\n    shapeRendering: 'inherit',\n    verticalAlign: 'middle',\n    fill: 'currentColor',\n    'path fill': 'currentColor'\n  },\n  children: _jsx(\"path\", {\n    d: \"m11.1 7.35-5.5 5.5a.5.5 0 0 1-.7-.7L10.04 7 4.9 1.85a.5.5 0 1 1 .7-.7l5.5 5.5c.2.2.2.5 0 .7Z\"\n  })\n});\nfunction _createMdxContent(props) {\n  const _components = {\n    code: \"code\",\n    h1: \"h1\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxs(_Fragment, {\n    children: [_jsx(Meta, {\n      title: \"Configure your project\"\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"sb-container\",\n      children: [_jsxs(\"div\", {\n        className: \"sb-section-title\",\n        children: [_jsx(_components.h1, {\n          id: \"configure-your-project\",\n          children: \"Configure your project\"\n        }), _jsx(_components.p, {\n          children: \"Because Storybook works separately from your app, you'll need to configure it for your specific stack and setup. Below, explore guides for configuring Storybook with popular frameworks and tools. If you get stuck, learn how you can ask for help from our community.\"\n        })]\n      }), _jsxs(\"div\", {\n        className: \"sb-section\",\n        children: [_jsxs(\"div\", {\n          className: \"sb-section-item\",\n          children: [_jsx(Image, {\n            src: Styling,\n            alt: \"A wall of logos representing different styling technologies\",\n            width: 0,\n            height: 0,\n            style: {\n              width: '100%',\n              height: 'auto'\n            }\n          }), _jsx(\"h4\", {\n            className: \"sb-section-item-heading\",\n            children: \"Add styling and CSS\"\n          }), _jsx(\"p\", {\n            className: \"sb-section-item-paragraph\",\n            children: \"Like with web applications, there are many ways to include CSS within Storybook. Learn more about setting up styling within Storybook.\"\n          }), _jsxs(\"a\", {\n            href: \"https://storybook.js.org/docs/configure/styling-and-css/?renderer=react\",\n            target: \"_blank\",\n            children: [\"Learn more\", _jsx(RightArrow, {})]\n          })]\n        }), _jsxs(\"div\", {\n          className: \"sb-section-item\",\n          children: [_jsx(Image, {\n            width: 0,\n            height: 0,\n            style: {\n              width: '100%',\n              height: 'auto'\n            },\n            src: Context,\n            alt: \"An abstraction representing the composition of data for a component\"\n          }), _jsx(\"h4\", {\n            className: \"sb-section-item-heading\",\n            children: \"Provide context and mocking\"\n          }), _jsx(\"p\", {\n            className: \"sb-section-item-paragraph\",\n            children: \"Often when a story doesn't render, it's because your component is expecting a specific environment or context (like a theme provider) to be available.\"\n          }), _jsxs(\"a\", {\n            href: \"https://storybook.js.org/docs/writing-stories/decorators/?renderer=react#context-for-mocking\",\n            target: \"_blank\",\n            children: [\"Learn more\", _jsx(RightArrow, {})]\n          })]\n        }), _jsxs(\"div\", {\n          className: \"sb-section-item\",\n          children: [_jsx(Image, {\n            width: 0,\n            height: 0,\n            style: {\n              width: '100%',\n              height: 'auto'\n            },\n            src: Assets,\n            alt: \"A representation of typography and image assets\"\n          }), _jsxs(\"div\", {\n            children: [_jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Load assets and resources\"\n            }), _jsxs(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: [\"To link static files (like fonts) to your projects and stories, use the\\n\", _jsx(_components.code, {\n                children: \"staticDirs\"\n              }), \" configuration option to specify folders to load when\\nstarting Storybook.\"]\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/configure/images-and-assets/?renderer=react\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"sb-container\",\n      children: [_jsxs(\"div\", {\n        className: \"sb-section-title\",\n        children: [_jsx(_components.h1, {\n          id: \"do-more-with-storybook\",\n          children: \"Do more with Storybook\"\n        }), _jsx(_components.p, {\n          children: \"Now that you know the basics, let's explore other parts of Storybook that will improve your experience. This list is just to get you started. You can customise Storybook in many ways to fit your needs.\"\n        })]\n      }), _jsx(\"div\", {\n        className: \"sb-section\",\n        children: _jsxs(\"div\", {\n          className: \"sb-features-grid\",\n          children: [_jsxs(\"div\", {\n            className: \"sb-grid-item\",\n            children: [_jsx(Image, {\n              width: 0,\n              height: 0,\n              style: {\n                width: '100%',\n                height: 'auto'\n              },\n              src: Docs,\n              alt: \"A screenshot showing the autodocs tag being set, pointing a docs page being generated\"\n            }), _jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Autodocs\"\n            }), _jsx(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: \"Auto-generate living,\\ninteractive reference documentation from your components and stories.\"\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/writing-docs/autodocs/?renderer=react\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          }), _jsxs(\"div\", {\n            className: \"sb-grid-item\",\n            children: [_jsx(Image, {\n              width: 0,\n              height: 0,\n              style: {\n                width: '100%',\n                height: 'auto'\n              },\n              src: Share,\n              alt: \"A browser window showing a Storybook being published to a chromatic.com URL\"\n            }), _jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Publish to Chromatic\"\n            }), _jsx(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: \"Publish your Storybook to review and collaborate with your entire team.\"\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/sharing/publish-storybook/?renderer=react#publish-storybook-with-chromatic\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          }), _jsxs(\"div\", {\n            className: \"sb-grid-item\",\n            children: [_jsx(Image, {\n              width: 0,\n              height: 0,\n              style: {\n                width: '100%',\n                height: 'auto'\n              },\n              src: FigmaPlugin,\n              alt: \"Windows showing the Storybook plugin in Figma\"\n            }), _jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Figma Plugin\"\n            }), _jsx(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: \"Embed your stories into Figma to cross-reference the design and live\\nimplementation in one place.\"\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/sharing/design-integrations/?renderer=react#embed-storybook-in-figma-with-the-plugin\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          }), _jsxs(\"div\", {\n            className: \"sb-grid-item\",\n            children: [_jsx(Image, {\n              width: 0,\n              height: 0,\n              style: {\n                width: '100%',\n                height: 'auto'\n              },\n              src: Testing,\n              alt: \"Screenshot of tests passing and failing\"\n            }), _jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Testing\"\n            }), _jsx(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: \"Use stories to test a component in all its variations, no matter how\\ncomplex.\"\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/writing-tests/?renderer=react\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          }), _jsxs(\"div\", {\n            className: \"sb-grid-item\",\n            children: [_jsx(Image, {\n              width: 0,\n              height: 0,\n              style: {\n                width: '100%',\n                height: 'auto'\n              },\n              src: Accessibility,\n              alt: \"Screenshot of accessibility tests passing and failing\"\n            }), _jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Accessibility\"\n            }), _jsx(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: \"Automatically test your components for a11y issues as you develop.\"\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/writing-tests/accessibility-testing/?renderer=react\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          }), _jsxs(\"div\", {\n            className: \"sb-grid-item\",\n            children: [_jsx(Image, {\n              width: 0,\n              height: 0,\n              style: {\n                width: '100%',\n                height: 'auto'\n              },\n              src: Theming,\n              alt: \"Screenshot of Storybook in light and dark mode\"\n            }), _jsx(\"h4\", {\n              className: \"sb-section-item-heading\",\n              children: \"Theming\"\n            }), _jsx(\"p\", {\n              className: \"sb-section-item-paragraph\",\n              children: \"Theme Storybook's UI to personalize it to your project.\"\n            }), _jsxs(\"a\", {\n              href: \"https://storybook.js.org/docs/configure/theming/?renderer=react\",\n              target: \"_blank\",\n              children: [\"Learn more\", _jsx(RightArrow, {})]\n            })]\n          })]\n        })\n      })]\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"sb-addon\",\n      children: [_jsxs(\"div\", {\n        className: \"sb-addon-text\",\n        children: [_jsx(\"h4\", {\n          children: \"Addons\"\n        }), _jsx(\"p\", {\n          className: \"sb-section-item-paragraph\",\n          children: \"Integrate your tools with Storybook to connect workflows.\"\n        }), _jsxs(\"a\", {\n          href: \"https://storybook.js.org/addons/\",\n          target: \"_blank\",\n          children: [\"Discover all addons\", _jsx(RightArrow, {})]\n        })]\n      }), _jsx(\"div\", {\n        className: \"sb-addon-img\",\n        children: _jsx(Image, {\n          width: 650,\n          height: 347,\n          src: AddonLibrary,\n          alt: \"Integrate your tools with Storybook to connect workflows.\"\n        })\n      })]\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"sb-section sb-socials\",\n      children: [_jsxs(\"div\", {\n        className: \"sb-section-item\",\n        children: [_jsx(Image, {\n          width: 32,\n          height: 32,\n          layout: \"fixed\",\n          src: Github,\n          alt: \"Github logo\",\n          className: \"sb-explore-image\"\n        }), _jsx(_components.p, {\n          children: \"Join our contributors building the future of UI development.\"\n        }), _jsxs(\"a\", {\n          href: \"https://github.com/storybookjs/storybook\",\n          target: \"_blank\",\n          children: [\"Star on GitHub\", _jsx(RightArrow, {})]\n        })]\n      }), _jsxs(\"div\", {\n        className: \"sb-section-item\",\n        children: [_jsx(Image, {\n          width: 33,\n          height: 32,\n          layout: \"fixed\",\n          src: Discord,\n          alt: \"Discord logo\",\n          className: \"sb-explore-image\"\n        }), _jsxs(\"div\", {\n          children: [_jsx(_components.p, {\n            children: \"Get support and chat with frontend developers.\"\n          }), _jsxs(\"a\", {\n            href: \"https://discord.gg/storybook\",\n            target: \"_blank\",\n            children: [\"Join Discord server\", _jsx(RightArrow, {})]\n          })]\n        })]\n      }), _jsxs(\"div\", {\n        className: \"sb-section-item\",\n        children: [_jsx(Image, {\n          width: 32,\n          height: 32,\n          layout: \"fixed\",\n          src: Youtube,\n          alt: \"Youtube logo\",\n          className: \"sb-explore-image\"\n        }), _jsxs(\"div\", {\n          children: [_jsx(_components.p, {\n            children: \"Watch tutorials, feature previews and interviews.\"\n          }), _jsxs(\"a\", {\n            href: \"https://www.youtube.com/@chromaticui\",\n            target: \"_blank\",\n            children: [\"Watch on YouTube\", _jsx(RightArrow, {})]\n          })]\n        })]\n      }), _jsxs(\"div\", {\n        className: \"sb-section-item\",\n        children: [_jsx(Image, {\n          width: 33,\n          height: 32,\n          layout: \"fixed\",\n          src: Tutorials,\n          alt: \"A book\",\n          className: \"sb-explore-image\"\n        }), _jsx(\"p\", {\n          children: \"Follow guided walkthroughs on for key workflows.\"\n        }), _jsxs(\"a\", {\n          href: \"https://storybook.js.org/tutorials/\",\n          target: \"_blank\",\n          children: [\"Discover tutorials\", _jsx(RightArrow, {})]\n        })]\n      })]\n    }), \"\\n\", _jsx(\"style\", {\n      children: `\n.sb-container {\n  margin-bottom: 48px;\n}\n\n.sb-section {\n  width: 100%;\n  display: flex;\n  flex-direction: row;\n  gap: 20px;\n}\n\nimg {\n  object-fit: cover;\n}\n\n.sb-section-title {\n  margin-bottom: 32px;\n}\n\n.sb-section a:not(h1 a, h2 a, h3 a) {\n  font-size: 14px;\n}\n\n.sb-section-item, .sb-grid-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.sb-section-item-heading {\n  padding-top: 20px !important;\n  padding-bottom: 5px !important;\n  margin: 0 !important;\n}\n.sb-section-item-paragraph {\n  margin: 0;\n  padding-bottom: 10px;\n}\n\n.sb-chevron {\n  margin-left: 5px;\n}\n\n.sb-features-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-gap: 32px 20px;\n}\n\n.sb-socials {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n}\n\n.sb-socials p {\n  margin-bottom: 10px;\n}\n\n.sb-explore-image {\n  max-height: 32px;\n  align-self: flex-start;\n}\n\n.sb-addon {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  position: relative;\n  background-color: #EEF3F8;\n  border-radius: 5px;\n  border: 1px solid rgba(0, 0, 0, 0.05);\n  background: #EEF3F8;\n  height: 180px;\n  margin-bottom: 48px;\n  overflow: hidden;\n}\n\n.sb-addon-text {\n  padding-left: 48px;\n  max-width: 240px;\n}\n\n.sb-addon-text h4 {\n  padding-top: 0px;\n}\n\n.sb-addon-img {\n  position: absolute;\n  left: 345px;\n  top: 0;\n  height: 100%;\n  width: 200%;\n  overflow: hidden;\n}\n\n.sb-addon-img img {\n  width: 650px;\n  transform: rotate(-15deg);\n  margin-left: 40px;\n  margin-top: -72px;\n  box-shadow: 0 0 1px rgba(255, 255, 255, 0);\n  backface-visibility: hidden;\n}\n\n@media screen and (max-width: 800px) {\n  .sb-addon-img {\n    left: 300px;\n  }\n}\n\n@media screen and (max-width: 600px) {\n  .sb-section {\n    flex-direction: column;\n  }\n\n  .sb-features-grid {\n    grid-template-columns: repeat(1, 1fr);\n  }\n\n  .sb-socials {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .sb-addon {\n    height: 280px;\n    align-items: flex-start;\n    padding-top: 32px;\n    overflow: hidden;\n  }\n\n  .sb-addon-text {\n    padding-left: 24px;\n  }\n\n  .sb-addon-img {\n    right: 0;\n    left: 0;\n    top: 130px;\n    bottom: 0;\n    overflow: hidden;\n    height: auto;\n    width: 124%;\n  }\n\n  .sb-addon-img img {\n    width: 1200px;\n    transform: rotate(-12deg);\n    margin-left: 0;\n    margin-top: 48px;\n    margin-bottom: -40px;\n    margin-left: -24px;\n  }\n}\n`\n    })]\n  });\n}\nexport default function MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\n", "export default {\"src\":\"static/media/src/stories/assets/accessibility.png\",\"height\":520,\"width\":890,\"blurDataURL\":\"static/media/src/stories/assets/accessibility.png\"};", "export default {\"src\":\"static/media/src/stories/assets/addon-library.png\",\"height\":2520,\"width\":4720,\"blurDataURL\":\"static/media/src/stories/assets/addon-library.png\"};", "export default {\"src\":\"static/media/src/stories/assets/assets.png\",\"height\":260,\"width\":580,\"blurDataURL\":\"static/media/src/stories/assets/assets.png\"};", "export default {\"src\":\"static/media/src/stories/assets/context.png\",\"height\":260,\"width\":580,\"blurDataURL\":\"static/media/src/stories/assets/context.png\"};", "export default {\"src\":\"static/media/src/stories/assets/discord.svg\",\"height\":32,\"width\":33,\"blurDataURL\":\"static/media/src/stories/assets/discord.svg\"};", "export default {\"src\":\"static/media/src/stories/assets/docs.png\",\"height\":520,\"width\":890,\"blurDataURL\":\"static/media/src/stories/assets/docs.png\"};", "export default {\"src\":\"static/media/src/stories/assets/figma-plugin.png\",\"height\":520,\"width\":890,\"blurDataURL\":\"static/media/src/stories/assets/figma-plugin.png\"};", "export default {\"src\":\"static/media/src/stories/assets/github.svg\",\"height\":32,\"width\":32,\"blurDataURL\":\"static/media/src/stories/assets/github.svg\"};", "export default {\"src\":\"static/media/src/stories/assets/share.png\",\"height\":520,\"width\":890,\"blurDataURL\":\"static/media/src/stories/assets/share.png\"};", "export default {\"src\":\"static/media/src/stories/assets/styling.png\",\"height\":260,\"width\":580,\"blurDataURL\":\"static/media/src/stories/assets/styling.png\"};", "export default {\"src\":\"static/media/src/stories/assets/testing.png\",\"height\":520,\"width\":890,\"blurDataURL\":\"static/media/src/stories/assets/testing.png\"};", "export default {\"src\":\"static/media/src/stories/assets/theming.png\",\"height\":520,\"width\":890,\"blurDataURL\":\"static/media/src/stories/assets/theming.png\"};", "export default {\"src\":\"static/media/src/stories/assets/tutorials.svg\",\"height\":32,\"width\":33,\"blurDataURL\":\"static/media/src/stories/assets/tutorials.svg\"};", "export default {\"src\":\"static/media/src/stories/assets/youtube.svg\",\"height\":32,\"width\":32,\"blurDataURL\":\"static/media/src/stories/assets/youtube.svg\"};"], "names": [], "sourceRoot": ""}