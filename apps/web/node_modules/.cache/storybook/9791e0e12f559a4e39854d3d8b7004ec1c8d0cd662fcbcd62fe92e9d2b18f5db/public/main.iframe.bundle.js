(self["webpackChunkweb"] = self["webpackChunkweb"] || []).push([["main"],{

/***/ "./.storybook/preview.ts":
/*!*******************************!*\
  !*** ./.storybook/preview.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");

const preview = {
    parameters: {
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/i
            }
        }
    }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (preview);


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./node_modules/@storybook/nextjs/dist sync recursive":
/*!***************************************************!*\
  !*** ./node_modules/@storybook/nextjs/dist/ sync ***!
  \***************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "./node_modules/@storybook/nextjs/dist sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:\\/src(?:\\/(?%21\\.)(?:(?:(?%21(?:^%7C\\/)\\.).)*?)\\/%7C\\/%7C$)(?%21\\.)(?=.)[^/]*?\\.mdx)$":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./src/ lazy ^\.\/.*$ include: (?%21.*node_modules)(?:\/src(?:\/(?%21\.)(?:(?:(?%21(?:^%7C\/)\.).)*?)\/%7C\/%7C$)(?%21\.)(?=.)[^/]*?\.mdx)$ chunkName: [request] namespace object ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./stories/Configure.mdx": [
		"./src/stories/Configure.mdx",
		"vendors-node_modules_storybook_addon-docs_dist_blocks_mjs",
		"vendors-node_modules_mdx-js_react_lib_index_js-node_modules_storybook_nextjs_dist_images_next-74b645",
		"stories-Configure-mdx"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:\\/src(?:\\/(?%21\\.)(?:(?:(?%21(?:^%7C\\/)\\.).)*?)\\/%7C\\/%7C$)(?%21\\.)(?=.)[^/]*?\\.mdx)$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:\\/src(?:\\/(?%21\\.)(?:(?:(?%21(?:^%7C\\/)\\.).)*?)\\/%7C\\/%7C$)(?%21\\.)(?=.)[^/]*?\\.stories\\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./src/ lazy ^\.\/.*$ include: (?%21.*node_modules)(?:\/src(?:\/(?%21\.)(?:(?:(?%21(?:^%7C\/)\.).)*?)\/%7C\/%7C$)(?%21\.)(?=.)[^/]*?\.stories\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$ chunkName: [request] namespace object ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./stories/Button.stories": [
		"./src/stories/Button.stories.ts",
		"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_sour-6083d2",
		"stories-Button-stories"
	],
	"./stories/Button.stories.ts": [
		"./src/stories/Button.stories.ts",
		"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_sour-6083d2",
		"stories-Button-stories"
	],
	"./stories/Header.stories": [
		"./src/stories/Header.stories.ts",
		"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_sour-6083d2",
		"src_stories_Header_tsx",
		"stories-Header-stories"
	],
	"./stories/Header.stories.ts": [
		"./src/stories/Header.stories.ts",
		"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_sour-6083d2",
		"src_stories_Header_tsx",
		"stories-Header-stories"
	],
	"./stories/Page.stories": [
		"./src/stories/Page.stories.ts",
		"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_sour-6083d2",
		"src_stories_Header_tsx",
		"stories-Page-stories"
	],
	"./stories/Page.stories.ts": [
		"./src/stories/Page.stories.ts",
		"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_sour-6083d2",
		"src_stories_Header_tsx",
		"stories-Page-stories"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:\\/src(?:\\/(?%21\\.)(?:(?:(?%21(?:^%7C\\/)\\.).)*?)\\/%7C\\/%7C$)(?%21\\.)(?=.)[^/]*?\\.stories\\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./storybook-config-entry.js":
/*!***********************************!*\
  !*** ./storybook-config-entry.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var storybook_internal_channels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! storybook/internal/channels */ "storybook/internal/channels");
/* harmony import */ var storybook_internal_channels__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(storybook_internal_channels__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var storybook_internal_core_events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! storybook/internal/core-events */ "storybook/internal/core-events");
/* harmony import */ var storybook_internal_core_events__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(storybook_internal_core_events__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var storybook_internal_csf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! storybook/internal/csf */ "./node_modules/storybook/dist/csf/index.js");
/* harmony import */ var _storybook_global__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @storybook/global */ "@storybook/global");
/* harmony import */ var _storybook_global__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_storybook_global__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var storybook_preview_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! storybook/preview-api */ "storybook/preview-api");
/* harmony import */ var storybook_preview_api__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(storybook_preview_api__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _storybook_stories_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./storybook-stories.js */ "./storybook-stories.js");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");










const getProjectAnnotations = () => {
  const previewAnnotations = [__webpack_require__(/*! ./node_modules/@storybook/react/dist/entry-preview.mjs */ "./node_modules/@storybook/react/dist/entry-preview.mjs"),__webpack_require__(/*! ./node_modules/@storybook/react/dist/entry-preview-argtypes.mjs */ "./node_modules/@storybook/react/dist/entry-preview-argtypes.mjs"),__webpack_require__(/*! ./node_modules/@storybook/react/dist/entry-preview-docs.mjs */ "./node_modules/@storybook/react/dist/entry-preview-docs.mjs"),__webpack_require__(/*! ./node_modules/@storybook/nextjs/dist/preview.mjs */ "./node_modules/@storybook/nextjs/dist/preview.mjs"),__webpack_require__(/*! ./node_modules/@storybook/addon-docs/dist/preview.mjs */ "./node_modules/@storybook/addon-docs/dist/preview.mjs"),__webpack_require__(/*! ./.storybook/preview.ts */ "./.storybook/preview.ts")];
  // the last one in this array is the user preview
  const userPreview = previewAnnotations[previewAnnotations.length - 1]?.default;

  if ((0,storybook_internal_csf__WEBPACK_IMPORTED_MODULE_5__.isPreview)(userPreview)) {
    return userPreview.composed;
  }

  return (0,storybook_preview_api__WEBPACK_IMPORTED_MODULE_3__.composeConfigs)(previewAnnotations);
};

const channel = (0,storybook_internal_channels__WEBPACK_IMPORTED_MODULE_0__.createBrowserChannel)({ page: 'preview' });
storybook_preview_api__WEBPACK_IMPORTED_MODULE_3__.addons.setChannel(channel);

if (_storybook_global__WEBPACK_IMPORTED_MODULE_2__.global.CONFIG_TYPE === 'DEVELOPMENT') {
  window.__STORYBOOK_SERVER_CHANNEL__ = channel;
}

const preview = new storybook_preview_api__WEBPACK_IMPORTED_MODULE_3__.PreviewWeb(_storybook_stories_js__WEBPACK_IMPORTED_MODULE_4__.importFn, getProjectAnnotations);

window.__STORYBOOK_PREVIEW__ = preview;
window.__STORYBOOK_STORY_STORE__ = preview.storyStore;
window.__STORYBOOK_ADDONS_CHANNEL__ = channel;

if (true) {
  module.hot.addStatusHandler((status) => {
    if (status === 'idle') {
      preview.channel.emit(storybook_internal_core_events__WEBPACK_IMPORTED_MODULE_1__.STORY_HOT_UPDATED);
    }
  });

  module.hot.accept(/*! ./storybook-stories.js */ "./storybook-stories.js", __WEBPACK_OUTDATED_DEPENDENCIES__ => { /* harmony import */ _storybook_stories_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./storybook-stories.js */ "./storybook-stories.js");
(() => {
    // importFn has changed so we need to patch the new one in
    preview.onStoriesChanged({ importFn: _storybook_stories_js__WEBPACK_IMPORTED_MODULE_4__.importFn });
  })(__WEBPACK_OUTDATED_DEPENDENCIES__); });

  module.hot.accept([/*! ./node_modules/@storybook/react/dist/entry-preview.mjs */ "./node_modules/@storybook/react/dist/entry-preview.mjs",/*! ./node_modules/@storybook/react/dist/entry-preview-argtypes.mjs */ "./node_modules/@storybook/react/dist/entry-preview-argtypes.mjs",/*! ./node_modules/@storybook/react/dist/entry-preview-docs.mjs */ "./node_modules/@storybook/react/dist/entry-preview-docs.mjs",/*! ./node_modules/@storybook/nextjs/dist/preview.mjs */ "./node_modules/@storybook/nextjs/dist/preview.mjs",/*! ./node_modules/@storybook/addon-docs/dist/preview.mjs */ "./node_modules/@storybook/addon-docs/dist/preview.mjs",/*! ./.storybook/preview.ts */ "./.storybook/preview.ts"], __WEBPACK_OUTDATED_DEPENDENCIES__ => { (() => {
    // getProjectAnnotations has changed so we need to patch the new one in
    preview.onGetProjectAnnotationsChanged({ getProjectAnnotations });
  })(__WEBPACK_OUTDATED_DEPENDENCIES__); });
}


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./storybook-stories.js":
/*!******************************!*\
  !*** ./storybook-stories.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   importFn: () => (/* binding */ importFn)
/* harmony export */ });
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");

const pipeline = (x) => x();

const importers = [
  async (path) => {
    if (!/^\.[\\/](?:src(?:\/(?!\.)(?:(?:(?!(?:^|\/)\.).)*?)\/|\/|$)(?!\.)(?=.)[^/]*?\.mdx)$/.exec(path)) {
      return;
    }
  
    const pathRemainder = path.substring(6);
    return __webpack_require__("./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:\\/src(?:\\/(?%21\\.)(?:(?:(?%21(?:^%7C\\/)\\.).)*?)\\/%7C\\/%7C$)(?%21\\.)(?=.)[^/]*?\\.mdx)$")("./" + pathRemainder);
  }
  ,
  async (path) => {
    if (!/^\.[\\/](?:src(?:\/(?!\.)(?:(?:(?!(?:^|\/)\.).)*?)\/|\/|$)(?!\.)(?=.)[^/]*?\.stories\.(js|jsx|mjs|ts|tsx))$/.exec(path)) {
      return;
    }
  
    const pathRemainder = path.substring(6);
    return __webpack_require__("./src lazy recursive ^\\.\\/.*$ include: (?%21.*node_modules)(?:\\/src(?:\\/(?%21\\.)(?:(?:(?%21(?:^%7C\\/)\\.).)*?)\\/%7C\\/%7C$)(?%21\\.)(?=.)[^/]*?\\.stories\\.(js%7Cjsx%7Cmjs%7Cts%7Ctsx))$")("./" + pathRemainder);
  }
  
];

async function importFn(path) {
  for (let i = 0; i < importers.length; i++) {
    const moduleExports = await pipeline(() => importers[i](path));
    if (moduleExports) {
      return moduleExports;
    }
  }
}

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "?19e6":
/*!**********************!*\
  !*** util (ignored) ***!
  \**********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?3e83":
/*!**********************!*\
  !*** util (ignored) ***!
  \**********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?c969":
/*!********************!*\
  !*** fs (ignored) ***!
  \********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "@storybook/global":
/*!**********************************************!*\
  !*** external "__STORYBOOK_MODULE_GLOBAL__" ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_GLOBAL__;

/***/ }),

/***/ "storybook/internal/channels":
/*!************************************************!*\
  !*** external "__STORYBOOK_MODULE_CHANNELS__" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_CHANNELS__;

/***/ }),

/***/ "storybook/internal/client-logger":
/*!*****************************************************!*\
  !*** external "__STORYBOOK_MODULE_CLIENT_LOGGER__" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_CLIENT_LOGGER__;

/***/ }),

/***/ "storybook/internal/core-events":
/*!***************************************************!*\
  !*** external "__STORYBOOK_MODULE_CORE_EVENTS__" ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_CORE_EVENTS__;

/***/ }),

/***/ "storybook/internal/preview-errors":
/*!******************************************************************!*\
  !*** external "__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__;

/***/ }),

/***/ "storybook/preview-api":
/*!***************************************************!*\
  !*** external "__STORYBOOK_MODULE_PREVIEW_API__" ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_PREVIEW_API__;

/***/ }),

/***/ "storybook/test":
/*!********************************************!*\
  !*** external "__STORYBOOK_MODULE_TEST__" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = __STORYBOOK_MODULE_TEST__;

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_pmmmwh_react-refresh-webpack-plugin_client_ReactRefreshEntry_js-node_mod-b7d0c3"], () => (__webpack_exec__("./node_modules/@pmmmwh/react-refresh-webpack-plugin/client/ReactRefreshEntry.js"), __webpack_exec__("./node_modules/webpack-hot-middleware/client.js?reload=true&quiet=false&overlay={\"errors\":true,\"warnings\":false,\"runtimeErrors\":false}&noInfo=true"), __webpack_exec__("./storybook-config-entry.js")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.iframe.bundle.js.map