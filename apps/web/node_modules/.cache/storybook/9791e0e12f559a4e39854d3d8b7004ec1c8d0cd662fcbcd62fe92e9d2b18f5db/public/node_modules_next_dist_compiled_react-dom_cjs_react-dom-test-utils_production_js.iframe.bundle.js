"use strict";
(self["webpackChunkweb"] = self["webpackChunkweb"] || []).push([["node_modules_next_dist_compiled_react-dom_cjs_react-dom-test-utils_production_js"],{

/***/ "./node_modules/next/dist/compiled/react-dom/cjs/react-dom-test-utils.production.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react-dom/cjs/react-dom-test-utils.production.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

/* provided dependency */ var console = __webpack_require__(/*! ./node_modules/console-browserify/index.js */ "./node_modules/console-browserify/index.js");
/**
 * @license React
 * react-dom-test-utils.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */


var React = __webpack_require__(/*! next/dist/compiled/react */ "./node_modules/next/dist/compiled/react/index.js"),
  didWarnAboutUsingAct = !1;
exports.act = function (callback) {
  !1 === didWarnAboutUsingAct &&
    ((didWarnAboutUsingAct = !0),
    console.error(
      "`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info."
    ));
  return React.act(callback);
};


/***/ })

}]);
//# sourceMappingURL=node_modules_next_dist_compiled_react-dom_cjs_react-dom-test-utils_production_js.iframe.bundle.js.map