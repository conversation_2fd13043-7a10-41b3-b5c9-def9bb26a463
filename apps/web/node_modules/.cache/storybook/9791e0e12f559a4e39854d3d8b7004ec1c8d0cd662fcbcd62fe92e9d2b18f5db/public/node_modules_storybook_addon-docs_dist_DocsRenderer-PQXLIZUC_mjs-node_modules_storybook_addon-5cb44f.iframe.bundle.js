(self["webpackChunkweb"] = self["webpackChunkweb"] || []).push([["node_modules_storybook_addon-docs_dist_DocsRenderer-PQXLIZUC_mjs-node_modules_storybook_addon-5cb44f"],{

/***/ "./node_modules/@storybook/addon-docs/dist sync recursive":
/*!*******************************************************!*\
  !*** ./node_modules/@storybook/addon-docs/dist/ sync ***!
  \*******************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "./node_modules/@storybook/addon-docs/dist sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./node_modules/@storybook/addon-docs/dist/DocsRenderer-PQXLIZUC.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@storybook/addon-docs/dist/DocsRenderer-PQXLIZUC.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DocsRenderer: () => (/* binding */ DocsRenderer),
/* harmony export */   defaultComponents: () => (/* binding */ defaultComponents)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _storybook_react_dom_shim__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @storybook/react-dom-shim */ "./node_modules/@storybook/react-dom-shim/dist/react-18.mjs");
/* harmony import */ var _storybook_addon_docs_blocks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @storybook/addon-docs/blocks */ "./node_modules/@storybook/addon-docs/dist/blocks.mjs");




var defaultComponents={code:_storybook_addon_docs_blocks__WEBPACK_IMPORTED_MODULE_1__.CodeOrSourceMdx,a:_storybook_addon_docs_blocks__WEBPACK_IMPORTED_MODULE_1__.AnchorMdx,..._storybook_addon_docs_blocks__WEBPACK_IMPORTED_MODULE_1__.HeadersMdx},ErrorBoundary=class extends react__WEBPACK_IMPORTED_MODULE_0__.Component{constructor(){super(...arguments);this.state={hasError:!1};}static getDerivedStateFromError(){return {hasError:!0}}componentDidCatch(err){let{showException}=this.props;showException(err);}render(){let{hasError}=this.state,{children}=this.props;return hasError?null:react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment),null,children)}},DocsRenderer=class{constructor(){this.render=async(context,docsParameter,element)=>{let components={...defaultComponents,...docsParameter?.components},TDocs=_storybook_addon_docs_blocks__WEBPACK_IMPORTED_MODULE_1__.Docs;return new Promise((resolve,reject)=>{__webpack_require__.e(/*! import() */ "node_modules_mdx-js_react_index_js").then(__webpack_require__.bind(__webpack_require__, /*! @mdx-js/react */ "./node_modules/@mdx-js/react/index.js")).then(({MDXProvider})=>(0,_storybook_react_dom_shim__WEBPACK_IMPORTED_MODULE_2__.renderElement)(react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ErrorBoundary,{showException:reject,key:Math.random()},react__WEBPACK_IMPORTED_MODULE_0___default().createElement(MDXProvider,{components},react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TDocs,{context,docsParameter}))),element)).then(()=>resolve());})},this.unmount=element=>{(0,_storybook_react_dom_shim__WEBPACK_IMPORTED_MODULE_2__.unmountElement)(element);};}};




/***/ }),

/***/ "./node_modules/storybook/dist/components sync recursive":
/*!******************************************************!*\
  !*** ./node_modules/storybook/dist/components/ sync ***!
  \******************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "./node_modules/storybook/dist/components sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./node_modules/storybook/dist/theming sync recursive":
/*!***************************************************!*\
  !*** ./node_modules/storybook/dist/theming/ sync ***!
  \***************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "./node_modules/storybook/dist/theming sync recursive";
module.exports = webpackEmptyContext;

/***/ })

}]);
//# sourceMappingURL=node_modules_storybook_addon-docs_dist_DocsRenderer-PQXLIZUC_mjs-node_modules_storybook_addon-5cb44f.iframe.bundle.js.map