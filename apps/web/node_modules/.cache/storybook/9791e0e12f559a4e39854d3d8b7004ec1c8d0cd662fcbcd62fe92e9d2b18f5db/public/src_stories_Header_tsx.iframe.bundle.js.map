{"version": 3, "file": "src_stories_Header_tsx.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;ACvCA;AAeA;AAQA;AACA;AAEA;AAEA;;;;;;;;AAAA;AADA;AAAA;AAAA;AAAA;;AAGA;;;;;;AAGA;;;;;;;;AAKA;AAtBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChBA;AACA;AAaA;AAEA;AAAA;;AACA;;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;AACA;AACA;AACA;;;;;;AAEA;AACA;AACA;;;;;;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;AAIA;AAAA;;;;;;;;;;;;AAEA;AACA;;AAEA;AAAA;;AAAA;AACA;AAAA;;;;;;AAAA;;;;;;;AAEA;AAAA;AAAA;AAAA;;;;;;;AAGA;;AACA;AAAA;AAAA;AAAA;;;;;;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAvCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://web/./src/stories/button.css", "webpack://web/./src/stories/header.css", "webpack://web/./src/stories/Button.tsx", "webpack://web/./src/stories/Header.tsx", "webpack://web/./src/stories/button.css?8690", "webpack://web/./src/stories/header.css?8b33"], "sourcesContent": ["// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.storybook-button {\n  display: inline-block;\n  cursor: pointer;\n  border: 0;\n  border-radius: 3em;\n  font-weight: 700;\n  line-height: 1;\n  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n}\n.storybook-button--primary {\n  background-color: #555ab9;\n  color: white;\n}\n.storybook-button--secondary {\n  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset;\n  background-color: transparent;\n  color: #333;\n}\n.storybook-button--small {\n  padding: 10px 16px;\n  font-size: 12px;\n}\n.storybook-button--medium {\n  padding: 11px 20px;\n  font-size: 14px;\n}\n.storybook-button--large {\n  padding: 12px 24px;\n  font-size: 16px;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/stories/button.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,qBAAqB;EACrB,eAAe;EACf,SAAS;EACT,kBAAkB;EAClB,gBAAgB;EAChB,cAAc;EACd,0EAA0E;AAC5E;AACA;EACE,yBAAyB;EACzB,YAAY;AACd;AACA;EACE,qDAAqD;EACrD,6BAA6B;EAC7B,WAAW;AACb;AACA;EACE,kBAAkB;EAClB,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,eAAe;AACjB\",\"sourcesContent\":[\".storybook-button {\\n  display: inline-block;\\n  cursor: pointer;\\n  border: 0;\\n  border-radius: 3em;\\n  font-weight: 700;\\n  line-height: 1;\\n  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;\\n}\\n.storybook-button--primary {\\n  background-color: #555ab9;\\n  color: white;\\n}\\n.storybook-button--secondary {\\n  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset;\\n  background-color: transparent;\\n  color: #333;\\n}\\n.storybook-button--small {\\n  padding: 10px 16px;\\n  font-size: 12px;\\n}\\n.storybook-button--medium {\\n  padding: 11px 20px;\\n  font-size: 14px;\\n}\\n.storybook-button--large {\\n  padding: 12px 24px;\\n  font-size: 16px;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.storybook-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n  padding: 15px 20px;\n  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n}\n\n.storybook-header svg {\n  display: inline-block;\n  vertical-align: top;\n}\n\n.storybook-header h1 {\n  display: inline-block;\n  vertical-align: top;\n  margin: 6px 0 6px 10px;\n  font-weight: 700;\n  font-size: 20px;\n  line-height: 1;\n}\n\n.storybook-header button + button {\n  margin-left: 10px;\n}\n\n.storybook-header .welcome {\n  margin-right: 10px;\n  color: #333;\n  font-size: 14px;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/stories/header.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,2CAA2C;EAC3C,kBAAkB;EAClB,0EAA0E;AAC5E;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;AACrB;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,sBAAsB;EACtB,gBAAgB;EAChB,eAAe;EACf,cAAc;AAChB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,eAAe;AACjB\",\"sourcesContent\":[\".storybook-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n  padding: 15px 20px;\\n  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;\\n}\\n\\n.storybook-header svg {\\n  display: inline-block;\\n  vertical-align: top;\\n}\\n\\n.storybook-header h1 {\\n  display: inline-block;\\n  vertical-align: top;\\n  margin: 6px 0 6px 10px;\\n  font-weight: 700;\\n  font-size: 20px;\\n  line-height: 1;\\n}\\n\\n.storybook-header button + button {\\n  margin-left: 10px;\\n}\\n\\n.storybook-header .welcome {\\n  margin-right: 10px;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "import './button.css';\n\nexport interface ButtonProps {\n  /** Is this the principal call to action on the page? */\n  primary?: boolean;\n  /** What background color to use */\n  backgroundColor?: string;\n  /** How large should the button be? */\n  size?: 'small' | 'medium' | 'large';\n  /** Button contents */\n  label: string;\n  /** Optional click handler */\n  onClick?: () => void;\n}\n\n/** Primary UI component for user interaction */\nexport const Button = ({\n  primary = false,\n  size = 'medium',\n  backgroundColor,\n  label,\n  ...props\n}: ButtonProps) => {\n  const mode = primary ? 'storybook-button--primary' : 'storybook-button--secondary';\n  return (\n    <button\n      type=\"button\"\n      className={['storybook-button', `storybook-button--${size}`, mode].join(' ')}\n      {...props}\n    >\n      {label}\n      <style jsx>{`\n        button {\n          background-color: ${backgroundColor};\n        }\n      `}</style>\n    </button>\n  );\n};\n", "import { Button } from './Button';\nimport './header.css';\n\ntype User = {\n  name: string;\n};\n\nexport interface HeaderProps {\n  user?: User;\n  onLogin?: () => void;\n  onLogout?: () => void;\n  onCreateAccount?: () => void;\n}\n\nexport const Header = ({ user, onLogin, onLogout, onCreateAccount }: HeaderProps) => (\n  <header>\n    <div className=\"storybook-header\">\n      <div>\n        <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" xmlns=\"http://www.w3.org/2000/svg\">\n          <g fill=\"none\" fillRule=\"evenodd\">\n            <path\n              d=\"M10 0h12a10 10 0 0110 10v12a10 10 0 01-10 10H10A10 10 0 010 22V10A10 10 0 0110 0z\"\n              fill=\"#FFF\"\n            />\n            <path\n              d=\"M5.3 10.6l10.4 6v11.1l-10.4-6v-11zm11.4-6.2l9.7 5.5-9.7 5.6V4.4z\"\n              fill=\"#555AB9\"\n            />\n            <path\n              d=\"M27.2 10.6v11.2l-10.5 6V16.5l10.5-6zM15.7 4.4v11L6 10l9.7-5.5z\"\n              fill=\"#91BAF8\"\n            />\n          </g>\n        </svg>\n        <h1>Acme</h1>\n      </div>\n      <div>\n        {user ? (\n          <>\n            <span className=\"welcome\">\n              Welcome, <b>{user.name}</b>!\n            </span>\n            <Button size=\"small\" onClick={onLogout} label=\"Log out\" />\n          </>\n        ) : (\n          <>\n            <Button size=\"small\" onClick={onLogin} label=\"Log in\" />\n            <Button primary size=\"small\" onClick={onCreateAccount} label=\"Sign up\" />\n          </>\n        )}\n      </div>\n    </div>\n  </header>\n);\n", "\n      import API from \"!../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./button.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\nif (module.hot) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n  if (!a && b || a && !b) {\n    return false;\n  }\n  var p;\n  for (p in a) {\n    if (isNamedExport && p === \"default\") {\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (a[p] !== b[p]) {\n      return false;\n    }\n  }\n  for (p in b) {\n    if (isNamedExport && p === \"default\") {\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (!a[p]) {\n      return false;\n    }\n  }\n  return true;\n};\n    var isNamedExport = !content.locals;\n    var oldLocals = isNamedExport ? namedExport : content.locals;\n\n    module.hot.accept(\n      \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./button.css\",\n      function () {\n        if (!isEqualLocals(oldLocals, isNamedExport ? namedExport : content.locals, isNamedExport)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = isNamedExport ? namedExport : content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\n\nexport * from \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./button.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./header.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\nif (module.hot) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n  if (!a && b || a && !b) {\n    return false;\n  }\n  var p;\n  for (p in a) {\n    if (isNamedExport && p === \"default\") {\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (a[p] !== b[p]) {\n      return false;\n    }\n  }\n  for (p in b) {\n    if (isNamedExport && p === \"default\") {\n      // eslint-disable-next-line no-continue\n      continue;\n    }\n    if (!a[p]) {\n      return false;\n    }\n  }\n  return true;\n};\n    var isNamedExport = !content.locals;\n    var oldLocals = isNamedExport ? namedExport : content.locals;\n\n    module.hot.accept(\n      \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./header.css\",\n      function () {\n        if (!isEqualLocals(oldLocals, isNamedExport ? namedExport : content.locals, isNamedExport)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = isNamedExport ? namedExport : content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\n\nexport * from \"!!../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].use[1]!../../node_modules/postcss-loader/dist/cjs.js!./header.css\";\n       export default content && content.locals ? content.locals : undefined;\n"], "names": [], "sourceRoot": ""}