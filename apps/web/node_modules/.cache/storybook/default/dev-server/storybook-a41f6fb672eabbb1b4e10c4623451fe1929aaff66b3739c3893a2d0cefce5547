{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "2W-MgHbOx3nmDC4y72RKG", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa", "payload": {"eventType": "dev"}, "context": {"inCI": false, "platform": "macOS", "nodeVersion": "22.17.1", "storybookVersion": "9.0.17", "cliVersion": "9.0.17"}}, "timestamp": 1752787637260}, "init-step": {"body": {"eventType": "init-step", "eventId": "z0XspqVnBbrRg7K9CtVxg", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa", "metadata": {"generatedAt": 1752787598895, "userSince": 1752787598885, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.1"}, "testPackages": {"@playwright/test": "1.54.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "jest": "30.0.4", "jest-environment-jsdom": "30.0.4"}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm"}, "storybookVersionSpecifier": "9.0.17", "language": "typescript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"inCI": false, "platform": "macOS", "nodeVersion": "22.17.1", "storybookVersion": "9.0.17", "cliVersion": "9.0.17", "anonymousId": "282403028cce300058405a82b0d348bb23170b7c43a03ee1ac64d5cda616e38e"}}, "timestamp": 1752787599376}, "init": {"body": {"eventType": "init", "eventId": "1W0-SxGRSCszrjrO-Imf-", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa", "metadata": {"generatedAt": 1752787598895, "userSince": 1752787598885, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.1"}, "testPackages": {"@playwright/test": "1.54.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "jest": "30.0.4", "jest-environment-jsdom": "30.0.4"}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm"}, "storybookVersionSpecifier": "9.0.17", "language": "typescript"}, "payload": {"projectType": "NEXTJS", "features": {"dev": true, "docs": true, "test": false, "onboarding": true}, "newUser": true}, "context": {"inCI": false, "platform": "macOS", "nodeVersion": "22.17.1", "storybookVersion": "9.0.17", "cliVersion": "9.0.17", "anonymousId": "282403028cce300058405a82b0d348bb23170b7c43a03ee1ac64d5cda616e38e"}}, "timestamp": 1752787634510}, "version-update": {"body": {"eventType": "version-update", "eventId": "1W3iOX-5oMpx_Gx0AlmfA", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa", "metadata": {"generatedAt": 1752787638615, "userSince": 1752787598885, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.1"}, "testPackages": {"@playwright/test": "1.54.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "jest": "30.0.4", "jest-environment-jsdom": "30.0.4"}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.17", "language": "typescript", "storybookPackages": {"@storybook/nextjs": {"version": "9.0.17"}, "eslint-plugin-storybook": {"version": "9.0.17"}, "storybook": {"version": "9.0.17"}}, "addons": {"@storybook/addon-docs": {"version": "9.0.17"}, "@storybook/addon-onboarding": {"version": "9.0.17"}}}, "payload": {}, "context": {"inCI": false, "platform": "macOS", "nodeVersion": "22.17.1", "storybookVersion": "9.0.17", "cliVersion": "9.0.17", "anonymousId": "282403028cce300058405a82b0d348bb23170b7c43a03ee1ac64d5cda616e38e"}}, "timestamp": 1752787640244}, "dev": {"body": {"eventType": "dev", "eventId": "b3AUTFqgXGzGbCAXVD9n1", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa", "metadata": {"generatedAt": 1752787638615, "userSince": 1752787598885, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.1"}, "testPackages": {"@playwright/test": "1.54.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "jest": "30.0.4", "jest-environment-jsdom": "30.0.4"}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.17", "language": "typescript", "storybookPackages": {"@storybook/nextjs": {"version": "9.0.17"}, "eslint-plugin-storybook": {"version": "9.0.17"}, "storybook": {"version": "9.0.17"}}, "addons": {"@storybook/addon-docs": {"version": "9.0.17"}, "@storybook/addon-onboarding": {"version": "9.0.17"}}}, "payload": {"precedingUpgrade": {"timestamp": 1752787634510, "eventType": "init", "eventId": "1W0-SxGRSCszrjrO-Imf-", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa"}, "versionStatus": "success", "storyIndex": {"storyCount": 0, "componentCount": 0, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 0, "mdxCount": 0, "exampleStoryCount": 8, "exampleDocsCount": 3, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "svelteCsfV4Count": 0, "svelteCsfV5Count": 0, "version": 5}, "storyStats": {}}, "context": {"inCI": false, "platform": "macOS", "nodeVersion": "22.17.1", "storybookVersion": "9.0.17", "cliVersion": "9.0.17", "anonymousId": "282403028cce300058405a82b0d348bb23170b7c43a03ee1ac64d5cda616e38e"}}, "timestamp": 1752787651435}, "addon-onboarding": {"body": {"eventType": "addon-onboarding", "eventId": "jLUn5ug9A5W-KZA7sHGdc", "sessionId": "ey6tQTuzaJt2S9Gxqnuxa", "metadata": {"generatedAt": 1752787638615, "userSince": 1752787598885, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.1"}, "testPackages": {"@playwright/test": "1.54.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "jest": "30.0.4", "jest-environment-jsdom": "30.0.4"}, "hasRouterPackage": true, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.17", "language": "typescript", "storybookPackages": {"@storybook/nextjs": {"version": "9.0.17"}, "eslint-plugin-storybook": {"version": "9.0.17"}, "storybook": {"version": "9.0.17"}}, "addons": {"@storybook/addon-docs": {"version": "9.0.17"}, "@storybook/addon-onboarding": {"version": "9.0.17"}}}, "payload": {"step": "2:Controls", "addonVersion": "9.0.17"}, "context": {"inCI": false, "platform": "macOS", "nodeVersion": "22.17.1", "storybookVersion": "9.0.17", "cliVersion": "9.0.17", "anonymousId": "282403028cce300058405a82b0d348bb23170b7c43a03ee1ac64d5cda616e38e"}}, "timestamp": 1752787667683}}}