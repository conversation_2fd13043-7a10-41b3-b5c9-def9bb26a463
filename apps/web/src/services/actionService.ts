// Action service for handling action-related API calls
import { actionApi, ActionCreateRequest, ActionCreateResponse } from './apiClient';
import { ActionFormData } from '../components/shared/ActionModal';
import { Campaign, Official } from '../types';

export interface CreateActionInput {
  formData: ActionFormData;
  campaign: Campaign;
  officials: Official[];
  userZipCode?: string;
}

export interface ActionSubmissionResult {
  success: boolean;
  actions: ActionCreateResponse[];
  errors?: string[];
}

/**
 * Creates actions for all officials in a campaign
 */
export const createAction = async ({
  formData,
  campaign,
  officials,
  userZipCode
}: CreateActionInput): Promise<ActionSubmissionResult> => {
  const results: ActionCreateResponse[] = [];
  const errors: string[] = [];

  // Create an action for each official
  for (const official of officials) {
    try {
      const actionData: ActionCreateRequest = {
        subject: `Re: ${campaign.bill.title} (${campaign.bill.bill_number})`,
        message: formData.message,
        action_type: '<PERSON><PERSON><PERSON>',
        user_name: extractNameFromEmail(formData.email),
        user_email: formData.email,
        user_zip_code: userZipCode,
        campaign_id: campaign.id,
        official_id: official.id,
        contact_email: official.email,
        contact_phone: official.phone,
        contact_address: official.office_address,
      };

      const result = await actionApi.createAction(actionData);
      results.push(result);
    } catch (error) {
      console.error(`Failed to create action for official ${official.name}:`, error);
      errors.push(`Failed to send message to ${official.name}`);
    }
  }

  return {
    success: results.length > 0,
    actions: results,
    errors: errors.length > 0 ? errors : undefined
  };
};

/**
 * Extracts a name from an email address
 * This is a simple implementation - in production you'd want better name handling
 */
function extractNameFromEmail(email: string): string {
  const localPart = email.split('@')[0];
  
  // Convert common patterns to names
  if (localPart.includes('.')) {
    return localPart
      .split('.')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ');
  }
  
  // Just capitalize the first letter
  return localPart.charAt(0).toUpperCase() + localPart.slice(1);
}

/**
 * Get actions for a specific user
 */
export const getUserActions = async (userId: string, limit: number = 20, offset: number = 0) => {
  return await actionApi.getUserActions(userId, { limit, offset });
};

/**
 * Get actions for a specific campaign
 */
export const getCampaignActions = async (campaignId: string, limit: number = 20, offset: number = 0) => {
  return await actionApi.getCampaignActions(campaignId, { limit, offset });
};

/**
 * Get action statistics
 */
export const getActionStats = async (campaignId?: string) => {
  return await actionApi.getActionStats(campaignId);
};

/**
 * Retry a failed action
 */
export const retryAction = async (actionId: string) => {
  return await actionApi.retryAction(actionId);
};

export default {
  createAction,
  getUserActions,
  getCampaignActions,
  getActionStats,
  retryAction
};