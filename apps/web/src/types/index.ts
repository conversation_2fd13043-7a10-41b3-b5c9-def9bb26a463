// TypeScript interfaces for ModernAction.io API
// These interfaces match the Pydantic schemas from the backend API

// Enums matching backend models
export enum BillStatus {
  INTRODUCED = 'introduced',
  COMMITTEE = 'committee',
  FLOOR = 'floor',
  PASSED = 'passed',
  SIGNED = 'signed',
  VETOED = 'vetoed',
  FAILED = 'failed'
}

export enum BillType {
  HOUSE_BILL = 'house_bill',
  SENATE_BILL = 'senate_bill',
  HOUSE_RESOLUTION = 'house_resolution',
  SENATE_RESOLUTION = 'senate_resolution',
  HOUSE_JOINT_RESOLUTION = 'house_joint_resolution',
  SENATE_JOINT_RESOLUTION = 'senate_joint_resolution',
  HOUSE_CONCURRENT_RESOLUTION = 'house_concurrent_resolution',
  SENATE_CONCURRENT_RESOLUTION = 'senate_concurrent_resolution'
}

export enum CampaignStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum CampaignType {
  SUPPORT = 'support',
  OPPOSE = 'oppose',
  NEUTRAL = 'neutral'
}

export enum OfficialLevel {
  FEDERAL = 'federal',
  STATE = 'state',
  LOCAL = 'local'
}

export enum Chamber {
  HOUSE = 'house',
  SENATE = 'senate',
  UNICAMERAL = 'unicameral'
}

// Base interfaces
export interface BaseModel {
  id: string;
  created_at: string;
  updated_at: string;
}

// Bill interfaces
export interface Bill extends BaseModel {
  title: string;
  description?: string;
  bill_number: string;
  bill_type: BillType;
  status: BillStatus;
  session_year: number;
  chamber: Chamber;
  state: string;
  summary?: string;
  full_text_url?: string;
  sponsor_name?: string;
  sponsor_party?: string;
  sponsor_state?: string;
  committee?: string;
  tags?: string[];
  categories?: string[];
  is_featured: boolean;
  priority_score: number;
  external_openstates_id?: string;
  external_congress_id?: string;
}

export interface BillCreate {
  title: string;
  description?: string;
  bill_number: string;
  bill_type: BillType;
  status: BillStatus;
  session_year: number;
  chamber: Chamber;
  state: string;
  summary?: string;
  full_text_url?: string;
  sponsor_name?: string;
  sponsor_party?: string;
  sponsor_state?: string;
  committee?: string;
  tags?: string[];
  categories?: string[];
  is_featured?: boolean;
  priority_score?: number;
  external_openstates_id?: string;
  external_congress_id?: string;
}

export interface BillUpdate {
  title?: string;
  description?: string;
  bill_number?: string;
  bill_type?: BillType;
  status?: BillStatus;
  session_year?: number;
  chamber?: Chamber;
  state?: string;
  summary?: string;
  full_text_url?: string;
  sponsor_name?: string;
  sponsor_party?: string;
  sponsor_state?: string;
  committee?: string;
  tags?: string[];
  categories?: string[];
  is_featured?: boolean;
  priority_score?: number;
  external_openstates_id?: string;
  external_congress_id?: string;
}

// Official interfaces
export interface Official extends BaseModel {
  name: string;
  level: OfficialLevel;
  chamber?: Chamber;
  state?: string;
  district?: string;
  party?: string;
  email?: string;
  phone?: string;
  website?: string;
  photo_url?: string;
  office_address?: string;
  twitter_handle?: string;
  social_media?: Record<string, string>;
  external_bioguide_id?: string;
  external_openstates_id?: string;
  external_google_civic_id?: string;
}

export interface OfficialCreate {
  name: string;
  level: OfficialLevel;
  chamber?: Chamber;
  state?: string;
  district?: string;
  party?: string;
  email?: string;
  phone?: string;
  website?: string;
  photo_url?: string;
  office_address?: string;
  social_media?: Record<string, string>;
  external_bioguide_id?: string;
  external_openstates_id?: string;
  external_google_civic_id?: string;
}

export interface OfficialUpdate {
  name?: string;
  level?: OfficialLevel;
  chamber?: Chamber;
  state?: string;
  district?: string;
  party?: string;
  email?: string;
  phone?: string;
  website?: string;
  photo_url?: string;
  office_address?: string;
  social_media?: Record<string, string>;
  external_bioguide_id?: string;
  external_openstates_id?: string;
  external_google_civic_id?: string;
}

// Campaign interfaces
export interface Campaign extends BaseModel {
  title: string;
  description?: string;
  campaign_type: CampaignType;
  status: CampaignStatus;
  call_to_action: string;
  bill_id: string;
  bill: Bill; // Nested bill object
  target_actions: number;
  actual_actions: number;
  start_date?: string;
  end_date?: string;
  is_featured: boolean;
  is_public: boolean;
  talking_points?: string[];
  geographic_scope?: string[];
  hashtags?: string[];
  completion_percentage: number;
}

export interface CampaignCreate {
  title: string;
  description?: string;
  campaign_type: CampaignType;
  status: CampaignStatus;
  call_to_action: string;
  bill_id: string;
  target_actions?: number;
  start_date?: string;
  end_date?: string;
  is_featured?: boolean;
  is_public?: boolean;
  talking_points?: string[];
  geographic_scope?: string[];
  hashtags?: string[];
}

export interface CampaignUpdate {
  title?: string;
  description?: string;
  campaign_type?: CampaignType;
  status?: CampaignStatus;
  call_to_action?: string;
  target_actions?: number;
  start_date?: string;
  end_date?: string;
  is_featured?: boolean;
  is_public?: boolean;
  talking_points?: string[];
  geographic_scope?: string[];
  hashtags?: string[];
}

// API Response types
export interface ApiError {
  detail: string;
}

export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface SearchParams extends PaginationParams {
  query?: string;
}

// Campaign search parameters
export interface CampaignSearchParams extends SearchParams {
  campaign_type?: CampaignType;
  status?: CampaignStatus;
  bill_id?: string;
  is_featured?: boolean;
  is_public?: boolean;
}

// Bill search parameters
export interface BillSearchParams extends SearchParams {
  bill_type?: BillType;
  status?: BillStatus;
  session_year?: number;
  chamber?: Chamber;
  state?: string;
  sponsor?: string;
  is_featured?: boolean;
}

// Official search parameters
export interface OfficialSearchParams extends SearchParams {
  level?: OfficialLevel;
  chamber?: Chamber;
  state?: string;
  district?: string;
  party?: string;
  zip_code?: string;
}

// Action interfaces (for future use)
export interface Action extends BaseModel {
  user_id: string;
  campaign_id: string;
  official_id: string;
  action_type: string;
  message?: string;
  status: string;
}
