import React, { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useForm } from 'react-hook-form';
import { XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Campaign, Official } from '../../types';
import axios from 'axios';

interface ActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  campaign: Campaign;
  officials: Official[];
  onSubmit: (data: ActionFormData) => Promise<void>;
  isLoading?: boolean;
}

export interface ActionFormData {
  email: string;
  message: string;
  action_types?: string[];
}

const ActionModal: React.FC<ActionModalProps> = ({
  isOpen,
  onClose,
  campaign,
  officials,
  onSubmit,
  isLoading = false
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
    setValue
  } = useForm<ActionFormData>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      message: campaign.call_to_action || ''
    }
  });

  const watchedEmail = watch('email');

  // AI assistance state
  const [isLoadingAI, setIsLoadingAI] = useState(false);
  const [aiInput, setAiInput] = useState('');

  // Twitter toggle state
  const [includeTwitter, setIncludeTwitter] = useState(false);

  const handleClose = () => {
    reset();
    onClose();
  };

  const onFormSubmit = async (data: ActionFormData) => {
    try {
      // Build action_types array based on user selections
      const actionTypes = ['EMAIL']; // Always include email
      if (includeTwitter) {
        actionTypes.push('TWITTER');
      }

      // Add action_types to the form data
      const submissionData = {
        ...data,
        action_types: actionTypes
      };

      await onSubmit(submissionData);
      handleClose();
    } catch (error) {
      console.error('Failed to submit action:', error);
      // Error handling is managed by the parent component
    }
  };

  const handleAIGenerate = async () => {
    if (!aiInput.trim()) {
      return;
    }

    setIsLoadingAI(true);
    try {
      const response = await axios.post('/api/v1/ai/personalize-message', {
        raw_text: aiInput,
        context: `${campaign.title} - ${campaign.description}`,
        tone: 'professional'
      });

      const personalizedMessage = response.data.personalized_message;
      setValue('message', personalizedMessage, { shouldValidate: true });
      setAiInput(''); // Clear the AI input after successful generation
    } catch (error) {
      console.error('Failed to generate AI message:', error);
      // Could add user-facing error handling here
    } finally {
      setIsLoadingAI(false);
    }
  };

  // Default message combining campaign info and talking points
  const defaultMessage = React.useMemo(() => {
    let message = campaign.call_to_action || 'I am writing to express my position on this important issue.';
    
    if (campaign.talking_points && campaign.talking_points.length > 0) {
      message += '\n\nKey points:\n';
      campaign.talking_points.forEach((point, index) => {
        message += `${index + 1}. ${point}\n`;
      });
    }
    
    message += '\n\nI urge you to take action on this matter. Thank you for your time and consideration.';
    
    return message;
  }, [campaign]);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-semibold leading-6 text-gray-900"
                  >
                    Contact Your Representatives
                  </Dialog.Title>
                  <button
                    type="button"
                    data-testid="action-modal-close-button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={handleClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {/* Campaign Context */}
                <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Campaign: {campaign.title}</h4>
                  <p className="text-sm text-blue-800">{campaign.description}</p>
                </div>

                {/* Officials Preview */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Your message will be sent to:</h4>
                  <div className="space-y-2">
                    {officials.map((official) => (
                      <div key={official.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div className="flex-shrink-0">
                          {official.photo_url ? (
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={official.photo_url}
                              alt={official.name}
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {official.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{official.name}</p>
                          <p className="text-sm text-gray-500">
                            {official.level === 'federal' ? 'U.S. ' : ''}
                            {official.chamber === 'house' ? 'Representative' : 'Senator'}
                            {official.party && ` (${official.party})`}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
                  {/* Email Field */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Your Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      data-testid="action-modal-email-input"
                      {...register('email', {
                        required: 'Email address is required',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Please enter a valid email address'
                        }
                      })}
                      className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.email ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="<EMAIL>"
                      disabled={isLoading}
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                    )}
                  </div>

                  {/* AI Message Assistant */}
                  <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg border border-purple-200">
                    <div className="flex items-center mb-3">
                      <SparklesIcon className="h-5 w-5 text-purple-600 mr-2" />
                      <h4 className="font-medium text-purple-900">AI Message Assistant</h4>
                    </div>
                    <p className="text-sm text-purple-700 mb-3">
                      Share your personal story or key concerns, and our AI will help craft a more persuasive message.
                    </p>
                    <div className="space-y-3">
                      <textarea
                        data-testid="ai-assist-input"
                        value={aiInput}
                        onChange={(e) => setAiInput(e.target.value)}
                        placeholder="e.g., I'm a parent concerned about climate change affecting my children's future..."
                        className="block w-full px-3 py-2 border border-purple-300 rounded-md shadow-sm placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                        rows={3}
                        disabled={isLoading || isLoadingAI}
                      />
                      <button
                        type="button"
                        data-testid="ai-assist-generate-button"
                        onClick={handleAIGenerate}
                        disabled={!aiInput.trim() || isLoading || isLoadingAI}
                        className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 ${
                          !aiInput.trim() || isLoading || isLoadingAI
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-purple-600 text-white hover:bg-purple-700'
                        }`}
                      >
                        {isLoadingAI ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating...
                          </>
                        ) : (
                          <>
                            <SparklesIcon className="h-4 w-4 mr-2" />
                            Generate My Message
                          </>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Message Field */}
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      Your Message
                    </label>
                    <textarea
                      id="message"
                      rows={8}
                      data-testid="action-modal-message-textarea"
                      {...register('message', {
                        required: 'Message is required',
                        minLength: {
                          value: 10,
                          message: 'Message must be at least 10 characters long'
                        }
                      })}
                      className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.message ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Your message to your representatives..."
                      disabled={isLoading}
                      defaultValue={defaultMessage}
                    />
                    {errors.message && (
                      <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Personal messages are more effective than form letters. Feel free to edit this message.
                    </p>
                  </div>

                  {/* Twitter Toggle */}
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <input
                      type="checkbox"
                      id="twitter-toggle"
                      data-testid="action-modal-tweet-toggle"
                      checked={includeTwitter}
                      onChange={(e) => setIncludeTwitter(e.target.checked)}
                      disabled={isLoading}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="twitter-toggle" className="flex-1">
                      <div className="text-sm font-medium text-blue-900">
                        Also post a public Tweet to this official
                      </div>
                      <div className="text-xs text-blue-700">
                        Your message will be shared publicly on Twitter to increase visibility and pressure
                      </div>
                    </label>
                    <div className="text-blue-600">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      data-testid="action-modal-cancel-button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      onClick={handleClose}
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      data-testid="action-modal-send-button"
                      disabled={!isValid || !watchedEmail || isLoading}
                      className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                        !isValid || !watchedEmail || isLoading
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {isLoading ? (
                        <div className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Sending...
                        </div>
                      ) : (
                        'Send Message'
                      )}
                    </button>
                  </div>
                </form>

                {/* Privacy Notice */}
                <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                  <p className="text-xs text-gray-600">
                    <strong>Privacy Notice:</strong> Your email and message will only be used to contact your representatives about this campaign. 
                    We respect your privacy and will never share your personal information with third parties.
                  </p>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default ActionModal;