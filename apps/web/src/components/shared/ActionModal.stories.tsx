import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import ActionModal from './ActionModal';
import { Campaign, Official, CampaignType, CampaignStatus, BillType, BillStatus, OfficialLevel, Chamber } from '../../types';

const meta: Meta<typeof ActionModal> = {
  title: 'Components/ActionModal',
  component: ActionModal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ActionModal>;

// Mock data
const mockBill = {
  id: '1',
  title: 'Climate Action Now Act',
  description: 'A comprehensive bill to address climate change through clean energy investments',
  bill_number: 'HR-1234',
  bill_type: BillType.HOUSE_BILL,
  status: BillStatus.COMMITTEE,
  session_year: 2024,
  chamber: Chamber.HOUSE,
  state: 'federal',
  is_featured: true,
  priority_score: 85,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockCampaign: Campaign = {
  id: '1',
  title: 'Support Climate Action Now',
  description: 'Urge your representatives to pass the Climate Action Now Act and invest in clean energy for our future.',
  campaign_type: CampaignType.SUPPORT,
  status: CampaignStatus.ACTIVE,
  call_to_action: 'I urge you to support the Climate Action Now Act (HR-1234) and vote YES when it comes to the floor.',
  bill_id: '1',
  bill: mockBill,
  target_actions: 1000,
  actual_actions: 342,
  is_featured: true,
  is_public: true,
  talking_points: [
    'Climate change is an urgent threat that requires immediate action',
    'Clean energy investments will create jobs and boost the economy',
    'We must reduce carbon emissions to protect future generations',
    'The bill includes funding for renewable energy infrastructure'
  ],
  geographic_scope: ['Federal', 'All States'],
  hashtags: ['#ClimateAction', '#CleanEnergy', '#GreenJobs'],
  completion_percentage: 34.2,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockOfficials: Official[] = [
  {
    id: '1',
    name: 'Nancy Pelosi',
    level: OfficialLevel.FEDERAL,
    chamber: Chamber.HOUSE,
    state: 'CA',
    district: '11',
    party: 'D',
    email: '<EMAIL>',
    phone: '(*************',
    website: 'https://pelosi.house.gov',
    photo_url: 'https://www.congress.gov/img/member/p000197_200.jpg',
    office_address: '1236 Longworth House Office Building',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Dianne Feinstein',
    level: OfficialLevel.FEDERAL,
    chamber: Chamber.SENATE,
    state: 'CA',
    party: 'D',
    email: '<EMAIL>',
    phone: '(*************',
    website: 'https://feinstein.senate.gov',
    photo_url: 'https://www.congress.gov/img/member/f000062_200.jpg',
    office_address: '331 Hart Senate Office Building',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

const mockOfficialsWithoutPhotos: Official[] = [
  {
    id: '3',
    name: 'John Smith',
    level: OfficialLevel.FEDERAL,
    chamber: Chamber.HOUSE,
    state: 'TX',
    district: '7',
    party: 'R',
    email: '<EMAIL>',
    phone: '(*************',
    website: 'https://johnsmith.house.gov',
    office_address: '1234 Longworth House Office Building',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '4',
    name: 'Mary Johnson',
    level: OfficialLevel.FEDERAL,
    chamber: Chamber.SENATE,
    state: 'TX',
    party: 'R',
    email: '<EMAIL>',
    phone: '(*************',
    website: 'https://maryjohnson.senate.gov',
    office_address: '567 Hart Senate Office Building',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

// Stories
export const Default: Story = {
  args: {
    isOpen: true,
    onClose: action('onClose'),
    campaign: mockCampaign,
    officials: mockOfficials,
    onSubmit: action('onSubmit'),
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    isOpen: true,
    onClose: action('onClose'),
    campaign: mockCampaign,
    officials: mockOfficials,
    onSubmit: action('onSubmit'),
    isLoading: true,
  },
};

export const WithoutPhotos: Story = {
  args: {
    isOpen: true,
    onClose: action('onClose'),
    campaign: mockCampaign,
    officials: mockOfficialsWithoutPhotos,
    onSubmit: action('onSubmit'),
    isLoading: false,
  },
};

export const SingleOfficial: Story = {
  args: {
    isOpen: true,
    onClose: action('onClose'),
    campaign: mockCampaign,
    officials: [mockOfficials[0]],
    onSubmit: action('onSubmit'),
    isLoading: false,
  },
};

export const MinimalCampaign: Story = {
  args: {
    isOpen: true,
    onClose: action('onClose'),
    campaign: {
      ...mockCampaign,
      description: undefined,
      talking_points: undefined,
      call_to_action: 'Please take action on this important issue.',
    },
    officials: mockOfficials,
    onSubmit: action('onSubmit'),
    isLoading: false,
  },
};

export const OpposeCampaign: Story = {
  args: {
    isOpen: true,
    onClose: action('onClose'),
    campaign: {
      ...mockCampaign,
      title: 'Oppose Harmful Legislation',
      campaign_type: CampaignType.OPPOSE,
      call_to_action: 'I urge you to vote NO on HR-1234 as it would harm our community.',
      talking_points: [
        'This bill would negatively impact local businesses',
        'The proposed regulations are too restrictive',
        'Alternative solutions would be more effective',
      ],
    },
    officials: mockOfficials,
    onSubmit: action('onSubmit'),
    isLoading: false,
  },
};

export const Closed: Story = {
  args: {
    isOpen: false,
    onClose: action('onClose'),
    campaign: mockCampaign,
    officials: mockOfficials,
    onSubmit: action('onSubmit'),
    isLoading: false,
  },
};