// CampaignHeader component - displays campaign title, description, and key metadata
import React from 'react';
import { Campaign, CampaignStatus, CampaignType } from '../../types';

interface CampaignHeaderProps {
  campaign: Campaign;
}

const CampaignHeader: React.FC<CampaignHeaderProps> = ({ campaign }) => {
  const getStatusColor = (status: CampaignStatus): string => {
    switch (status) {
      case CampaignStatus.ACTIVE:
        return 'bg-green-100 text-green-800';
      case CampaignStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case CampaignStatus.PAUSED:
        return 'bg-yellow-100 text-yellow-800';
      case CampaignStatus.COMPLETED:
        return 'bg-blue-100 text-blue-800';
      case CampaignStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: CampaignType): string => {
    switch (type) {
      case CampaignType.SUPPORT:
        return 'bg-green-100 text-green-800';
      case CampaignType.OPPOSE:
        return 'bg-red-100 text-red-800';
      case CampaignType.NEUTRAL:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Status and Type Badges */}
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
            {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
          </span>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(campaign.campaign_type)}`}>
            {campaign.campaign_type.charAt(0).toUpperCase() + campaign.campaign_type.slice(1)}
          </span>
          {campaign.is_featured && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Featured
            </span>
          )}
        </div>

        {/* Campaign Title */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {campaign.title}
        </h1>

        {/* Campaign Description */}
        {campaign.description && (
          <p className="text-lg text-gray-600 mb-6 max-w-4xl">
            {campaign.description}
          </p>
        )}

        {/* Campaign Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-500">
          <div>
            <span className="font-medium text-gray-900">Progress:</span>
            <div className="mt-1">
              <div className="flex items-center">
                <div className="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(campaign.completion_percentage, 100)}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {Math.round(campaign.completion_percentage)}%
                </span>
              </div>
              <p className="mt-1 text-xs">
                {campaign.actual_actions.toLocaleString()} of {campaign.target_actions.toLocaleString()} actions
              </p>
            </div>
          </div>

          {campaign.start_date && (
            <div>
              <span className="font-medium text-gray-900">Started:</span>
              <p className="mt-1">{formatDate(campaign.start_date)}</p>
            </div>
          )}

          {campaign.end_date && (
            <div>
              <span className="font-medium text-gray-900">Ends:</span>
              <p className="mt-1">{formatDate(campaign.end_date)}</p>
            </div>
          )}
        </div>

        {/* Hashtags */}
        {campaign.hashtags && campaign.hashtags.length > 0 && (
          <div className="mt-6">
            <div className="flex flex-wrap gap-2">
              {campaign.hashtags.map((hashtag, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-50 text-blue-700"
                >
                  {hashtag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            Take Action Now
          </h2>
          <p className="text-blue-800">
            {campaign.call_to_action}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CampaignHeader;
