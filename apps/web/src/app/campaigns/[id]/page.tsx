// Campaign detail page - displays full campaign information with modular components
import { notFound } from 'next/navigation';
import { campaignApi } from '../../../services/apiClient';
import { Campaign } from '../../../types';
import {
  <PERSON><PERSON><PERSON>er,
  BillSummaryCard,
  ActionForm,
  LiveStats
} from '../../../components/campaign';

interface CampaignPageProps {
  params: {
    id: string;
  };
}

// Server-side data fetching for campaign details
async function getCampaign(id: string): Promise<Campaign | null> {
  try {
    const campaign = await campaignApi.getCampaignById(id);
    return campaign;
  } catch (error: any) {
    // Handle different error types
    if (error.response?.status === 404) {
      return null; // Campaign not found
    }
    
    // Log other errors but don't crash the page
    console.error('Error fetching campaign:', error);
    throw error; // Re-throw for other error types
  }
}

// Handle action form submission
async function handleActionSubmission(campaignId: string, zipCode: string, message?: string) {
  'use server';
  
  try {
    // TODO: Implement action submission logic
    // This would typically:
    // 1. Find representatives for the zip code
    // 2. Create action records
    // 3. Send emails to representatives
    // 4. Update campaign statistics
    
    console.log('Action submitted:', { campaignId, zipCode, message });
    
    // For now, just log the submission
    // In a real implementation, this would call the actions API
    
  } catch (error) {
    console.error('Error submitting action:', error);
    throw error;
  }
}

export default async function CampaignPage({ params }: CampaignPageProps) {
  const { id } = params;
  
  // Fetch campaign data on the server
  const campaign = await getCampaign(id);
  
  // Return 404 if campaign not found
  if (!campaign) {
    notFound();
  }

  // Handle action form submission
  const onActionSubmit = async (zipCode: string, message?: string) => {
    'use server';
    await handleActionSubmission(campaign.id, zipCode, message);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Campaign Header */}
      <CampaignHeader campaign={campaign} />
      
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Bill Summary */}
            <BillSummaryCard bill={campaign.bill} />
            
            {/* Campaign Details */}
            <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                About This Campaign
              </h3>
              
              {campaign.description && (
                <div className="mb-6">
                  <p className="text-gray-600 leading-relaxed">
                    {campaign.description}
                  </p>
                </div>
              )}
              
              {/* Campaign Metadata */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div>
                  <span className="font-medium text-gray-900">Campaign Type:</span>
                  <p className="text-gray-600 capitalize">{campaign.campaign_type}</p>
                </div>
                
                <div>
                  <span className="font-medium text-gray-900">Status:</span>
                  <p className="text-gray-600 capitalize">{campaign.status}</p>
                </div>
                
                <div>
                  <span className="font-medium text-gray-900">Target Actions:</span>
                  <p className="text-gray-600">{campaign.target_actions.toLocaleString()}</p>
                </div>
                
                <div>
                  <span className="font-medium text-gray-900">Actions Taken:</span>
                  <p className="text-gray-600">{campaign.actual_actions.toLocaleString()}</p>
                </div>
                
                {campaign.start_date && (
                  <div>
                    <span className="font-medium text-gray-900">Start Date:</span>
                    <p className="text-gray-600">
                      {new Date(campaign.start_date).toLocaleDateString()}
                    </p>
                  </div>
                )}
                
                {campaign.end_date && (
                  <div>
                    <span className="font-medium text-gray-900">End Date:</span>
                    <p className="text-gray-600">
                      {new Date(campaign.end_date).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
              
              {/* Geographic Scope */}
              {campaign.geographic_scope && campaign.geographic_scope.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    Geographic Focus
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {campaign.geographic_scope.map((area, index) => (
                      <span 
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {area}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Right Column - Action Form and Stats */}
          <div className="space-y-8">
            {/* Action Form */}
            <ActionForm 
              campaign={campaign} 
              onSubmit={onActionSubmit}
            />
            
            {/* Live Stats */}
            <LiveStats campaign={campaign} />
            
            {/* Share Campaign */}
            <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Share This Campaign
              </h3>
              
              <div className="space-y-3">
                <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                  Share on Twitter
                </button>
                
                <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                  Share on Facebook
                </button>
                
                <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Copy Link
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Generate metadata for the page
export async function generateMetadata({ params }: CampaignPageProps) {
  const { id } = params;
  
  try {
    const campaign = await getCampaign(id);
    
    if (!campaign) {
      return {
        title: 'Campaign Not Found',
        description: 'The requested campaign could not be found.'
      };
    }
    
    return {
      title: `${campaign.title} | ModernAction.io`,
      description: campaign.description || `Take action on ${campaign.bill.title}`,
      openGraph: {
        title: campaign.title,
        description: campaign.description || `Take action on ${campaign.bill.title}`,
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: campaign.title,
        description: campaign.description || `Take action on ${campaign.bill.title}`,
      }
    };
  } catch (error) {
    return {
      title: 'Campaign | ModernAction.io',
      description: 'Take action on important legislation'
    };
  }
}
