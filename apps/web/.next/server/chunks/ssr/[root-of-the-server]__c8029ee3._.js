module.exports = {

"[project]/.next-internal/server/app/campaigns/page/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/services/apiClient.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Centralized API client for ModernAction.io
// Uses axios for robust HTTP client features like interceptors and error handling
__turbopack_context__.s({
    "actionApi": ()=>actionApi,
    "billApi": ()=>billApi,
    "campaignApi": ()=>campaignApi,
    "default": ()=>__TURBOPACK__default__export__,
    "officialApi": ()=>officialApi
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
;
// Create axios instance with base configuration
const apiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000/api/v1") || 'http://localhost:8000/api/v1',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Request interceptor for adding auth tokens (future use)
apiClient.interceptors.request.use((config)=>{
    // Add auth token when available
    // const token = getAuthToken();
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor for error handling
apiClient.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // Handle common error scenarios
    if (error.response?.status === 401) {
        // Handle unauthorized access
        console.error('Unauthorized access - redirecting to login');
    // Redirect to login page
    } else if (error.response?.status === 404) {
        // Handle not found errors
        console.error('Resource not found:', error.response.data);
    } else if (error.response?.status >= 500) {
        // Handle server errors
        console.error('Server error:', error.response.data);
    }
    return Promise.reject(error);
});
// Helper function to build query string from parameters
const buildQueryString = (params)=>{
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== '') {
            searchParams.append(key, value.toString());
        }
    });
    return searchParams.toString();
};
const campaignApi = {
    // Get all campaigns with pagination
    getCampaigns: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/campaigns/?${queryString}`);
        return response.data;
    },
    // Get single campaign by ID
    getCampaignById: async (id)=>{
        const response = await apiClient.get(`/campaigns/${id}`);
        return response.data;
    },
    // Create new campaign
    createCampaign: async (campaignData)=>{
        const response = await apiClient.post('/campaigns/', campaignData);
        return response.data;
    },
    // Update existing campaign
    updateCampaign: async (id, campaignData)=>{
        const response = await apiClient.put(`/campaigns/${id}`, campaignData);
        return response.data;
    },
    // Delete campaign
    deleteCampaign: async (id)=>{
        await apiClient.delete(`/campaigns/${id}`);
    },
    // Search campaigns with filters
    searchCampaigns: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/campaigns/search?${queryString}`);
        return response.data;
    },
    // Get featured campaigns
    getFeaturedCampaigns: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/campaigns/featured?${queryString}`);
        return response.data;
    },
    // Get active campaigns
    getActiveCampaigns: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/campaigns/active?${queryString}`);
        return response.data;
    },
    // Get campaigns by status
    getCampaignsByStatus: async (status, params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/campaigns/status/${status}?${queryString}`);
        return response.data;
    },
    // Get campaigns by bill ID
    getCampaignsByBill: async (billId, params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/campaigns/bill/${billId}?${queryString}`);
        return response.data;
    }
};
const billApi = {
    // Get all bills with pagination
    getBills: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/bills/?${queryString}`);
        return response.data;
    },
    // Get single bill by ID
    getBillById: async (id)=>{
        const response = await apiClient.get(`/bills/${id}`);
        return response.data;
    },
    // Create new bill
    createBill: async (billData)=>{
        const response = await apiClient.post('/bills/', billData);
        return response.data;
    },
    // Update existing bill
    updateBill: async (id, billData)=>{
        const response = await apiClient.put(`/bills/${id}`, billData);
        return response.data;
    },
    // Delete bill
    deleteBill: async (id)=>{
        await apiClient.delete(`/bills/${id}`);
    },
    // Search bills with filters
    searchBills: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/bills/search?${queryString}`);
        return response.data;
    },
    // Get featured bills
    getFeaturedBills: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/bills/featured?${queryString}`);
        return response.data;
    },
    // Get bills by status
    getBillsByStatus: async (status, params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/bills/status/${status}?${queryString}`);
        return response.data;
    }
};
const officialApi = {
    // Get all officials with pagination
    getOfficials: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/officials/?${queryString}`);
        return response.data;
    },
    // Get single official by ID
    getOfficialById: async (id)=>{
        const response = await apiClient.get(`/officials/${id}`);
        return response.data;
    },
    // Create new official
    createOfficial: async (officialData)=>{
        const response = await apiClient.post('/officials/', officialData);
        return response.data;
    },
    // Update existing official
    updateOfficial: async (id, officialData)=>{
        const response = await apiClient.put(`/officials/${id}`, officialData);
        return response.data;
    },
    // Delete official
    deleteOfficial: async (id)=>{
        await apiClient.delete(`/officials/${id}`);
    },
    // Search officials with filters
    searchOfficials: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/officials/search?${queryString}`);
        return response.data;
    },
    // Get officials by zip code
    getOfficialsByZip: async (zipCode, params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/officials/zip/${zipCode}?${queryString}`);
        return response.data;
    }
};
const actionApi = {
    // Create new action
    createAction: async (actionData)=>{
        const response = await apiClient.post('/actions/', actionData);
        return response.data;
    },
    // Get single action by ID
    getActionById: async (id)=>{
        const response = await apiClient.get(`/actions/${id}`);
        return response.data;
    },
    // Get actions with filters
    getActions: async (params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/actions/?${queryString}`);
        return response.data;
    },
    // Retry failed action
    retryAction: async (id)=>{
        const response = await apiClient.post(`/actions/${id}/retry`);
        return response.data;
    },
    // Delete action
    deleteAction: async (id)=>{
        await apiClient.delete(`/actions/${id}`);
    },
    // Get action statistics
    getActionStats: async (campaignId)=>{
        const url = campaignId ? `/actions/stats?campaign_id=${campaignId}` : '/actions/stats';
        const response = await apiClient.get(url);
        return response.data;
    },
    // Get user actions
    getUserActions: async (userId, params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/actions/user/${userId}?${queryString}`);
        return response.data;
    },
    // Get campaign actions
    getCampaignActions: async (campaignId, params = {})=>{
        const queryString = buildQueryString(params);
        const response = await apiClient.get(`/actions/campaign/${campaignId}/stats?${queryString}`);
        return response.data;
    }
};
const __TURBOPACK__default__export__ = apiClient;
}),
"[project]/src/types/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// TypeScript interfaces for ModernAction.io API
// These interfaces match the Pydantic schemas from the backend API
// Enums matching backend models
__turbopack_context__.s({
    "BillStatus": ()=>BillStatus,
    "BillType": ()=>BillType,
    "CampaignStatus": ()=>CampaignStatus,
    "CampaignType": ()=>CampaignType,
    "Chamber": ()=>Chamber,
    "OfficialLevel": ()=>OfficialLevel
});
var BillStatus = /*#__PURE__*/ function(BillStatus) {
    BillStatus["INTRODUCED"] = "introduced";
    BillStatus["COMMITTEE"] = "committee";
    BillStatus["FLOOR"] = "floor";
    BillStatus["PASSED"] = "passed";
    BillStatus["SIGNED"] = "signed";
    BillStatus["VETOED"] = "vetoed";
    BillStatus["FAILED"] = "failed";
    return BillStatus;
}({});
var BillType = /*#__PURE__*/ function(BillType) {
    BillType["HOUSE_BILL"] = "house_bill";
    BillType["SENATE_BILL"] = "senate_bill";
    BillType["HOUSE_RESOLUTION"] = "house_resolution";
    BillType["SENATE_RESOLUTION"] = "senate_resolution";
    BillType["HOUSE_JOINT_RESOLUTION"] = "house_joint_resolution";
    BillType["SENATE_JOINT_RESOLUTION"] = "senate_joint_resolution";
    BillType["HOUSE_CONCURRENT_RESOLUTION"] = "house_concurrent_resolution";
    BillType["SENATE_CONCURRENT_RESOLUTION"] = "senate_concurrent_resolution";
    return BillType;
}({});
var CampaignStatus = /*#__PURE__*/ function(CampaignStatus) {
    CampaignStatus["DRAFT"] = "draft";
    CampaignStatus["ACTIVE"] = "active";
    CampaignStatus["PAUSED"] = "paused";
    CampaignStatus["COMPLETED"] = "completed";
    CampaignStatus["CANCELLED"] = "cancelled";
    return CampaignStatus;
}({});
var CampaignType = /*#__PURE__*/ function(CampaignType) {
    CampaignType["SUPPORT"] = "support";
    CampaignType["OPPOSE"] = "oppose";
    CampaignType["NEUTRAL"] = "neutral";
    return CampaignType;
}({});
var OfficialLevel = /*#__PURE__*/ function(OfficialLevel) {
    OfficialLevel["FEDERAL"] = "federal";
    OfficialLevel["STATE"] = "state";
    OfficialLevel["LOCAL"] = "local";
    return OfficialLevel;
}({});
var Chamber = /*#__PURE__*/ function(Chamber) {
    Chamber["HOUSE"] = "house";
    Chamber["SENATE"] = "senate";
    Chamber["UNICAMERAL"] = "unicameral";
    return Chamber;
}({});
}),
"[project]/src/app/campaigns/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Campaigns listing page - displays all campaigns with search and filtering
__turbopack_context__.s({
    "default": ()=>CampaignsPage,
    "generateMetadata": ()=>generateMetadata
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$apiClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/apiClient.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-rsc] (ecmascript)");
;
;
;
;
// Server-side data fetching for campaigns
async function getCampaigns(searchParams) {
    try {
        const params = {
            status: searchParams.status,
            campaign_type: searchParams.type,
            is_featured: searchParams.featured === 'true' ? true : undefined,
            skip: searchParams.page ? (parseInt(searchParams.page) - 1) * 20 : 0,
            limit: 20
        };
        // Remove undefined values
        const cleanParams = Object.fromEntries(Object.entries(params).filter(([_, value])=>value !== undefined));
        if (Object.keys(cleanParams).length > 2) {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$apiClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["campaignApi"].searchCampaigns(cleanParams);
        } else {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$apiClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["campaignApi"].getCampaigns(cleanParams);
        }
    } catch (error) {
        console.error('Error fetching campaigns:', error);
        return [];
    }
}
async function CampaignsPage({ searchParams }) {
    const campaigns = await getCampaigns(searchParams);
    const getStatusColor = (status)=>{
        switch(status){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignStatus"].ACTIVE:
                return 'bg-green-100 text-green-800';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignStatus"].DRAFT:
                return 'bg-gray-100 text-gray-800';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignStatus"].PAUSED:
                return 'bg-yellow-100 text-yellow-800';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignStatus"].COMPLETED:
                return 'bg-blue-100 text-blue-800';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignStatus"].CANCELLED:
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const getTypeColor = (type)=>{
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignType"].SUPPORT:
                return 'bg-green-100 text-green-800';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignType"].OPPOSE:
                return 'bg-red-100 text-red-800';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CampaignType"].NEUTRAL:
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white shadow-sm border-b border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "md:flex md:items-center md:justify-between",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 min-w-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-3xl font-bold text-gray-900",
                                    children: "Active Campaigns"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/campaigns/page.tsx",
                                    lineNumber: 82,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mt-2 text-lg text-gray-600",
                                    children: "Take action on important legislation affecting your community"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/campaigns/page.tsx",
                                    lineNumber: 85,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/campaigns/page.tsx",
                            lineNumber: 81,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/campaigns/page.tsx",
                        lineNumber: 80,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/campaigns/page.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/campaigns/page.tsx",
                lineNumber: 78,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-sm border border-gray-200 p-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/campaigns",
                                className: `px-3 py-1 rounded-full text-sm font-medium transition-colors ${!searchParams.status && !searchParams.type && !searchParams.featured ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,
                                children: "All Campaigns"
                            }, void 0, false, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 97,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/campaigns?featured=true",
                                className: `px-3 py-1 rounded-full text-sm font-medium transition-colors ${searchParams.featured === 'true' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,
                                children: "Featured"
                            }, void 0, false, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 108,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/campaigns?status=active",
                                className: `px-3 py-1 rounded-full text-sm font-medium transition-colors ${searchParams.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,
                                children: "Active"
                            }, void 0, false, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 119,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/campaigns?type=support",
                                className: `px-3 py-1 rounded-full text-sm font-medium transition-colors ${searchParams.type === 'support' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,
                                children: "Support"
                            }, void 0, false, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 130,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/campaigns?type=oppose",
                                className: `px-3 py-1 rounded-full text-sm font-medium transition-colors ${searchParams.type === 'oppose' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,
                                children: "Oppose"
                            }, void 0, false, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 141,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/campaigns/page.tsx",
                        lineNumber: 96,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/campaigns/page.tsx",
                    lineNumber: 95,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/campaigns/page.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12",
                children: campaigns.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center py-12",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "mx-auto h-12 w-12 text-gray-400",
                            fill: "none",
                            viewBox: "0 0 24 24",
                            stroke: "currentColor",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 160,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/campaigns/page.tsx",
                            lineNumber: 159,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "mt-2 text-sm font-medium text-gray-900",
                            children: "No campaigns found"
                        }, void 0, false, {
                            fileName: "[project]/src/app/campaigns/page.tsx",
                            lineNumber: 162,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mt-1 text-sm text-gray-500",
                            children: "Try adjusting your filters or check back later for new campaigns."
                        }, void 0, false, {
                            fileName: "[project]/src/app/campaigns/page.tsx",
                            lineNumber: 163,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/campaigns/page.tsx",
                    lineNumber: 158,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
                    children: campaigns.map((campaign)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                            href: `/campaigns/${campaign.id}`,
                            className: "bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow overflow-hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-wrap gap-2 mb-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`,
                                                children: campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 178,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(campaign.campaign_type)}`,
                                                children: campaign.campaign_type.charAt(0).toUpperCase() + campaign.campaign_type.slice(1)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 181,
                                                columnNumber: 21
                                            }, this),
                                            campaign.is_featured && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",
                                                children: "Featured"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 185,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                        lineNumber: 177,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-semibold text-gray-900 mb-2 line-clamp-2",
                                        children: campaign.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                        lineNumber: 192,
                                        columnNumber: 19
                                    }, this),
                                    campaign.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-600 text-sm mb-4 line-clamp-3",
                                        children: campaign.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                        lineNumber: 198,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-4 p-3 bg-gray-50 rounded-md",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-900 mb-1",
                                                children: [
                                                    "Related Bill: ",
                                                    campaign.bill.bill_number
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 205,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600 line-clamp-2",
                                                children: campaign.bill.title
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 208,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                        lineNumber: 204,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: "Progress"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                                        lineNumber: 216,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium text-gray-900",
                                                        children: [
                                                            Math.round(campaign.completion_percentage),
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                                        lineNumber: 217,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 215,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-full bg-gray-200 rounded-full h-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-blue-600 h-2 rounded-full transition-all duration-300",
                                                    style: {
                                                        width: `${Math.min(campaign.completion_percentage, 100)}%`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/campaigns/page.tsx",
                                                    lineNumber: 222,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 221,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500 mt-1",
                                                children: [
                                                    campaign.actual_actions.toLocaleString(),
                                                    " of ",
                                                    campaign.target_actions.toLocaleString(),
                                                    " actions"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/campaigns/page.tsx",
                                                lineNumber: 227,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                        lineNumber: 214,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors",
                                            children: [
                                                "Take Action",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                    className: "ml-2 -mr-1 w-4 h-4",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M9 5l7 7-7 7"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                                        lineNumber: 237,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/campaigns/page.tsx",
                                                    lineNumber: 236,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/campaigns/page.tsx",
                                            lineNumber: 234,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/campaigns/page.tsx",
                                        lineNumber: 233,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/campaigns/page.tsx",
                                lineNumber: 175,
                                columnNumber: 17
                            }, this)
                        }, campaign.id, false, {
                            fileName: "[project]/src/app/campaigns/page.tsx",
                            lineNumber: 170,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/app/campaigns/page.tsx",
                    lineNumber: 168,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/campaigns/page.tsx",
                lineNumber: 156,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/campaigns/page.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
function generateMetadata({ searchParams }) {
    let title = 'Campaigns | ModernAction.io';
    let description = 'Take action on important legislation affecting your community';
    if (searchParams.featured === 'true') {
        title = 'Featured Campaigns | ModernAction.io';
        description = 'Featured campaigns for important legislative action';
    } else if (searchParams.status) {
        title = `${searchParams.status.charAt(0).toUpperCase() + searchParams.status.slice(1)} Campaigns | ModernAction.io`;
    } else if (searchParams.type) {
        title = `${searchParams.type.charAt(0).toUpperCase() + searchParams.type.slice(1)} Campaigns | ModernAction.io`;
    }
    return {
        title,
        description,
        openGraph: {
            title,
            description,
            type: 'website'
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description
        }
    };
}
}),
"[project]/src/app/campaigns/page.tsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/campaigns/page.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c8029ee3._.js.map