import pytest
from fastapi.testclient import TestClient
from app.models.official import Official, OfficialLevel, OfficialChamber

class TestOfficialsAPI:
    """Test suite for officials API endpoints"""

    def test_get_officials_empty(self, test_client):
        """Test getting officials when database is empty"""
        response = test_client.get("/api/v1/officials/")
        assert response.status_code == 200
        assert response.json() == []

    def test_create_official(self, test_client):
        """Test creating a new official"""
        official_data = {
            "name": "<PERSON>",
            "title": "Representative",
            "party": "Democrat",
            "email": "<EMAIL>",
            "level": "federal",
            "chamber": "house",
            "state": "CA",
            "district": "1"
        }
        
        response = test_client.post("/api/v1/officials/", json=official_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == official_data["name"]
        assert data["title"] == official_data["title"]
        assert data["party"] == official_data["party"]
        assert data["email"] == official_data["email"]
        assert data["level"] == official_data["level"]
        assert data["chamber"] == official_data["chamber"]
        assert data["state"] == official_data["state"]
        assert data["district"] == official_data["district"]
        assert "id" in data
        assert "created_at" in data

    def test_get_official_by_id(self, test_client, test_db_session):
        """Test getting a specific official by ID"""
        # Create an official first
        official = Official(
            name="Jane Smith",
            title="Senator",
            party="Republican", 
            email="<EMAIL>",
            level=OfficialLevel.FEDERAL,
            chamber=OfficialChamber.SENATE,
            state="TX"
        )
        test_db_session.add(official)
        test_db_session.commit()
        
        response = test_client.get(f"/api/v1/officials/{official.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == "Jane Smith"
        assert data["title"] == "Senator"
        assert data["party"] == "Republican"

    def test_get_official_not_found(self, test_client):
        """Test getting a non-existent official"""
        fake_id = "550e8400-e29b-41d4-a716-446655440000"
        response = test_client.get(f"/api/v1/officials/{fake_id}")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_search_officials_by_name(self, test_client, test_db_session):
        """Test searching officials by name"""
        # Create test officials
        officials = [
            Official(
                name="John Adams",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE
            ),
            Official(
                name="Jane Doe",
                title="Senator", 
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.SENATE
            ),
            Official(
                name="Bob Smith",
                title="Governor",
                level=OfficialLevel.STATE
            )
        ]
        
        for official in officials:
            test_db_session.add(official)
        test_db_session.commit()
        
        # Search for "John"
        response = test_client.get("/api/v1/officials/search?query=John")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "John Adams"

    def test_search_officials_by_level(self, test_client, test_db_session):
        """Test searching officials by government level"""
        # Create test officials
        officials = [
            Official(
                name="Federal Rep",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE
            ),
            Official(
                name="State Gov",
                title="Governor",
                level=OfficialLevel.STATE
            ),
            Official(
                name="Local Mayor",
                title="Mayor",
                level=OfficialLevel.LOCAL
            )
        ]
        
        for official in officials:
            test_db_session.add(official)
        test_db_session.commit()
        
        # Search for federal officials
        response = test_client.get("/api/v1/officials/search?level=federal")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "Federal Rep"

    def test_get_officials_by_zip_code(self, test_client, test_db_session):
        """Test getting officials by zip code"""
        # Create test officials
        officials = [
            Official(
                name="CA Federal Rep",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE,
                state="CA"
            ),
            Official(
                name="CA State Gov",
                title="Governor",
                level=OfficialLevel.STATE,
                state="CA"
            ),
            Official(
                name="NY Federal Rep",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE,
                state="NY"
            )
        ]
        
        for official in officials:
            test_db_session.add(official)
        test_db_session.commit()
        
        # Test California zip code (90210)
        response = test_client.get("/api/v1/officials/by-zip/90210")
        assert response.status_code == 200
        data = response.json()
        
        # Should return federal officials and CA state officials
        names = [official["name"] for official in data]
        assert "CA Federal Rep" in names
        assert "CA State Gov" in names
        assert "NY Federal Rep" not in names

    def test_get_officials_by_level_endpoint(self, test_client, test_db_session):
        """Test the get officials by level endpoint"""
        # Create test officials
        officials = [
            Official(
                name="Federal Official",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE
            ),
            Official(
                name="State Official",
                title="Governor",
                level=OfficialLevel.STATE
            )
        ]
        
        for official in officials:
            test_db_session.add(official)
        test_db_session.commit()
        
        response = test_client.get("/api/v1/officials/levels/federal")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "Federal Official"

    def test_get_officials_by_chamber_endpoint(self, test_client, test_db_session):
        """Test the get officials by chamber endpoint"""
        # Create test officials
        officials = [
            Official(
                name="House Rep",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE
            ),
            Official(
                name="Senator",
                title="Senator",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.SENATE
            )
        ]
        
        for official in officials:
            test_db_session.add(official)
        test_db_session.commit()
        
        response = test_client.get("/api/v1/officials/chambers/house")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "House Rep"

    def test_update_official(self, test_client, test_db_session):
        """Test updating an official's information"""
        # Create an official first
        official = Official(
            name="John Doe",
            title="Representative",
            party="Democrat",
            level=OfficialLevel.FEDERAL,
            chamber=OfficialChamber.HOUSE
        )
        test_db_session.add(official)
        test_db_session.commit()
        
        # Update the official
        update_data = {
            "party": "Independent",
            "phone": "555-0123"
        }
        
        response = test_client.put(f"/api/v1/officials/{official.id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["party"] == "Independent"
        assert data["phone"] == "555-0123"
        assert data["name"] == "John Doe"  # Unchanged

    def test_delete_official(self, test_client, test_db_session):
        """Test soft deleting an official"""
        # Create an official first
        official = Official(
            name="John Doe",
            title="Representative",
            level=OfficialLevel.FEDERAL,
            chamber=OfficialChamber.HOUSE
        )
        test_db_session.add(official)
        test_db_session.commit()
        
        # Delete the official
        response = test_client.delete(f"/api/v1/officials/{official.id}")
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]
        
        # Verify official is no longer in active list
        response = test_client.get("/api/v1/officials/")
        assert response.status_code == 200
        assert len(response.json()) == 0

    def test_get_official_by_external_id(self, test_client, test_db_session):
        """Test getting official by external ID"""
        # Create an official with external IDs
        official = Official(
            name="John Doe",
            title="Representative",
            level=OfficialLevel.FEDERAL,
            chamber=OfficialChamber.HOUSE,
            bioguide_id="D000001",
            openstates_id="ocd-person/12345"
        )
        test_db_session.add(official)
        test_db_session.commit()
        
        # Test bioguide ID lookup
        response = test_client.get("/api/v1/officials/external/bioguide/D000001")
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "John Doe"
        
        # Test openstates ID lookup
        response = test_client.get("/api/v1/officials/external/openstates/ocd-person/12345")
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "John Doe"

    def test_invalid_zip_code(self, test_client):
        """Test invalid zip code format"""
        response = test_client.get("/api/v1/officials/by-zip/123")
        assert response.status_code == 400
        assert "Invalid zip code" in response.json()["detail"]

    def test_create_official_duplicate_external_id(self, test_client, test_db_session):
        """Test creating official with duplicate external ID"""
        # Create first official
        official = Official(
            name="John Doe",
            title="Representative",
            level=OfficialLevel.FEDERAL,
            chamber=OfficialChamber.HOUSE,
            bioguide_id="D000001"
        )
        test_db_session.add(official)
        test_db_session.commit()
        
        # Try to create another with same bioguide_id
        official_data = {
            "name": "Jane Doe",
            "title": "Senator",
            "level": "federal",
            "chamber": "senate",
            "bioguide_id": "D000001"
        }
        
        response = test_client.post("/api/v1/officials/", json=official_data)
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_pagination(self, test_client, test_db_session):
        """Test pagination of officials list"""
        # Create multiple officials
        for i in range(25):
            official = Official(
                name=f"Official {i}",
                title="Representative",
                level=OfficialLevel.FEDERAL,
                chamber=OfficialChamber.HOUSE
            )
            test_db_session.add(official)
        test_db_session.commit()
        
        # Test first page
        response = test_client.get("/api/v1/officials/?skip=0&limit=10")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 10
        
        # Test second page
        response = test_client.get("/api/v1/officials/?skip=10&limit=10")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 10
        
        # Test third page
        response = test_client.get("/api/v1/officials/?skip=20&limit=10")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5