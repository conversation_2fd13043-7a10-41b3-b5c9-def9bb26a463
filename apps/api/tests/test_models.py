import pytest
from sqlalchemy.exc import IntegrityError
from app.models.user import User
from app.models.bill import Bill, BillStatus, BillType
from app.models.campaign import Campaign, CampaignStatus, CampaignType
from app.models.official import Official, OfficialLevel, OfficialChamber
from app.models.action import Action, ActionStatus, ActionType


class TestUserModel:
    def test_create_user(self, test_db_session, sample_user_data):
        """Test creating a user"""
        user = User(
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            first_name=sample_user_data["first_name"],
            last_name=sample_user_data["last_name"],
            zip_code=sample_user_data["zip_code"]
        )
        
        test_db_session.add(user)
        test_db_session.commit()
        
        # Verify user was created
        saved_user = test_db_session.query(User).filter_by(email=sample_user_data["email"]).first()
        assert saved_user is not None
        assert saved_user.email == sample_user_data["email"]
        assert saved_user.first_name == sample_user_data["first_name"]
        assert saved_user.last_name == sample_user_data["last_name"]
        assert saved_user.full_name == f"{sample_user_data['first_name']} {sample_user_data['last_name']}"
        assert saved_user.is_active is True
        assert saved_user.is_verified is False
        assert saved_user.is_superuser is False
        assert saved_user.id is not None
        assert saved_user.created_at is not None
        assert saved_user.updated_at is not None

    def test_user_unique_email(self, test_db_session, sample_user_data):
        """Test that email must be unique"""
        user1 = User(
            email=sample_user_data["email"],
            hashed_password="hashed_password1",
            first_name="First",
            last_name="User"
        )
        user2 = User(
            email=sample_user_data["email"],
            hashed_password="hashed_password2",
            first_name="Second",
            last_name="User"
        )
        
        test_db_session.add(user1)
        test_db_session.commit()
        
        test_db_session.add(user2)
        with pytest.raises(IntegrityError):
            test_db_session.commit()

    def test_user_full_name_property(self, test_db_session):
        """Test the full_name property"""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="John",
            last_name="Doe"
        )
        
        assert user.full_name == "John Doe"


class TestBillModel:
    def test_create_bill(self, test_db_session, sample_bill_data):
        """Test creating a bill"""
        bill = Bill(**sample_bill_data)
        
        test_db_session.add(bill)
        test_db_session.commit()
        
        # Verify bill was created
        saved_bill = test_db_session.query(Bill).filter_by(bill_number=sample_bill_data["bill_number"]).first()
        assert saved_bill is not None
        assert saved_bill.title == sample_bill_data["title"]
        assert saved_bill.bill_number == sample_bill_data["bill_number"]
        assert saved_bill.bill_type == BillType.HOUSE_BILL
        assert saved_bill.status == BillStatus.INTRODUCED
        assert saved_bill.session_year == sample_bill_data["session_year"]
        assert saved_bill.id is not None
        assert saved_bill.created_at is not None

    def test_bill_full_id_property(self, test_db_session):
        """Test the full_bill_id property"""
        bill = Bill(
            title="Test Bill",
            bill_number="HR-123",
            bill_type=BillType.HOUSE_BILL,
            status=BillStatus.INTRODUCED,
            session_year=2024,
            chamber="house",
            state="CA"
        )
        
        assert bill.full_bill_id == "CA-HR-123-2024"

    def test_bill_enum_values(self, test_db_session):
        """Test bill enum values"""
        bill = Bill(
            title="Test Bill",
            bill_number="S-456",
            bill_type=BillType.SENATE_BILL,
            status=BillStatus.PASSED,
            session_year=2024,
            chamber="senate",
            state="federal"
        )
        
        test_db_session.add(bill)
        test_db_session.commit()
        
        saved_bill = test_db_session.query(Bill).filter_by(bill_number="S-456").first()
        assert saved_bill.bill_type == BillType.SENATE_BILL
        assert saved_bill.status == BillStatus.PASSED


class TestOfficialModel:
    def test_create_official(self, test_db_session, sample_official_data):
        """Test creating an official"""
        official = Official(**sample_official_data)
        
        test_db_session.add(official)
        test_db_session.commit()
        
        # Verify official was created
        saved_official = test_db_session.query(Official).filter_by(name=sample_official_data["name"]).first()
        assert saved_official is not None
        assert saved_official.name == sample_official_data["name"]
        assert saved_official.title == sample_official_data["title"]
        assert saved_official.party == sample_official_data["party"]
        assert saved_official.level == OfficialLevel.FEDERAL
        assert saved_official.chamber == OfficialChamber.HOUSE
        assert saved_official.id is not None

    def test_official_properties(self, test_db_session):
        """Test official properties"""
        official = Official(
            name="John Smith",
            title="Representative",
            party="Democrat",
            email="<EMAIL>",
            level=OfficialLevel.FEDERAL,
            chamber=OfficialChamber.HOUSE,
            state="CA",
            is_active=True
        )
        
        assert official.full_title == "Representative from CA"
        assert official.display_name == "John Smith (Democrat)"
        assert official.contact_preference == "email"

    def test_official_contact_preference(self, test_db_session):
        """Test contact preference logic"""
        # Official with email
        official_email = Official(
            name="Test Official",
            title="Representative",
            email="<EMAIL>",
            level=OfficialLevel.FEDERAL,
            is_active=True
        )
        assert official_email.contact_preference == "email"
        
        # Official with phone only
        official_phone = Official(
            name="Test Official",
            title="Representative",
            phone="555-0123",
            level=OfficialLevel.FEDERAL,
            is_active=True
        )
        assert official_phone.contact_preference == "phone"
        
        # Official with website only
        official_website = Official(
            name="Test Official",
            title="Representative",
            website="https://example.com",
            level=OfficialLevel.FEDERAL,
            is_active=True
        )
        assert official_website.contact_preference == "website"
        
        # Official with no contact info
        official_none = Official(
            name="Test Official",
            title="Representative",
            level=OfficialLevel.FEDERAL,
            is_active=True
        )
        assert official_none.contact_preference == "none"


class TestCampaignModel:
    def test_create_campaign(self, test_db_session, sample_bill_data, sample_campaign_data):
        """Test creating a campaign"""
        # First create a bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()
        
        # Then create a campaign
        campaign_data = sample_campaign_data.copy()
        campaign_data["bill_id"] = bill.id
        campaign = Campaign(**campaign_data)
        
        test_db_session.add(campaign)
        test_db_session.commit()
        
        # Verify campaign was created
        saved_campaign = test_db_session.query(Campaign).filter_by(title=sample_campaign_data["title"]).first()
        assert saved_campaign is not None
        assert saved_campaign.title == sample_campaign_data["title"]
        assert saved_campaign.campaign_type == CampaignType.SUPPORT
        assert saved_campaign.status == CampaignStatus.ACTIVE
        assert saved_campaign.bill_id == bill.id
        assert saved_campaign.bill == bill

    def test_campaign_properties(self, test_db_session, sample_bill_data):
        """Test campaign properties"""
        # Create a bill first
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()
        
        campaign = Campaign(
            title="Test Campaign",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.ACTIVE,
            call_to_action="Support this bill!",
            goal_actions=100,
            actual_actions=25,
            bill_id=bill.id
        )
        
        assert campaign.completion_percentage == 25.0
        assert campaign.is_active is True
        assert campaign.is_expired is False

    def test_campaign_completion_percentage(self, test_db_session, sample_bill_data):
        """Test completion percentage calculation"""
        # Create a bill first
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()
        
        # Test with no goal
        campaign_no_goal = Campaign(
            title="No Goal Campaign",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.ACTIVE,
            call_to_action="Support this bill!",
            actual_actions=50,
            bill_id=bill.id
        )
        assert campaign_no_goal.completion_percentage == 0.0
        
        # Test with goal exceeded
        campaign_exceeded = Campaign(
            title="Exceeded Campaign",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.ACTIVE,
            call_to_action="Support this bill!",
            goal_actions=100,
            actual_actions=150,
            bill_id=bill.id
        )
        assert campaign_exceeded.completion_percentage == 100.0


class TestActionModel:
    def test_create_action(self, test_db_session, sample_user_data, sample_bill_data, sample_campaign_data, sample_official_data):
        """Test creating an action"""
        # Create required entities
        user = User(
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            first_name=sample_user_data["first_name"],
            last_name=sample_user_data["last_name"]
        )
        test_db_session.add(user)
        
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        
        official = Official(**sample_official_data)
        test_db_session.add(official)
        
        test_db_session.commit()
        
        campaign_data = sample_campaign_data.copy()
        campaign_data["bill_id"] = bill.id
        campaign = Campaign(**campaign_data)
        test_db_session.add(campaign)
        test_db_session.commit()
        
        # Create action
        action = Action(
            subject="Test Action",
            message="This is a test action message",
            action_type=ActionType.EMAIL,
            status=ActionStatus.PENDING,
            user_name=user.full_name,
            user_email=user.email,
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        
        test_db_session.add(action)
        test_db_session.commit()
        
        # Verify action was created
        saved_action = test_db_session.query(Action).filter_by(subject="Test Action").first()
        assert saved_action is not None
        assert saved_action.subject == "Test Action"
        assert saved_action.action_type == ActionType.EMAIL
        assert saved_action.status == ActionStatus.PENDING
        assert saved_action.user == user
        assert saved_action.campaign == campaign
        assert saved_action.official == official

    def test_action_properties(self, test_db_session, sample_user_data, sample_bill_data, sample_campaign_data, sample_official_data):
        """Test action properties"""
        # Create required entities
        user = User(email=sample_user_data["email"], hashed_password="hashed", first_name="Test", last_name="User")
        test_db_session.add(user)
        
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        
        official = Official(**sample_official_data)
        test_db_session.add(official)
        
        test_db_session.commit()
        
        campaign_data = sample_campaign_data.copy()
        campaign_data["bill_id"] = bill.id
        campaign = Campaign(**campaign_data)
        test_db_session.add(campaign)
        test_db_session.commit()
        
        # Test successful action
        action_success = Action(
            subject="Success Action",
            message="Test message",
            action_type=ActionType.EMAIL,
            status=ActionStatus.SENT,
            user_name="Test User",
            user_email="<EMAIL>",
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        assert action_success.is_successful is True
        assert action_success.is_failed is False
        assert action_success.can_retry is False
        
        # Test failed action that can retry
        action_failed = Action(
            subject="Failed Action",
            message="Test message",
            action_type=ActionType.EMAIL,
            status=ActionStatus.FAILED,
            retry_count=1,
            user_name="Test User",
            user_email="<EMAIL>",
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        assert action_failed.is_successful is False
        assert action_failed.is_failed is True
        assert action_failed.can_retry is True
        
        # Test failed action that cannot retry
        action_no_retry = Action(
            subject="No Retry Action",
            message="Test message",
            action_type=ActionType.EMAIL,
            status=ActionStatus.FAILED,
            retry_count=3,
            user_name="Test User",
            user_email="<EMAIL>",
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        assert action_no_retry.can_retry is False


class TestModelRelationships:
    def test_user_actions_relationship(self, test_db_session, sample_user_data, sample_bill_data, sample_campaign_data, sample_official_data):
        """Test the relationship between users and actions"""
        # Create entities
        user = User(email=sample_user_data["email"], hashed_password="hashed", first_name="Test", last_name="User")
        test_db_session.add(user)
        
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        
        official = Official(**sample_official_data)
        test_db_session.add(official)
        
        test_db_session.commit()
        
        campaign_data = sample_campaign_data.copy()
        campaign_data["bill_id"] = bill.id
        campaign = Campaign(**campaign_data)
        test_db_session.add(campaign)
        test_db_session.commit()
        
        # Create actions
        action1 = Action(
            subject="Action 1",
            message="Message 1",
            action_type=ActionType.EMAIL,
            status=ActionStatus.PENDING,
            user_name=user.full_name,
            user_email=user.email,
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        
        action2 = Action(
            subject="Action 2",
            message="Message 2",
            action_type=ActionType.EMAIL,
            status=ActionStatus.SENT,
            user_name=user.full_name,
            user_email=user.email,
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        
        test_db_session.add_all([action1, action2])
        test_db_session.commit()
        
        # Test relationship
        assert len(user.actions) == 2
        assert action1 in user.actions
        assert action2 in user.actions

    def test_bill_campaigns_relationship(self, test_db_session, sample_bill_data, sample_campaign_data):
        """Test the relationship between bills and campaigns"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()
        
        # Create campaigns
        campaign1_data = sample_campaign_data.copy()
        campaign1_data["title"] = "Campaign 1"
        campaign1_data["bill_id"] = bill.id
        campaign1 = Campaign(**campaign1_data)
        
        campaign2_data = sample_campaign_data.copy()
        campaign2_data["title"] = "Campaign 2"
        campaign2_data["bill_id"] = bill.id
        campaign2 = Campaign(**campaign2_data)
        
        test_db_session.add_all([campaign1, campaign2])
        test_db_session.commit()
        
        # Test relationship
        assert len(bill.campaigns) == 2
        assert campaign1 in bill.campaigns
        assert campaign2 in bill.campaigns
        assert campaign1.bill == bill
        assert campaign2.bill == bill