# tests/test_action_tasks.py
"""
Test suite for action processing tasks.

These tests verify the task_process_action function that handles
both email and Twitter action processing.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from uuid import uuid4
from sqlalchemy.orm import Session
from app.tasks import task_process_action
from app.models.action import Action, ActionStatus
from app.models.official import Official
from app.models.campaign import Campaign


class TestActionTasks:
    """Test class for action processing tasks"""

    def create_mock_action(self, action_types=None, has_email=True, has_twitter_handle=True):
        """Helper method to create a mock action"""
        action = Mock(spec=Action)
        action.id = uuid4()
        action.status = ActionStatus.PENDING
        action.action_types = action_types or ["EMAIL"]
        action.subject = "Test Subject"
        action.message = "Test message content"
        action.user_name = "John <PERSON>"
        action.user_email = "<EMAIL>"
        action.user_address = "123 Main St"
        action.user_zip_code = "12345"
        action.contact_email = "<EMAIL>" if has_email else None
        
        # Mock official
        official = Mock(spec=Official)
        official.name = "<PERSON>"
        official.twitter_handle = "senator_smith" if has_twitter_handle else None
        action.official = official
        
        # Mock campaign
        campaign = Mock(spec=Campaign)
        campaign.hashtag = "TestCampaign"
        action.campaign = campaign
        
        return action

    def test_task_process_action_email_only_success(self):
        """Test processing action with EMAIL only - success case"""
        action = self.create_mock_action(action_types=["EMAIL"])
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock email service
        with patch('app.tasks.EmailService') as mock_email_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            # Setup email service mock
            mock_email_service = Mock()
            mock_email_service.send_action_email.return_value = {
                'success': True,
                'message_id': 'email-123'
            }
            mock_email_service_class.return_value = mock_email_service
            
            # Setup action service mock
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify email service was called
            mock_email_service.send_action_email.assert_called_once()
            
            # Verify action status was updated to SENT
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.SENT
            assert update_call['delivery_method'] == 'email'

    def test_task_process_action_twitter_only_success(self):
        """Test processing action with TWITTER only - success case"""
        action = self.create_mock_action(action_types=["TWITTER"])
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock Twitter service
        with patch('app.tasks.TwitterService') as mock_twitter_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            # Setup Twitter service mock
            mock_twitter_service = Mock()
            mock_twitter_service.is_available.return_value = True
            mock_twitter_service.post_action_tweet.return_value = {
                'success': True,
                'tweet_id': '1234567890'
            }
            mock_twitter_service_class.return_value = mock_twitter_service
            
            # Setup action service mock
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify Twitter service was called
            mock_twitter_service.post_action_tweet.assert_called_once()
            
            # Verify action status was updated to SENT
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.SENT
            assert update_call['delivery_method'] == 'twitter'

    def test_task_process_action_both_email_and_twitter_success(self):
        """Test processing action with both EMAIL and TWITTER - success case"""
        action = self.create_mock_action(action_types=["EMAIL", "TWITTER"])
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock both services
        with patch('app.tasks.EmailService') as mock_email_service_class, \
             patch('app.tasks.TwitterService') as mock_twitter_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            # Setup email service mock
            mock_email_service = Mock()
            mock_email_service.send_action_email.return_value = {
                'success': True,
                'message_id': 'email-123'
            }
            mock_email_service_class.return_value = mock_email_service
            
            # Setup Twitter service mock
            mock_twitter_service = Mock()
            mock_twitter_service.is_available.return_value = True
            mock_twitter_service.post_action_tweet.return_value = {
                'success': True,
                'tweet_id': '1234567890'
            }
            mock_twitter_service_class.return_value = mock_twitter_service
            
            # Setup action service mock
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify both services were called
            mock_email_service.send_action_email.assert_called_once()
            mock_twitter_service.post_action_tweet.assert_called_once()
            
            # Verify action status was updated to SENT with both delivery methods
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.SENT
            assert 'email' in update_call['delivery_method']
            assert 'twitter' in update_call['delivery_method']

    def test_task_process_action_email_fails_twitter_succeeds(self):
        """Test processing action where email fails but Twitter succeeds"""
        action = self.create_mock_action(action_types=["EMAIL", "TWITTER"])
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock both services
        with patch('app.tasks.EmailService') as mock_email_service_class, \
             patch('app.tasks.TwitterService') as mock_twitter_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            # Setup email service mock (fails)
            mock_email_service = Mock()
            mock_email_service.send_action_email.return_value = {
                'success': False,
                'error_message': 'Email delivery failed'
            }
            mock_email_service_class.return_value = mock_email_service
            
            # Setup Twitter service mock (succeeds)
            mock_twitter_service = Mock()
            mock_twitter_service.is_available.return_value = True
            mock_twitter_service.post_action_tweet.return_value = {
                'success': True,
                'tweet_id': '1234567890'
            }
            mock_twitter_service_class.return_value = mock_twitter_service
            
            # Setup action service mock
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify action status was updated to PARTIAL
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.PARTIAL
            assert 'Email: Email delivery failed' in update_call['error_message']

    def test_task_process_action_both_fail(self):
        """Test processing action where both email and Twitter fail"""
        action = self.create_mock_action(action_types=["EMAIL", "TWITTER"])
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock both services
        with patch('app.tasks.EmailService') as mock_email_service_class, \
             patch('app.tasks.TwitterService') as mock_twitter_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            # Setup email service mock (fails)
            mock_email_service = Mock()
            mock_email_service.send_action_email.return_value = {
                'success': False,
                'error_message': 'Email delivery failed'
            }
            mock_email_service_class.return_value = mock_email_service
            
            # Setup Twitter service mock (fails)
            mock_twitter_service = Mock()
            mock_twitter_service.is_available.return_value = True
            mock_twitter_service.post_action_tweet.return_value = {
                'success': False,
                'error_message': 'Tweet posting failed'
            }
            mock_twitter_service_class.return_value = mock_twitter_service
            
            # Setup action service mock
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify action status was updated to FAILED
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.FAILED
            assert 'Email: Email delivery failed' in update_call['error_message']
            assert 'Twitter: Tweet posting failed' in update_call['error_message']

    def test_task_process_action_no_contact_info(self):
        """Test processing action with no contact information"""
        action = self.create_mock_action(
            action_types=["EMAIL", "TWITTER"],
            has_email=False,
            has_twitter_handle=False
        )
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock action service
        with patch('app.tasks.ActionService') as mock_action_service_class:
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify action status was updated to FAILED
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.FAILED
            assert 'No contact email available' in update_call['error_message']
            assert 'Official has no Twitter handle' in update_call['error_message']

    def test_task_process_action_twitter_service_unavailable(self):
        """Test processing action when Twitter service is not configured"""
        action = self.create_mock_action(action_types=["TWITTER"])
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock Twitter service (unavailable)
        with patch('app.tasks.TwitterService') as mock_twitter_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            # Setup Twitter service mock (not available)
            mock_twitter_service = Mock()
            mock_twitter_service.is_available.return_value = False
            mock_twitter_service_class.return_value = mock_twitter_service
            
            # Setup action service mock
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task
            task_process_action(action.id, mock_db)
            
            # Verify Twitter service was not called for posting
            mock_twitter_service.post_action_tweet.assert_not_called()
            
            # Verify action status was updated to FAILED
            mock_action_service.update_action.assert_called_once()
            update_call = mock_action_service.update_action.call_args[1]
            assert update_call['status'] == ActionStatus.FAILED
            assert 'Twitter service not configured' in update_call['error_message']

    def test_task_process_action_not_found(self):
        """Test processing action that doesn't exist in database"""
        action_id = uuid4()
        
        # Mock database session (action not found)
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute task (should handle gracefully)
        task_process_action(action_id, mock_db)
        
        # No exceptions should be raised

    def test_task_process_action_not_pending(self):
        """Test processing action that is not in pending status"""
        action = self.create_mock_action()
        action.status = ActionStatus.SENT  # Already sent
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Execute task (should skip processing)
        task_process_action(action.id, mock_db)
        
        # No services should be called since action is not pending

    def test_task_process_action_exception_handling(self):
        """Test task handles exceptions gracefully"""
        action = self.create_mock_action()
        
        # Mock database session
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = action
        
        # Mock email service to raise exception
        with patch('app.tasks.EmailService') as mock_email_service_class, \
             patch('app.tasks.ActionService') as mock_action_service_class:
            
            mock_email_service = Mock()
            mock_email_service.send_action_email.side_effect = Exception("Service error")
            mock_email_service_class.return_value = mock_email_service
            
            mock_action_service = Mock()
            mock_action_service_class.return_value = mock_action_service
            
            # Execute task (should handle exception gracefully)
            task_process_action(action.id, mock_db)
            
            # Verify action status was updated to FAILED
            mock_action_service.update_action.assert_called()
            update_calls = mock_action_service.update_action.call_args_list
            
            # Should have at least one call to update status to FAILED
            failed_update = None
            for call in update_calls:
                if call[1].get('status') == ActionStatus.FAILED:
                    failed_update = call[1]
                    break
            
            assert failed_update is not None
            assert 'error_message' in failed_update
