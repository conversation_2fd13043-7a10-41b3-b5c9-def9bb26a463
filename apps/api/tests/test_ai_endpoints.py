# tests/test_ai_endpoints.py
"""
Test suite for AI API endpoints.

These tests verify the AI service endpoints including message personalization,
text generation, and health monitoring functionality.
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


class TestAIEndpoints:
    """Test class for AI API endpoints"""

    def test_personalize_message_success(self):
        """Test successful message personalization"""
        # Mock the AI service response
        mock_result = {
            "personalized_message": "As a concerned citizen, I urge you to support this important legislation. This issue affects our community directly and requires immediate attention.",
            "original_length": 45,
            "personalized_length": 125,
            "processing_time_ms": 1250.5
        }
        
        with patch('app.api.v1.endpoints.ai.personalize_message', return_value=mock_result):
            response = client.post(
                "/api/v1/ai/personalize-message",
                json={
                    "raw_text": "I care about this issue and want action taken",
                    "context": "Climate Action Now Act - Support renewable energy",
                    "tone": "professional"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["personalized_message"] == mock_result["personalized_message"]
            assert data["original_length"] == 45
            assert data["personalized_length"] == 125
            assert data["processing_time_ms"] == 1250.5

    def test_personalize_message_invalid_input(self):
        """Test message personalization with invalid input"""
        # Test empty raw_text
        response = client.post(
            "/api/v1/ai/personalize-message",
            json={
                "raw_text": "",
                "context": "Climate Action Now Act",
                "tone": "professional"
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_personalize_message_ai_service_error(self):
        """Test message personalization when AI service fails"""
        with patch('app.api.v1.endpoints.ai.personalize_message', side_effect=RuntimeError("AI service unavailable")):
            response = client.post(
                "/api/v1/ai/personalize-message",
                json={
                    "raw_text": "I care about this issue",
                    "context": "Climate Action Now Act",
                    "tone": "professional"
                }
            )
            
            assert response.status_code == 500
            assert "AI service temporarily unavailable" in response.json()["detail"]

    def test_ai_health_endpoint_healthy(self):
        """Test AI health endpoint when service is healthy"""
        mock_health = {
            "status": "healthy",
            "model_loaded": True,
            "capabilities": ["summarization", "text_generation", "message_personalization"],
            "model_info": {
                "model_name": "t5-small",
                "framework": "transformers",
                "device": "cpu"
            }
        }
        
        with patch('app.api.v1.endpoints.ai.ai_health_check', return_value=mock_health):
            response = client.get("/api/v1/ai/health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "healthy"
            assert data["model_loaded"] is True
            assert "message_personalization" in data["capabilities"]
            assert data["model_info"]["model_name"] == "t5-small"

    def test_ai_health_endpoint_unhealthy(self):
        """Test AI health endpoint when service is unhealthy"""
        with patch('app.api.v1.endpoints.ai.ai_health_check', side_effect=Exception("Model loading failed")):
            response = client.get("/api/v1/ai/health")
            
            assert response.status_code == 200  # Health endpoint should not fail
            data = response.json()
            
            assert data["status"] == "unhealthy"
            assert data["model_loaded"] is False
            assert data["capabilities"] == []

    def test_generate_text_success(self):
        """Test successful text generation"""
        mock_result = {
            "personalized_message": "Environmental protection is crucial for our future. We must take immediate action to address climate change through sustainable policies.",
            "original_length": 47,
            "personalized_length": 120,
            "processing_time_ms": 890.2
        }
        
        with patch('app.api.v1.endpoints.ai.personalize_message', return_value=mock_result):
            response = client.post(
                "/api/v1/ai/generate-text",
                json={
                    "prompt": "Write about environmental protection importance",
                    "max_length": 200,
                    "temperature": 0.7
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["generated_text"] == mock_result["personalized_message"]
            assert data["prompt_length"] == 47
            assert data["generated_length"] == 120

    def test_get_ai_capabilities(self):
        """Test AI capabilities endpoint"""
        mock_health = {
            "status": "healthy",
            "model_loaded": True,
            "capabilities": ["summarization", "text_generation", "message_personalization"],
            "model_info": {"model_name": "t5-small"}
        }
        
        with patch('app.api.v1.endpoints.ai.ai_health_check', return_value=mock_health):
            response = client.get("/api/v1/ai/capabilities")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["service"] == "ModernAction AI Service"
            assert data["status"] == "healthy"
            assert "message_personalization" in data["capabilities"]
            assert "message_personalization" in data["features"]
            assert data["features"]["message_personalization"]["supported_tones"] == [
                "professional", "passionate", "formal", "personal", "urgent"
            ]

    def test_get_model_info(self):
        """Test model info endpoint"""
        mock_model_info = {
            "model_name": "t5-small",
            "status": "loaded",
            "framework": "transformers",
            "device": "cpu"
        }
        
        mock_health = {
            "status": "healthy",
            "model_loaded": True
        }
        
        with patch('app.services.ai.get_model_info', return_value=mock_model_info), \
             patch('app.api.v1.endpoints.ai.ai_health_check', return_value=mock_health):
            
            response = client.get("/api/v1/ai/models/info")
            
            assert response.status_code == 200
            data = response.json()
            
            assert "models" in data
            assert "performance" in data
            assert "limitations" in data
            assert data["models"]["summarization"]["model_name"] == "t5-small"

    def test_personalize_message_different_tones(self):
        """Test message personalization with different tones"""
        mock_result = {
            "personalized_message": "Test personalized message",
            "original_length": 20,
            "personalized_length": 25,
            "processing_time_ms": 1000.0
        }
        
        tones = ["professional", "passionate", "formal", "personal", "urgent"]
        
        for tone in tones:
            with patch('app.api.v1.endpoints.ai.personalize_message', return_value=mock_result):
                response = client.post(
                    "/api/v1/ai/personalize-message",
                    json={
                        "raw_text": "I care about this issue",
                        "context": "Test context",
                        "tone": tone
                    }
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["personalized_message"] == "Test personalized message"

    def test_personalize_message_invalid_tone(self):
        """Test message personalization with invalid tone"""
        response = client.post(
            "/api/v1/ai/personalize-message",
            json={
                "raw_text": "I care about this issue",
                "context": "Test context",
                "tone": "invalid_tone"
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_personalize_message_missing_context(self):
        """Test message personalization with missing context"""
        response = client.post(
            "/api/v1/ai/personalize-message",
            json={
                "raw_text": "I care about this issue",
                "tone": "professional"
                # Missing context
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_personalize_message_integration(self):
        """Integration test for message personalization endpoint without mocking"""
        # This test uses the actual AI service but with a simple mock
        # to avoid loading the full ML model in tests

        with patch('app.services.ai.get_text_generator') as mock_generator:
            # Mock the text generator to return a predictable result
            mock_pipeline = Mock()
            mock_pipeline.return_value = [{'generated_text': 'As a concerned citizen, I urge you to support this important legislation.'}]
            mock_generator.return_value = mock_pipeline

            response = client.post(
                "/api/v1/ai/personalize-message",
                json={
                    "raw_text": "I care about this issue",
                    "context": "Climate Action Now Act",
                    "tone": "professional"
                }
            )

            assert response.status_code == 200
            data = response.json()

            assert "personalized_message" in data
            assert "processing_time_ms" in data
            assert data["original_length"] == 23  # Length of "I care about this issue"
            assert data["personalized_length"] > 0
