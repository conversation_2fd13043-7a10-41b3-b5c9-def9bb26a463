import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.db.database import get_db
from app.db.base_class import Base
from app.core.config import settings


@pytest.fixture(scope="function")
def test_db():
    """Create a test database for each test function"""
    # Create a temporary database file
    db_fd, db_path = tempfile.mkstemp(suffix=".db")
    test_database_url = f"sqlite:///{db_path}"
    
    # Create test engine
    engine = create_engine(
        test_database_url,
        connect_args={"check_same_thread": False},
    )
    
    # Create test session
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield TestingSessionLocal
    
    # Cleanup
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture(scope="function")
def test_client(test_db):
    """Create a test client with test database"""
    def override_get_db():
        try:
            db = test_db()
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def test_db_session(test_db):
    """Create a test database session"""
    session = test_db()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
        "zip_code": "12345"
    }


@pytest.fixture
def sample_bill_data():
    """Sample bill data for testing"""
    return {
        "title": "Test Bill",
        "description": "A test bill for testing purposes",
        "bill_number": "HR-123",
        "bill_type": "house_bill",
        "status": "introduced",
        "session_year": 2024,
        "chamber": "house",
        "state": "federal",
        "full_text": "This is the full text of the test bill.",
        "summary": "This is a summary of the test bill.",
        "is_featured": False,
        "priority_score": 0
    }


@pytest.fixture
def sample_official_data():
    """Sample official data for testing"""
    return {
        "name": "Test Official",
        "title": "Representative",
        "party": "Independent",
        "email": "<EMAIL>",
        "phone": "555-0123",
        "level": "federal",
        "chamber": "house",
        "state": "CA",
        "district": "1",
        "is_active": True
    }


@pytest.fixture
def sample_campaign_data():
    """Sample campaign data for testing"""
    return {
        "title": "Test Campaign",
        "description": "A test campaign for testing purposes",
        "campaign_type": "support",
        "status": "active",
        "call_to_action": "Support this important legislation!",
        "is_featured": False,
        "is_public": True,
        "requires_verification": False,
        "actual_actions": 0
    }