# app/models/user.py
from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.db.base_class import Base
import uuid

class User(Base):
    __tablename__ = "users"
    
    # Basic user information
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    
    # User preferences and settings
    zip_code = Column(String, nullable=True)
    phone_number = Column(String, nullable=True)
    
    # User status and permissions
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # User engagement preferences
    email_notifications = Column(<PERSON>olean, default=True, nullable=False)
    sms_notifications = Column(Boolean, default=False, nullable=False)
    
    # User profile information
    bio = Column(Text, nullable=True)
    profile_picture_url = Column(String, nullable=True)
    
    # Email verification
    email_verification_token = Column(String, nullable=True)
    email_verified_at = Column(DateTime, nullable=True)
    
    # Password reset
    password_reset_token = Column(String, nullable=True)
    password_reset_expires_at = Column(DateTime, nullable=True)
    
    # Last login tracking
    last_login_at = Column(DateTime, nullable=True)
    
    # Relationships
    actions = relationship("Action", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(email='{self.email}', name='{self.first_name} {self.last_name}')>"
    
    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"