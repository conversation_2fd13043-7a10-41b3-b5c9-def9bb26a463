# app/schemas/ai.py
"""
Pydantic schemas for AI service endpoints.

This module defines the request and response schemas for AI-powered features
including message personalization and text generation.
"""

from pydantic import BaseModel, Field
from typing import Optional


class PersonalizeMessageRequest(BaseModel):
    """Request schema for message personalization endpoint."""
    
    raw_text: str = Field(
        ...,
        description="The user's raw message or personal story to be enhanced",
        min_length=10,
        max_length=1000,
        example="I am a parent concerned about climate change affecting my children's future."
    )
    
    context: str = Field(
        ...,
        description="Context for personalization (e.g., campaign title, issue, or goal)",
        min_length=5,
        max_length=200,
        example="Climate Action Now Act - Support renewable energy legislation"
    )
    
    tone: Optional[str] = Field(
        default="professional",
        description="Desired tone for the personalized message",
        pattern="^(professional|passionate|formal|personal|urgent)$",
        example="professional"
    )


class PersonalizeMessageResponse(BaseModel):
    """Response schema for message personalization endpoint."""
    
    personalized_message: str = Field(
        ...,
        description="The AI-generated personalized message",
        example="As a concerned parent, I urge you to support the Climate Action Now Act. The future of our children depends on immediate action to transition to renewable energy sources. This legislation represents a critical step toward ensuring a sustainable environment for the next generation."
    )
    
    original_length: int = Field(
        ...,
        description="Character count of the original raw text",
        example=85
    )
    
    personalized_length: int = Field(
        ...,
        description="Character count of the personalized message",
        example=245
    )
    
    processing_time_ms: float = Field(
        ...,
        description="Time taken to generate the personalized message in milliseconds",
        example=1250.5
    )


class AIHealthResponse(BaseModel):
    """Response schema for AI service health check."""
    
    status: str = Field(
        ...,
        description="Overall health status of the AI service",
        example="healthy"
    )
    
    model_loaded: bool = Field(
        ...,
        description="Whether the AI model is successfully loaded",
        example=True
    )
    
    capabilities: list[str] = Field(
        ...,
        description="List of AI capabilities available",
        example=["summarization", "text_generation", "message_personalization"]
    )
    
    model_info: dict = Field(
        ...,
        description="Information about the loaded AI model",
        example={
            "model_name": "t5-small",
            "framework": "transformers",
            "device": "cpu"
        }
    )


class TextGenerationRequest(BaseModel):
    """Request schema for general text generation."""
    
    prompt: str = Field(
        ...,
        description="The prompt or seed text for generation",
        min_length=5,
        max_length=500,
        example="Write a persuasive message about environmental protection"
    )
    
    max_length: Optional[int] = Field(
        default=200,
        description="Maximum length of generated text",
        ge=50,
        le=500,
        example=200
    )
    
    temperature: Optional[float] = Field(
        default=0.7,
        description="Creativity/randomness of generation (0.0 = deterministic, 1.0 = very creative)",
        ge=0.0,
        le=1.0,
        example=0.7
    )


class TextGenerationResponse(BaseModel):
    """Response schema for text generation."""
    
    generated_text: str = Field(
        ...,
        description="The AI-generated text",
        example="Environmental protection is crucial for our planet's future. We must act now to preserve natural resources and combat climate change through sustainable practices and renewable energy adoption."
    )
    
    prompt_length: int = Field(
        ...,
        description="Character count of the input prompt",
        example=55
    )
    
    generated_length: int = Field(
        ...,
        description="Character count of the generated text",
        example=185
    )
    
    processing_time_ms: float = Field(
        ...,
        description="Time taken to generate the text in milliseconds",
        example=890.2
    )
