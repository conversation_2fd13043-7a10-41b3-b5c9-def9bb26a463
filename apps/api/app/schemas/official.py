# app/schemas/official.py
from pydantic import BaseModel, EmailStr, ConfigDict, HttpUrl
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.official import OfficialLevel, OfficialChamber

class OfficialBase(BaseModel):
    """Base official schema with common fields"""
    name: str
    title: str
    party: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[HttpUrl] = None
    twitter_handle: Optional[str] = None
    office_address: Optional[str] = None
    office_city: Optional[str] = None
    office_state: Optional[str] = None
    office_zip: Optional[str] = None
    level: OfficialLevel
    chamber: Optional[OfficialChamber] = None
    state: Optional[str] = None
    district: Optional[str] = None
    bio: Optional[str] = None
    profile_picture_url: Optional[HttpUrl] = None
    term_start: Optional[str] = None
    term_end: Optional[str] = None
    is_active: bool = True

class OfficialCreate(OfficialBase):
    """Schema for creating a new official"""
    bioguide_id: Optional[str] = None
    openstates_id: Optional[str] = None
    google_civic_id: Optional[str] = None

class OfficialUpdate(BaseModel):
    """Schema for updating official information"""
    name: Optional[str] = None
    title: Optional[str] = None
    party: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[HttpUrl] = None
    twitter_handle: Optional[str] = None
    office_address: Optional[str] = None
    office_city: Optional[str] = None
    office_state: Optional[str] = None
    office_zip: Optional[str] = None
    bio: Optional[str] = None
    profile_picture_url: Optional[HttpUrl] = None
    is_active: Optional[bool] = None

class OfficialResponse(OfficialBase):
    """Schema for official API responses"""
    id: str
    created_at: datetime
    updated_at: datetime
    bioguide_id: Optional[str] = None
    openstates_id: Optional[str] = None
    google_civic_id: Optional[str] = None
    twitter_handle: Optional[str] = None
    facebook_url: Optional[HttpUrl] = None
    instagram_handle: Optional[str] = None
    response_rate: Optional[int] = None
    avg_response_time: Optional[int] = None
    
    model_config = ConfigDict(from_attributes=True)

class Official(OfficialResponse):
    """Complete official schema for internal use"""
    voting_record: Optional[List[Dict[str, Any]]] = None
    positions: Optional[List[Dict[str, Any]]] = None
    official_metadata: Optional[Dict[str, Any]] = None

class OfficialSummary(BaseModel):
    """Lightweight official summary for lists"""
    id: str
    name: str
    title: str
    party: Optional[str] = None
    level: OfficialLevel
    state: Optional[str] = None
    district: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

class OfficialSearch(BaseModel):
    """Schema for official search parameters"""
    query: Optional[str] = None
    level: Optional[OfficialLevel] = None
    chamber: Optional[OfficialChamber] = None
    state: Optional[str] = None
    district: Optional[str] = None
    party: Optional[str] = None
    is_active: Optional[bool] = True
    zip_code: Optional[str] = None
    limit: int = 20
    offset: int = 0

class OfficialContact(BaseModel):
    """Contact information for an official"""
    id: str
    name: str
    title: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[HttpUrl] = None
    office_address: Optional[str] = None
    preferred_contact_method: str
    
    model_config = ConfigDict(from_attributes=True)