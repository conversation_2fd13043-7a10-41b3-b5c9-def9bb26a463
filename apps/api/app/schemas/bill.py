# app/schemas/bill.py
from pydantic import BaseModel, ConfigDict, HttpUrl
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.bill import BillStatus, BillType

class BillBase(BaseModel):
    """Base bill schema with common fields"""
    title: str
    description: Optional[str] = None
    bill_number: str
    bill_type: BillType
    status: BillStatus
    session_year: int
    chamber: str
    state: str = "federal"
    summary: Optional[str] = None
    ai_summary: Optional[str] = None
    source_url: Optional[HttpUrl] = None
    text_url: Optional[HttpUrl] = None
    sponsor_name: Optional[str] = None
    sponsor_party: Optional[str] = None
    sponsor_state: Optional[str] = None
    is_featured: bool = False
    priority_score: int = 0

class BillCreate(BillBase):
    """Schema for creating a new bill"""
    full_text: Optional[str] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    openstates_id: Optional[str] = None
    congress_gov_id: Optional[str] = None

class BillUpdate(BaseModel):
    """Schema for updating bill information"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[BillStatus] = None
    summary: Optional[str] = None
    ai_summary: Optional[str] = None
    full_text: Optional[str] = None
    source_url: Optional[HttpUrl] = None
    text_url: Optional[HttpUrl] = None
    is_featured: Optional[bool] = None
    priority_score: Optional[int] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None

class BillResponse(BillBase):
    """Schema for bill API responses"""
    id: str
    created_at: datetime
    updated_at: datetime
    introduced_date: Optional[datetime] = None
    last_action_date: Optional[datetime] = None
    openstates_id: Optional[str] = None
    congress_gov_id: Optional[str] = None
    cosponsors: Optional[List[Dict[str, Any]]] = None
    vote_history: Optional[List[Dict[str, Any]]] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    
    model_config = ConfigDict(from_attributes=True)

class Bill(BillResponse):
    """Complete bill schema for internal use"""
    full_text: Optional[str] = None
    bill_metadata: Optional[Dict[str, Any]] = None

class BillSummary(BaseModel):
    """Lightweight bill summary for lists"""
    id: str
    title: str
    bill_number: str
    status: BillStatus
    chamber: str
    ai_summary: Optional[str] = None
    is_featured: bool
    priority_score: int
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class BillSearch(BaseModel):
    """Schema for bill search parameters"""
    query: Optional[str] = None
    status: Optional[BillStatus] = None
    chamber: Optional[str] = None
    state: Optional[str] = None
    session_year: Optional[int] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    is_featured: Optional[bool] = None
    limit: int = 20
    offset: int = 0

class BillStats(BaseModel):
    """Bill statistics schema"""
    total_bills: int
    by_status: Dict[str, int]
    by_chamber: Dict[str, int]
    featured_count: int
    recent_updates: int