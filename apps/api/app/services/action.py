# app/services/action.py
"""
Service layer for action management.

This module handles the business logic for creating, updating, and querying actions.
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from app.models.action import Action, ActionStatus, ActionType
from app.models.user import User
from app.models.campaign import Campaign
from app.models.official import Official
from app.schemas.action import (
    ActionCreate, 
    ActionUpdate, 
    ActionResponse, 
    ActionSummary, 
    ActionSearch,
    ActionStats,
    ActionWithDetails
)

logger = logging.getLogger(__name__)

class ActionService:
    """Service class for managing actions"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_action(self, action_data: ActionCreate) -> ActionResponse:
        """
        Create a new action record.
        
        Args:
            action_data: The action data to create
            
        Returns:
            ActionResponse: The created action
            
        Raises:
            ValueError: If referenced entities don't exist
        """
        logger.info(f"Creating action for campaign {action_data.campaign_id} and official {action_data.official_id}")
        
        # Validate that campaign exists
        campaign = self.db.query(Campaign).filter(Campaign.id == action_data.campaign_id).first()
        if not campaign:
            raise ValueError(f"Campaign with id {action_data.campaign_id} not found")
        
        # Validate that official exists
        official = self.db.query(Official).filter(Official.id == action_data.official_id).first()
        if not official:
            raise ValueError(f"Official with id {action_data.official_id} not found")
        
        # Find or create user based on email
        user = self.db.query(User).filter(User.email == action_data.user_email).first()
        if not user:
            # Create a new user record
            user = User(
                name=action_data.user_name,
                email=action_data.user_email,
                address=action_data.user_address,
                zip_code=action_data.user_zip_code
            )
            self.db.add(user)
            self.db.flush()  # Get the user ID
        
        # Create the action
        action = Action(
            subject=action_data.subject,
            message=action_data.message,
            action_type=action_data.action_type,
            user_name=action_data.user_name,
            user_email=action_data.user_email,
            user_address=action_data.user_address,
            user_zip_code=action_data.user_zip_code,
            contact_email=action_data.contact_email or official.email,
            contact_phone=action_data.contact_phone or official.phone,
            contact_address=action_data.contact_address or official.office_address,
            user_id=user.id,
            campaign_id=action_data.campaign_id,
            official_id=action_data.official_id,
            status=ActionStatus.PENDING
        )
        
        self.db.add(action)
        self.db.commit()
        self.db.refresh(action)
        
        # Update campaign action count
        campaign.actual_actions += 1
        self.db.commit()
        
        logger.info(f"Created action {action.id} for user {user.email}")
        
        return ActionResponse.model_validate(action)
    
    def get_action(self, action_id: UUID) -> Optional[ActionResponse]:
        """Get an action by ID"""
        action = self.db.query(Action).filter(Action.id == action_id).first()
        if not action:
            return None
        return ActionResponse.model_validate(action)
    
    def get_actions(self, search_params: ActionSearch) -> List[ActionSummary]:
        """
        Get actions with optional filtering.
        
        Args:
            search_params: Search and filter parameters
            
        Returns:
            List[ActionSummary]: List of action summaries
        """
        query = self.db.query(Action).join(Official)
        
        # Apply filters
        if search_params.user_id:
            query = query.filter(Action.user_id == search_params.user_id)
        if search_params.campaign_id:
            query = query.filter(Action.campaign_id == search_params.campaign_id)
        if search_params.official_id:
            query = query.filter(Action.official_id == search_params.official_id)
        if search_params.status:
            query = query.filter(Action.status == search_params.status)
        if search_params.action_type:
            query = query.filter(Action.action_type == search_params.action_type)
        if search_params.date_from:
            query = query.filter(Action.created_at >= search_params.date_from)
        if search_params.date_to:
            query = query.filter(Action.created_at <= search_params.date_to)
        
        # Order by most recent first
        query = query.order_by(desc(Action.created_at))
        
        # Apply pagination
        actions = query.offset(search_params.offset).limit(search_params.limit).all()
        
        # Convert to summaries
        summaries = []
        for action in actions:
            summary = ActionSummary(
                id=str(action.id),
                subject=action.subject,
                action_type=action.action_type,
                status=action.status,
                campaign_id=str(action.campaign_id),
                official_name=action.official.name,
                created_at=action.created_at,
                sent_at=action.sent_at
            )
            summaries.append(summary)
        
        return summaries
    
    def update_action(self, action_id: UUID, update_data: ActionUpdate) -> Optional[ActionResponse]:
        """
        Update an action's status and delivery information.
        
        Args:
            action_id: The ID of the action to update
            update_data: The fields to update
            
        Returns:
            ActionResponse: The updated action, or None if not found
        """
        action = self.db.query(Action).filter(Action.id == action_id).first()
        if not action:
            return None
        
        # Update fields
        for field, value in update_data.model_dump(exclude_unset=True).items():
            if hasattr(action, field):
                setattr(action, field, value)
        
        # Auto-set sent_at if status changes to SENT
        if update_data.status == ActionStatus.SENT and not action.sent_at:
            action.sent_at = datetime.utcnow()
        
        # Auto-set delivered_at if status changes to DELIVERED
        if update_data.status == ActionStatus.DELIVERED and not action.delivered_at:
            action.delivered_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(action)
        
        logger.info(f"Updated action {action_id} with status {action.status}")
        
        return ActionResponse.model_validate(action)
    
    def get_action_stats(self, campaign_id: Optional[UUID] = None) -> ActionStats:
        """
        Get action statistics.
        
        Args:
            campaign_id: Optional campaign ID to filter stats
            
        Returns:
            ActionStats: Statistics about actions
        """
        query = self.db.query(Action)
        
        if campaign_id:
            query = query.filter(Action.campaign_id == campaign_id)
        
        total_actions = query.count()
        
        if total_actions == 0:
            return ActionStats(
                total_actions=0,
                successful_actions=0,
                pending_actions=0,
                failed_actions=0,
                success_rate=0.0,
                by_type={},
                by_status={}
            )
        
        # Count by status
        status_counts = {}
        for status in ActionStatus:
            count = query.filter(Action.status == status).count()
            status_counts[status.value] = count
        
        # Count by type
        type_counts = {}
        for action_type in ActionType:
            count = query.filter(Action.action_type == action_type).count()
            type_counts[action_type.value] = count
        
        successful_actions = status_counts.get(ActionStatus.SENT.value, 0) + \
                           status_counts.get(ActionStatus.DELIVERED.value, 0)
        pending_actions = status_counts.get(ActionStatus.PENDING.value, 0)
        failed_actions = status_counts.get(ActionStatus.FAILED.value, 0) + \
                        status_counts.get(ActionStatus.BOUNCED.value, 0)
        
        success_rate = (successful_actions / total_actions) * 100 if total_actions > 0 else 0.0
        
        # Calculate average response time for successful actions
        avg_response_time = None
        successful_query = query.filter(Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED]))
        if successful_query.count() > 0:
            avg_seconds = self.db.query(
                func.avg(
                    func.extract('epoch', Action.sent_at) - 
                    func.extract('epoch', Action.created_at)
                )
            ).filter(
                Action.sent_at.isnot(None),
                Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED])
            ).scalar()
            
            if avg_seconds:
                avg_response_time = float(avg_seconds)
        
        return ActionStats(
            total_actions=total_actions,
            successful_actions=successful_actions,
            pending_actions=pending_actions,
            failed_actions=failed_actions,
            success_rate=success_rate,
            average_response_time=avg_response_time,
            by_type=type_counts,
            by_status=status_counts
        )
    
    def get_action_with_details(self, action_id: UUID) -> Optional[ActionWithDetails]:
        """
        Get an action with full campaign and official details.
        
        Args:
            action_id: The ID of the action
            
        Returns:
            ActionWithDetails: Action with related details, or None if not found
        """
        action = self.db.query(Action).filter(Action.id == action_id).first()
        if not action:
            return None
        
        # Build response with related data
        response_data = ActionResponse.model_validate(action).model_dump()
        response_data.update({
            'campaign_title': action.campaign.title,
            'official_name': action.official.name,
            'official_title': f"{action.official.chamber.title() if action.official.chamber else ''} {action.official.level.title()}".strip(),
            'bill_title': action.campaign.bill.title,
            'bill_number': action.campaign.bill.bill_number
        })
        
        return ActionWithDetails(**response_data)
    
    def delete_action(self, action_id: UUID) -> bool:
        """
        Delete an action.
        
        Args:
            action_id: The ID of the action to delete
            
        Returns:
            bool: True if deleted, False if not found
        """
        action = self.db.query(Action).filter(Action.id == action_id).first()
        if not action:
            return False
        
        # Update campaign action count
        campaign = action.campaign
        if campaign.actual_actions > 0:
            campaign.actual_actions -= 1
        
        self.db.delete(action)
        self.db.commit()
        
        logger.info(f"Deleted action {action_id}")
        return True
    
    def retry_failed_action(self, action_id: UUID) -> Optional[ActionResponse]:
        """
        Retry a failed action by resetting its status to PENDING.
        
        Args:
            action_id: The ID of the action to retry
            
        Returns:
            ActionResponse: The updated action, or None if not found or can't retry
        """
        action = self.db.query(Action).filter(Action.id == action_id).first()
        if not action or not action.can_retry:
            return None
        
        action.status = ActionStatus.PENDING
        action.retry_count += 1
        action.error_message = None
        
        self.db.commit()
        self.db.refresh(action)
        
        logger.info(f"Retrying action {action_id} (attempt {action.retry_count})")
        
        return ActionResponse.model_validate(action)