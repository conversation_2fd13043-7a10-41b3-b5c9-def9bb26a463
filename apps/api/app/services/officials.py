# app/services/officials.py
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.models.official import Official
from app.schemas.official import OfficialCreate, OfficialUpdate, OfficialSearch
from app.models.official import OfficialLevel, OfficialChamber

class OfficialService:
    """Service layer for official operations"""
    
    def __init__(self, db: Session):
        self.db = db

    def get_official(self, official_id: str) -> Optional[Official]:
        """Get a single official by ID"""
        return self.db.query(Official).filter(Official.id == official_id).first()

    def get_officials(self, skip: int = 0, limit: int = 20) -> List[Official]:
        """Get a list of officials with pagination"""
        return self.db.query(Official).filter(Official.is_active == True).offset(skip).limit(limit).all()

    def search_officials(self, search_params: OfficialSearch) -> List[Official]:
        """Search officials based on various criteria"""
        query = self.db.query(Official).filter(Official.is_active == True)
        
        # Text search across name and title
        if search_params.query:
            search_term = f"%{search_params.query.lower()}%"
            query = query.filter(
                or_(
                    func.lower(Official.name).contains(search_term),
                    func.lower(Official.title).contains(search_term)
                )
            )
        
        # Filter by level (federal, state, local)
        if search_params.level:
            query = query.filter(Official.level == search_params.level)
            
        # Filter by chamber
        if search_params.chamber:
            query = query.filter(Official.chamber == search_params.chamber)
            
        # Filter by state
        if search_params.state:
            query = query.filter(Official.state == search_params.state)
            
        # Filter by district
        if search_params.district:
            query = query.filter(Official.district == search_params.district)
            
        # Filter by party
        if search_params.party:
            query = query.filter(func.lower(Official.party) == search_params.party.lower())
        
        return query.offset(search_params.offset).limit(search_params.limit).all()

    def get_officials_by_zip_code(self, zip_code: str) -> List[Official]:
        """Get officials representing a specific zip code area"""
        # This is a simplified implementation
        # In a real system, you'd need to use geographic data to map zip codes to districts
        # For MVP, we'll return federal officials and officials from the state derived from zip code
        
        # Simple zip code to state mapping (first 3 digits approach)
        # This is very basic and would need proper geographic data in production
        state_mapping = {
            '100': 'NY', '101': 'NY', '102': 'NY', '103': 'NY', '104': 'NY',
            '200': 'DC', '201': 'VA', '202': 'DC', '203': 'CT', '204': 'MD',
            '300': 'PA', '301': 'MD', '302': 'DE', '303': 'PA', '304': 'WV',
            '900': 'CA', '901': 'CA', '902': 'CA', '903': 'CA', '904': 'CA',
        }
        
        zip_prefix = zip_code[:3] if len(zip_code) >= 3 else zip_code
        state = state_mapping.get(zip_prefix)
        
        query = self.db.query(Official).filter(Official.is_active == True)
        
        if state:
            # Get federal officials and state officials for this state
            query = query.filter(
                or_(
                    and_(Official.level == OfficialLevel.FEDERAL, Official.state == state),
                    and_(Official.level == OfficialLevel.STATE, Official.state == state)
                )
            )
        else:
            # If we can't determine state, just return federal officials
            query = query.filter(Official.level == OfficialLevel.FEDERAL)
            
        return query.all()

    def create_official(self, official_data: OfficialCreate) -> Official:
        """Create a new official"""
        official = Official(**official_data.model_dump())
        self.db.add(official)
        self.db.commit()
        self.db.refresh(official)
        return official

    def update_official(self, official_id: str, official_data: OfficialUpdate) -> Optional[Official]:
        """Update an existing official"""
        official = self.get_official(official_id)
        if not official:
            return None
            
        update_data = official_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(official, field, value)
            
        self.db.commit()
        self.db.refresh(official)
        return official

    def delete_official(self, official_id: str) -> bool:
        """Soft delete an official by setting is_active to False"""
        official = self.get_official(official_id)
        if not official:
            return False
            
        official.is_active = False
        self.db.commit()
        return True

    def get_officials_by_level(self, level: OfficialLevel) -> List[Official]:
        """Get all officials at a specific government level"""
        return self.db.query(Official).filter(
            and_(Official.level == level, Official.is_active == True)
        ).all()

    def get_officials_by_chamber(self, chamber: OfficialChamber) -> List[Official]:
        """Get all officials in a specific chamber"""
        return self.db.query(Official).filter(
            and_(Official.chamber == chamber, Official.is_active == True)
        ).all()

    def get_officials_count(self) -> int:
        """Get total count of active officials"""
        return self.db.query(Official).filter(Official.is_active == True).count()

    def get_officials_by_external_id(self, external_id: str, id_type: str) -> Optional[Official]:
        """Get official by external ID (bioguide_id, openstates_id, etc.)"""
        if id_type == "bioguide":
            return self.db.query(Official).filter(Official.bioguide_id == external_id).first()
        elif id_type == "openstates":
            return self.db.query(Official).filter(Official.openstates_id == external_id).first()
        elif id_type == "google_civic":
            return self.db.query(Official).filter(Official.google_civic_id == external_id).first()
        else:
            return None