# app/services/twitter.py
"""
Twitter service for posting tweets to officials via the Twitter API.

This module provides functionality to post tweets to government officials
using the Twitter API v2 through the tweepy library.
"""

import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

try:
    import tweepy
except ImportError:
    tweepy = None

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class TwitterService:
    """Service for posting tweets via Twitter API"""
    
    def __init__(self):
        """Initialize the Twitter service with API credentials"""
        if not tweepy:
            raise RuntimeError("tweepy library is not installed. Please install it to use Twitter functionality.")
        
        # Check if Twitter credentials are configured
        if not all([
            settings.TWITTER_API_KEY,
            settings.TWITTER_API_SECRET,
            settings.TWITTER_ACCESS_TOKEN,
            settings.TWITTER_ACCESS_TOKEN_SECRET
        ]):
            logger.warning("Twitter credentials not fully configured. Twitter functionality will be disabled.")
            self.client = None
            return
        
        try:
            # Initialize Twitter API v2 client
            self.client = tweepy.Client(
                consumer_key=settings.TWITTER_API_KEY,
                consumer_secret=settings.TWITTER_API_SECRET,
                access_token=settings.TWITTER_ACCESS_TOKEN,
                access_token_secret=settings.TWITTER_ACCESS_TOKEN_SECRET,
                wait_on_rate_limit=True  # Automatically handle rate limiting
            )
            
            # Test the connection
            try:
                me = self.client.get_me()
                logger.info(f"Twitter client initialized successfully for user: @{me.data.username}")
            except Exception as e:
                logger.warning(f"Twitter client initialized but authentication test failed: {e}")
                
        except Exception as e:
            logger.error(f"Failed to initialize Twitter client: {e}")
            self.client = None
            raise RuntimeError(f"Could not initialize Twitter client: {e}")

    def post_tweet(self, message: str, reply_to_username: Optional[str] = None) -> Dict[str, Any]:
        """
        Post a tweet using the Twitter API.
        
        Args:
            message (str): The tweet content (max 280 characters)
            reply_to_username (str, optional): Username to mention/reply to
            
        Returns:
            Dict[str, Any]: Dictionary containing tweet result and metadata
            
        Raises:
            RuntimeError: If Twitter client is not initialized or posting fails
            ValueError: If message is invalid
        """
        start_time = time.time()
        
        if not self.client:
            raise RuntimeError("Twitter client is not initialized. Please check your Twitter API credentials.")
        
        if not message or not message.strip():
            raise ValueError("Tweet message cannot be empty")
        
        # Clean and prepare the message
        message = message.strip()
        
        # Add mention if reply_to_username is provided
        if reply_to_username:
            # Ensure username starts with @
            if not reply_to_username.startswith('@'):
                reply_to_username = f"@{reply_to_username}"
            
            # Add mention at the beginning if not already present
            if reply_to_username.lower() not in message.lower():
                message = f"{reply_to_username} {message}"
        
        # Ensure message fits Twitter's character limit
        if len(message) > 280:
            logger.warning(f"Tweet message too long ({len(message)} chars), truncating to 280 characters")
            message = message[:277] + "..."
        
        try:
            logger.info(f"Posting tweet: {message[:50]}...")
            
            # Post the tweet
            response = self.client.create_tweet(text=message)
            
            processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            tweet_id = response.data['id']
            tweet_url = f"https://twitter.com/user/status/{tweet_id}"
            
            logger.info(f"Tweet posted successfully. ID: {tweet_id}, URL: {tweet_url}")
            
            return {
                "success": True,
                "tweet_id": tweet_id,
                "tweet_url": tweet_url,
                "message": message,
                "character_count": len(message),
                "processing_time_ms": processing_time,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except tweepy.TooManyRequests as e:
            logger.error(f"Twitter rate limit exceeded: {e}")
            return {
                "success": False,
                "error_code": "RATE_LIMIT_EXCEEDED",
                "error_message": "Twitter rate limit exceeded. Please try again later.",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except tweepy.Forbidden as e:
            logger.error(f"Twitter API forbidden error: {e}")
            return {
                "success": False,
                "error_code": "FORBIDDEN",
                "error_message": "Twitter API access forbidden. Please check your permissions.",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except tweepy.Unauthorized as e:
            logger.error(f"Twitter API unauthorized error: {e}")
            return {
                "success": False,
                "error_code": "UNAUTHORIZED",
                "error_message": "Twitter API authentication failed. Please check your credentials.",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Unexpected error posting tweet: {e}")
            return {
                "success": False,
                "error_code": "UNKNOWN_ERROR",
                "error_message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    def post_action_tweet(self, 
                         message: str, 
                         official_twitter_handle: Optional[str] = None,
                         campaign_hashtag: Optional[str] = None,
                         user_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Post an action tweet to an official with proper formatting.
        
        Args:
            message (str): The main message content
            official_twitter_handle (str, optional): Official's Twitter handle
            campaign_hashtag (str, optional): Campaign hashtag to include
            user_name (str, optional): Name of the person taking action
            
        Returns:
            Dict[str, Any]: Dictionary containing tweet result
        """
        # Build the tweet content
        tweet_parts = []
        
        # Add official mention if provided
        if official_twitter_handle:
            if not official_twitter_handle.startswith('@'):
                official_twitter_handle = f"@{official_twitter_handle}"
            tweet_parts.append(official_twitter_handle)
        
        # Add the main message (truncated if necessary to fit other elements)
        available_chars = 280
        if official_twitter_handle:
            available_chars -= len(official_twitter_handle) + 1  # +1 for space
        if campaign_hashtag:
            available_chars -= len(campaign_hashtag) + 1  # +1 for space
        if user_name:
            available_chars -= len(f" - {user_name}") + 1
        
        # Reserve some characters for spacing
        available_chars -= 5
        
        if len(message) > available_chars:
            message = message[:available_chars-3] + "..."
        
        tweet_parts.append(message)
        
        # Add campaign hashtag if provided
        if campaign_hashtag:
            if not campaign_hashtag.startswith('#'):
                campaign_hashtag = f"#{campaign_hashtag}"
            tweet_parts.append(campaign_hashtag)
        
        # Add user attribution if provided
        if user_name:
            tweet_parts.append(f"- {user_name}")
        
        # Join all parts
        tweet_content = " ".join(tweet_parts)
        
        return self.post_tweet(tweet_content)

    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the Twitter service.
        
        Returns:
            Dict[str, Any]: Dictionary containing health status
        """
        try:
            if not self.client:
                return {
                    "healthy": False,
                    "service": "twitter",
                    "error": "Twitter client not initialized",
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            # Test API connection by getting user info
            me = self.client.get_me()
            
            return {
                "healthy": True,
                "service": "twitter",
                "api_version": "v2",
                "authenticated_user": me.data.username if me.data else "unknown",
                "rate_limit_status": "ok",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except tweepy.TooManyRequests:
            return {
                "healthy": False,
                "service": "twitter",
                "error": "Rate limit exceeded",
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Twitter health check failed: {e}")
            return {
                "healthy": False,
                "service": "twitter",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    def is_available(self) -> bool:
        """
        Check if Twitter service is available and properly configured.
        
        Returns:
            bool: True if Twitter service is available, False otherwise
        """
        return self.client is not None


# Convenience functions for direct usage
def post_tweet(message: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to post a tweet.
    
    Args:
        message (str): Tweet content
        **kwargs: Additional arguments passed to TwitterService.post_tweet
        
    Returns:
        Dict[str, Any]: Dictionary containing tweet result
    """
    service = TwitterService()
    return service.post_tweet(message, **kwargs)


def post_action_tweet(message: str, official_twitter_handle: str = None, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to post an action tweet.
    
    Args:
        message (str): Tweet message
        official_twitter_handle (str): Official's Twitter handle
        **kwargs: Additional arguments
        
    Returns:
        Dict[str, Any]: Dictionary containing tweet result
    """
    service = TwitterService()
    return service.post_action_tweet(
        message=message,
        official_twitter_handle=official_twitter_handle,
        **kwargs
    )


def health_check() -> Dict[str, Any]:
    """
    Check Twitter service health.
    
    Returns:
        Dict[str, Any]: Dictionary containing health status
    """
    try:
        service = TwitterService()
        return service.health_check()
    except Exception as e:
        return {
            "healthy": False,
            "service": "twitter",
            "error": f"Failed to initialize service: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
