# app/services/email.py
"""
Email service for sending action emails via AWS SES.

This module provides functionality to send emails from constituents to 
government officials using Amazon Simple Email Service (SES).
"""

import logging
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from typing import Dict, Any, Optional
from datetime import datetime
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class EmailService:
    """Service for sending emails via AWS SES"""
    
    def __init__(self):
        """Initialize the email service with AWS SES client"""
        try:
            self.ses_client = boto3.client(
                'ses',
                region_name=settings.AWS_REGION or 'us-east-1',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            logger.info("SES client initialized successfully")
        except NoCredentialsError:
            logger.error("AWS credentials not found. Please configure AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize SES client: {str(e)}")
            raise

    def send_email(self, 
                   to_address: str, 
                   subject: str, 
                   body: str,
                   from_address: Optional[str] = None,
                   from_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Send an email via AWS SES.
        
        Args:
            to_address: Recipient email address
            subject: Email subject line
            body: Email body content (plain text)
            from_address: Sender email address (optional, uses default)
            from_name: Sender name (optional, uses default)
            
        Returns:
            Dict containing send result and message ID
            
        Raises:
            ClientError: If SES request fails
            Exception: For other errors
        """
        try:
            # Use default from address if not provided
            if not from_address:
                from_address = settings.AWS_SES_FROM_EMAIL or '<EMAIL>'
            
            if not from_name:
                from_name = settings.AWS_SES_FROM_NAME or 'ModernAction'
            
            # Format sender address
            sender = f"{from_name} <{from_address}>"
            
            logger.info(f"Sending email to {to_address} with subject: {subject[:50]}...")
            
            # Send email via SES
            response = self.ses_client.send_email(
                Source=sender,
                Destination={'ToAddresses': [to_address]},
                Message={
                    'Subject': {'Data': subject, 'Charset': 'UTF-8'},
                    'Body': {
                        'Text': {'Data': body, 'Charset': 'UTF-8'}
                    }
                }
            )
            
            message_id = response['MessageId']
            logger.info(f"Email sent successfully. Message ID: {message_id}")
            
            return {
                'success': True,
                'message_id': message_id,
                'timestamp': datetime.utcnow().isoformat(),
                'to_address': to_address,
                'subject': subject
            }
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            logger.error(f"SES ClientError ({error_code}): {error_message}")
            
            return {
                'success': False,
                'error_code': error_code,
                'error_message': error_message,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Unexpected error sending email: {str(e)}")
            return {
                'success': False,
                'error_code': 'UNKNOWN_ERROR',
                'error_message': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }

    def send_action_email(self,
                         to_address: str,
                         user_name: str,
                         user_email: str,
                         user_address: Optional[str],
                         user_zip_code: Optional[str],
                         subject: str,
                         message: str,
                         official_name: str) -> Dict[str, Any]:
        """
        Send an action email from a constituent to an official.
        
        Args:
            to_address: Official's email address
            user_name: Name of the constituent
            user_email: Email of the constituent
            user_address: Address of the constituent (optional)
            user_zip_code: Zip code of the constituent (optional)
            subject: Subject line for the email
            message: Main message content
            official_name: Name of the official
            
        Returns:
            Dict containing send result
        """
        # Format the email body with proper structure
        email_body = self._format_action_email_body(
            message=message,
            user_name=user_name,
            user_email=user_email,
            user_address=user_address,
            user_zip_code=user_zip_code,
            official_name=official_name
        )
        
        return self.send_email(
            to_address=to_address,
            subject=subject,
            body=email_body,
            from_address=settings.AWS_SES_FROM_EMAIL,
            from_name=user_name  # Send from the constituent's name
        )

    def _format_action_email_body(self,
                                 message: str,
                                 user_name: str,
                                 user_email: str,
                                 user_address: Optional[str],
                                 user_zip_code: Optional[str],
                                 official_name: str) -> str:
        """
        Format the action email body with proper structure.
        
        Args:
            message: Main message content
            user_name: Name of the constituent
            user_email: Email of the constituent
            user_address: Address of the constituent
            user_zip_code: Zip code of the constituent
            official_name: Name of the official
            
        Returns:
            Formatted email body
        """
        # Build address line
        address_parts = []
        if user_address:
            address_parts.append(user_address)
        if user_zip_code:
            address_parts.append(user_zip_code)
        
        address_line = ', '.join(address_parts) if address_parts else ''
        
        # Format the email body
        body = f"""Dear {official_name},

{message}

Sincerely,
{user_name}"""
        
        # Add contact information
        if user_email:
            body += f"\n{user_email}"
        
        if address_line:
            body += f"\n{address_line}"
        
        # Add footer
        body += f"""

---
This message was sent through ModernAction.io on behalf of your constituent.
For questions about this platform, <NAME_EMAIL>.
"""
        
        return body

    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the email service.
        
        Returns:
            Dict containing health status
        """
        try:
            # Check SES sending quota
            quota_response = self.ses_client.get_send_quota()
            
            # Check if account is in sandbox mode
            sending_enabled = self.ses_client.get_account_sending_enabled()
            
            return {
                'healthy': True,
                'service': 'email',
                'provider': 'aws_ses',
                'quota': {
                    'max_24_hour': quota_response['Max24HourSend'],
                    'max_send_rate': quota_response['MaxSendRate'],
                    'sent_last_24_hours': quota_response['SentLast24Hours']
                },
                'sending_enabled': sending_enabled['Enabled'],
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except ClientError as e:
            logger.error(f"SES health check failed: {e}")
            return {
                'healthy': False,
                'service': 'email',
                'provider': 'aws_ses',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Email service health check failed: {e}")
            return {
                'healthy': False,
                'service': 'email',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }

    def get_send_statistics(self) -> Dict[str, Any]:
        """
        Get sending statistics from SES.
        
        Returns:
            Dict containing send statistics
        """
        try:
            stats = self.ses_client.get_send_statistics()
            return {
                'success': True,
                'statistics': stats['SendDataPoints'],
                'timestamp': datetime.utcnow().isoformat()
            }
        except ClientError as e:
            logger.error(f"Failed to get send statistics: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Convenience functions for direct usage
def send_email(to_address: str, subject: str, body: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to send an email.
    
    Args:
        to_address: Recipient email address
        subject: Email subject
        body: Email body content
        **kwargs: Additional arguments passed to EmailService.send_email
        
    Returns:
        Dict containing send result
    """
    service = EmailService()
    return service.send_email(to_address, subject, body, **kwargs)


def send_action_email(to_address: str, user_name: str, user_email: str, 
                     subject: str, message: str, official_name: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to send an action email.
    
    Args:
        to_address: Official's email address
        user_name: Constituent's name
        user_email: Constituent's email
        subject: Email subject
        message: Email message
        official_name: Official's name
        **kwargs: Additional arguments
        
    Returns:
        Dict containing send result
    """
    service = EmailService()
    return service.send_action_email(
        to_address=to_address,
        user_name=user_name,
        user_email=user_email,
        subject=subject,
        message=message,
        official_name=official_name,
        **kwargs
    )


def health_check() -> Dict[str, Any]:
    """
    Check email service health.
    
    Returns:
        Dict containing health status
    """
    service = EmailService()
    return service.health_check()