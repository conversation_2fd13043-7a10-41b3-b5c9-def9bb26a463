# app/api/v1/endpoints/bills.py
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.bills import BillService
from app.tasks import task_generate_summary_for_bill, task_regenerate_summary_for_bill
from app.schemas.bill import (
    Bill,
    BillResponse, 
    BillCreate,
    BillUpdate
)
from app.models.bill import BillStatus, BillType

router = APIRouter()

@router.get("/", response_model=List[BillResponse])
def get_bills(
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get a list of bills with pagination.
    
    Returns a paginated list of bills ordered by creation date (newest first).
    """
    service = BillService(db)
    bills = service.get_bills(skip=skip, limit=limit)
    return bills

@router.get("/search", response_model=List[BillResponse])
def search_bills(
    query: Optional[str] = Query(None, description="Search term for title, description, summary, or bill number"),
    bill_type: Optional[BillType] = Query(None, description="Type of bill (house_bill, senate_bill, etc.)"),
    status: Optional[BillStatus] = Query(None, description="Bill status (introduced, committee, passed, etc.)"),
    session_year: Optional[int] = Query(None, description="Legislative session year"),
    chamber: Optional[str] = Query(None, description="Chamber (house, senate)"),
    state: Optional[str] = Query(None, description="State abbreviation or 'federal'"),
    sponsor_name: Optional[str] = Query(None, description="Sponsor name (partial match)"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    skip: int = Query(0, ge=0, description="Number of results to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results to return"),
    db: Session = Depends(get_db)
):
    """
    Search bills based on various criteria.
    
    Supports text search across title, description, summary, and bill number.
    Results are ordered by priority score and creation date.
    """
    service = BillService(db)
    bills = service.search_bills(
        query=query,
        bill_type=bill_type,
        status=status,
        session_year=session_year,
        chamber=chamber,
        state=state,
        sponsor_name=sponsor_name,
        is_featured=is_featured,
        skip=skip,
        limit=limit
    )
    return bills

@router.get("/featured", response_model=List[BillResponse])
def get_featured_bills(
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get featured bills.
    
    Returns bills marked as featured, ordered by priority score.
    """
    service = BillService(db)
    bills = service.get_featured_bills(skip=skip, limit=limit)
    return bills

@router.get("/status/{status}", response_model=List[BillResponse])
def get_bills_by_status(
    status: BillStatus,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by status.
    
    Returns bills with the specified status (introduced, committee, passed, etc.).
    """
    service = BillService(db)
    bills = service.get_bills_by_status(status, skip=skip, limit=limit)
    return bills

@router.get("/type/{bill_type}", response_model=List[BillResponse])
def get_bills_by_type(
    bill_type: BillType,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by type.
    
    Returns bills of the specified type (house_bill, senate_bill, etc.).
    """
    service = BillService(db)
    bills = service.get_bills_by_type(bill_type, skip=skip, limit=limit)
    return bills

@router.get("/session/{session_year}", response_model=List[BillResponse])
def get_bills_by_session_year(
    session_year: int,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by session year.

    Returns bills from the specified legislative session year.
    """
    service = BillService(db)
    bills = service.get_bills_by_session_year(session_year, skip=skip, limit=limit)
    return bills

@router.get("/sponsor/{sponsor_name}", response_model=List[BillResponse])
def get_bills_by_sponsor(
    sponsor_name: str,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by sponsor name.

    Returns bills sponsored by the specified legislator (partial name match).
    """
    service = BillService(db)
    bills = service.get_bills_by_sponsor(sponsor_name, skip=skip, limit=limit)
    return bills

@router.get("/external/{id_type}/{external_id:path}", response_model=BillResponse)
def get_bill_by_external_id(
    id_type: str,
    external_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a bill by external ID.

    Supported ID types:
    - openstates: Open States API ID
    - congress_gov: Congress.gov ID
    """
    valid_id_types = ["openstates", "congress_gov"]
    if id_type not in valid_id_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid ID type. Must be one of: {', '.join(valid_id_types)}"
        )

    service = BillService(db)
    bill = service.get_bill_by_external_id(external_id, id_type)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    return bill

@router.get("/{bill_id}", response_model=BillResponse)
def get_bill(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific bill.

    Returns complete bill information including status, sponsors, and metadata.
    """
    service = BillService(db)
    bill = service.get_bill(bill_id)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    return bill

@router.post("/", response_model=BillResponse, status_code=201)
def create_bill(
    bill_data: BillCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Create a new bill record with asynchronous AI summarization.

    This endpoint creates a bill and schedules AI summary generation in the background
    to ensure fast API responses. The AI summary will be available shortly after
    the bill is created.
    
    This endpoint is typically used for data ingestion from external APIs
    or manual data entry by administrators.
    """
    service = BillService(db)

    # Check if bill already exists by external ID
    if hasattr(bill_data, 'openstates_id') and bill_data.openstates_id:
        existing = service.get_bill_by_external_id(bill_data.openstates_id, "openstates")
        if existing:
            raise HTTPException(status_code=400, detail="Bill with this openstates_id already exists")

    if hasattr(bill_data, 'congress_gov_id') and bill_data.congress_gov_id:
        existing = service.get_bill_by_external_id(bill_data.congress_gov_id, "congress_gov")
        if existing:
            raise HTTPException(status_code=400, detail="Bill with this congress_gov_id already exists")

    # Create the bill
    bill = service.create_bill(bill_data)
    
    # Schedule AI summary generation as a background task
    # This runs after the response is sent to the client
    background_tasks.add_task(
        task_generate_summary_for_bill,
        bill.id,
        db
    )
    
    return bill

@router.put("/{bill_id}", response_model=BillResponse)
def update_bill(
    bill_id: str,
    bill_data: BillUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing bill.

    Updates only the provided fields, leaving others unchanged.
    """
    service = BillService(db)
    bill = service.update_bill(bill_id, bill_data)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    return bill

@router.delete("/{bill_id}")
def delete_bill(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete a bill.

    Permanently removes the bill from the database.
    """
    service = BillService(db)
    success = service.delete_bill(bill_id)

    if not success:
        raise HTTPException(status_code=404, detail="Bill not found")

    return {"message": "Bill deleted successfully"}

@router.get("/stats/count")
def get_bills_count(db: Session = Depends(get_db)):
    """
    Get total count of bills in the database.

    Returns the total number of bills for statistics purposes.
    """
    service = BillService(db)
    count = service.get_bills_count()
    return {"total_bills": count}

@router.post("/{bill_id}/regenerate-summary")
def regenerate_ai_summary(
    bill_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Regenerate AI summary for an existing bill.
    
    This endpoint schedules regeneration of the AI summary for a specific bill.
    Useful when the AI model has been updated or the bill text has changed.
    """
    service = BillService(db)
    
    # Check if bill exists
    bill = service.get_bill(bill_id)
    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")
    
    # Schedule AI summary regeneration as a background task
    background_tasks.add_task(
        task_regenerate_summary_for_bill,
        bill.id,
        db
    )
    
    return {"message": "AI summary regeneration scheduled", "bill_id": bill_id}

@router.get("/{bill_id}/ai-summary-status")
def get_ai_summary_status(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get the AI summary status for a specific bill.
    
    Returns information about whether the bill has an AI summary
    and when it was last updated.
    """
    service = BillService(db)
    
    # Check if bill exists
    bill = service.get_bill(bill_id)
    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")
    
    has_ai_summary = bool(bill.ai_summary and bill.ai_summary.strip())
    has_full_text = bool(bill.full_text and bill.full_text.strip())
    
    return {
        "bill_id": bill_id,
        "has_ai_summary": has_ai_summary,
        "has_full_text": has_full_text,
        "ai_summary_length": len(bill.ai_summary) if bill.ai_summary else 0,
        "full_text_length": len(bill.full_text) if bill.full_text else 0,
        "ai_summary_preview": bill.ai_summary[:100] if bill.ai_summary else None,
        "can_generate_summary": has_full_text and not has_ai_summary,
        "last_updated": bill.updated_at.isoformat() if bill.updated_at else None
    }
