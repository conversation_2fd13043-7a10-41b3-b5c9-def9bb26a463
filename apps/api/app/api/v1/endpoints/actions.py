# app/api/v1/endpoints/actions.py
"""
Actions API endpoints for the ModernAction platform.

This module provides REST API endpoints for managing user actions (emails, calls, etc.)
sent to government officials through campaigns.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.action import ActionService
from app.schemas.action import (
    ActionCreate, 
    ActionResponse, 
    ActionSummary, 
    ActionSearch,
    ActionStats,
    ActionWithDetails
)
from app.models.action import ActionStatus, ActionType
from datetime import datetime

router = APIRouter()

@router.post("/", response_model=ActionResponse, status_code=202)
def create_action(
    action_data: ActionCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Create a new action and queue it for processing.
    
    This endpoint creates an action record in the database and schedules
    the actual sending (email, etc.) as a background task to ensure
    fast API responses.
    
    Returns 202 Accepted to indicate the request has been received
    and is being processed asynchronously.
    """
    try:
        service = ActionService(db)
        
        # Create the action record
        action = service.create_action(action_data)
        
        # Import the background task function here to avoid circular imports
        from app.tasks import task_send_action_email
        
        # Schedule the email sending as a background task
        # This runs after the response is sent to the client
        background_tasks.add_task(
            task_send_action_email,
            action.id,
            db
        )
        
        return action
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to create action")

@router.get("/", response_model=List[ActionSummary])
def get_actions(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    campaign_id: Optional[str] = Query(None, description="Filter by campaign ID"),
    official_id: Optional[str] = Query(None, description="Filter by official ID"),
    status: Optional[ActionStatus] = Query(None, description="Filter by action status"),
    action_type: Optional[ActionType] = Query(None, description="Filter by action type"),
    date_from: Optional[datetime] = Query(None, description="Filter actions from this date"),
    date_to: Optional[datetime] = Query(None, description="Filter actions to this date"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of actions to return"),
    offset: int = Query(0, ge=0, description="Number of actions to skip"),
    db: Session = Depends(get_db)
):
    """
    Get a list of actions with optional filtering.
    
    Returns a paginated list of action summaries ordered by creation date (newest first).
    """
    service = ActionService(db)
    
    search_params = ActionSearch(
        user_id=user_id,
        campaign_id=campaign_id,
        official_id=official_id,
        status=status,
        action_type=action_type,
        date_from=date_from,
        date_to=date_to,
        limit=limit,
        offset=offset
    )
    
    actions = service.get_actions(search_params)
    return actions

@router.get("/stats", response_model=ActionStats)
def get_action_stats(
    campaign_id: Optional[str] = Query(None, description="Filter stats by campaign ID"),
    db: Session = Depends(get_db)
):
    """
    Get action statistics.
    
    Returns statistics about actions including success rates, counts by status/type,
    and average response times.
    """
    service = ActionService(db)
    stats = service.get_action_stats(campaign_id)
    return stats

@router.get("/{action_id}", response_model=ActionWithDetails)
def get_action(
    action_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific action.
    
    Returns complete action information including related campaign,
    official, and bill details.
    """
    service = ActionService(db)
    action = service.get_action_with_details(action_id)
    
    if not action:
        raise HTTPException(status_code=404, detail="Action not found")
    
    return action

@router.post("/{action_id}/retry", response_model=ActionResponse)
def retry_action(
    action_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Retry a failed action.
    
    This endpoint allows retrying actions that failed to send.
    The action must be in a failed state and have retry attempts remaining.
    """
    service = ActionService(db)
    
    # Attempt to retry the action
    action = service.retry_failed_action(action_id)
    
    if not action:
        raise HTTPException(
            status_code=404, 
            detail="Action not found or cannot be retried"
        )
    
    # Schedule the retry as a background task
    from app.tasks import task_send_action_email
    
    background_tasks.add_task(
        task_send_action_email,
        action.id,
        db
    )
    
    return action

@router.delete("/{action_id}")
def delete_action(
    action_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete an action.
    
    Permanently removes the action from the database.
    Note: This should be used carefully as it affects campaign statistics.
    """
    service = ActionService(db)
    success = service.delete_action(action_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Action not found")
    
    return {"message": "Action deleted successfully"}

@router.get("/campaign/{campaign_id}/stats", response_model=ActionStats)
def get_campaign_action_stats(
    campaign_id: str,
    db: Session = Depends(get_db)
):
    """
    Get action statistics for a specific campaign.
    
    Returns detailed statistics about actions taken for a particular campaign.
    """
    service = ActionService(db)
    stats = service.get_action_stats(campaign_id)
    return stats

@router.get("/user/{user_id}/actions", response_model=List[ActionSummary])
def get_user_actions(
    user_id: str,
    limit: int = Query(20, ge=1, le=100, description="Maximum number of actions to return"),
    offset: int = Query(0, ge=0, description="Number of actions to skip"),
    db: Session = Depends(get_db)
):
    """
    Get all actions taken by a specific user.
    
    Returns a paginated list of actions taken by the user,
    ordered by creation date (newest first).
    """
    service = ActionService(db)
    
    search_params = ActionSearch(
        user_id=user_id,
        limit=limit,
        offset=offset
    )
    
    actions = service.get_actions(search_params)
    return actions