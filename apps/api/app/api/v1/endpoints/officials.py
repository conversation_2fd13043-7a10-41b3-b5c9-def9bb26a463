# app/api/v1/endpoints/officials.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.officials import OfficialService
from app.schemas.official import (
    Official,
    OfficialResponse, 
    OfficialSummary,
    OfficialCreate,
    OfficialUpdate,
    OfficialSearch,
    OfficialContact
)
from app.models.official import OfficialLevel, OfficialChamber

router = APIRouter()

@router.get("/", response_model=List[OfficialSummary])
def get_officials(
    skip: int = Query(0, ge=0, description="Number of officials to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of officials to return"),
    db: Session = Depends(get_db)
):
    """
    Get a list of officials with pagination.
    
    Returns a paginated list of officials with basic information.
    """
    service = OfficialService(db)
    officials = service.get_officials(skip=skip, limit=limit)
    return officials

@router.get("/search", response_model=List[OfficialSummary])
def search_officials(
    query: str = Query(None, description="Search term for name or title"),
    level: OfficialLevel = Query(None, description="Government level (federal, state, local)"),
    chamber: OfficialChamber = Query(None, description="Chamber (house, senate, executive, etc.)"),
    state: str = Query(None, description="State abbreviation"),
    district: str = Query(None, description="District number or name"),
    party: str = Query(None, description="Political party"),
    skip: int = Query(0, ge=0, description="Number of results to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results to return"),
    db: Session = Depends(get_db)
):
    """
    Search for officials based on various criteria.
    
    Supports searching by name, title, level, chamber, state, district, and party.
    """
    search_params = OfficialSearch(
        query=query,
        level=level,
        chamber=chamber,
        state=state,
        district=district,
        party=party,
        offset=skip,
        limit=limit
    )
    
    service = OfficialService(db)
    officials = service.search_officials(search_params)
    return officials

@router.get("/by-zip/{zip_code}", response_model=List[OfficialContact])
def get_officials_by_zip_code(
    zip_code: str,
    db: Session = Depends(get_db)
):
    """
    Get officials representing a specific zip code area.
    
    Returns federal officials and relevant state/local officials for the given zip code.
    This is the primary endpoint for users to find their representatives.
    """
    if len(zip_code) < 5:
        raise HTTPException(status_code=400, detail="Invalid zip code format")
        
    service = OfficialService(db)
    officials = service.get_officials_by_zip_code(zip_code)
    
    # Convert to contact format for easier consumption
    return [
        OfficialContact(
            id=official.id,
            name=official.name,
            title=official.title,
            email=official.email,
            phone=official.phone,
            website=official.website,
            office_address=official.office_address,
            preferred_contact_method=official.contact_preference
        )
        for official in officials
    ]

@router.get("/levels/{level}", response_model=List[OfficialSummary])
def get_officials_by_level(
    level: OfficialLevel,
    db: Session = Depends(get_db)
):
    """
    Get all officials at a specific government level.
    
    - federal: Federal officials (Congress, President, etc.)
    - state: State-level officials (Governor, State Legislature, etc.)
    - local: Local officials (Mayor, City Council, etc.)
    """
    service = OfficialService(db)
    officials = service.get_officials_by_level(level)
    return officials

@router.get("/chambers/{chamber}", response_model=List[OfficialSummary])
def get_officials_by_chamber(
    chamber: OfficialChamber,
    db: Session = Depends(get_db)
):
    """
    Get all officials in a specific chamber.
    
    - house: House of Representatives
    - senate: Senate
    - executive: Executive branch
    - judicial: Judicial branch
    - other: Other positions
    """
    service = OfficialService(db)
    officials = service.get_officials_by_chamber(chamber)
    return officials

@router.get("/{official_id}", response_model=OfficialResponse)
def get_official(
    official_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific official.
    
    Returns complete official information including contact details,
    bio, social media, and engagement metrics.
    """
    service = OfficialService(db)
    official = service.get_official(official_id)
    
    if not official:
        raise HTTPException(status_code=404, detail="Official not found")
        
    return official

@router.post("/", response_model=OfficialResponse)
def create_official(
    official_data: OfficialCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new official record.
    
    This endpoint is typically used for data ingestion from external APIs
    or manual data entry by administrators.
    """
    service = OfficialService(db)
    
    # Check if official already exists by external ID
    if official_data.bioguide_id:
        existing = service.get_officials_by_external_id(official_data.bioguide_id, "bioguide")
        if existing:
            raise HTTPException(status_code=400, detail="Official with this bioguide_id already exists")
    
    if official_data.openstates_id:
        existing = service.get_officials_by_external_id(official_data.openstates_id, "openstates")
        if existing:
            raise HTTPException(status_code=400, detail="Official with this openstates_id already exists")
    
    official = service.create_official(official_data)
    return official

@router.put("/{official_id}", response_model=OfficialResponse)
def update_official(
    official_id: str,
    official_data: OfficialUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing official's information.
    
    Only provided fields will be updated. Other fields will remain unchanged.
    """
    service = OfficialService(db)
    official = service.update_official(official_id, official_data)
    
    if not official:
        raise HTTPException(status_code=404, detail="Official not found")
        
    return official

@router.delete("/{official_id}")
def delete_official(
    official_id: str,
    db: Session = Depends(get_db)
):
    """
    Soft delete an official (sets is_active to False).
    
    This preserves historical data while removing the official from active lists.
    """
    service = OfficialService(db)
    success = service.delete_official(official_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Official not found")
        
    return {"message": "Official deleted successfully"}

@router.get("/external/{id_type}/{external_id:path}", response_model=OfficialResponse)
def get_official_by_external_id(
    id_type: str,
    external_id: str,
    db: Session = Depends(get_db)
):
    """
    Get an official by external ID.
    
    Supported ID types:
    - bioguide: Congressional Bioguide ID
    - openstates: Open States API ID  
    - google_civic: Google Civic Information API ID
    """
    valid_id_types = ["bioguide", "openstates", "google_civic"]
    if id_type not in valid_id_types:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid ID type. Must be one of: {', '.join(valid_id_types)}"
        )
    
    service = OfficialService(db)
    official = service.get_officials_by_external_id(external_id, id_type)
    
    if not official:
        raise HTTPException(status_code=404, detail="Official not found")
        
    return official