# app/api/v1/endpoints/ai.py
"""
AI service API endpoints for the ModernAction platform.

This module provides REST API endpoints for AI-powered features including
message personalization, text generation, and AI service health monitoring.
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from app.schemas.ai import (
    PersonalizeMessageRequest,
    PersonalizeMessageResponse,
    AIHealthResponse,
    TextGenerationRequest,
    TextGenerationResponse
)
from app.services.ai import personalize_message, health_check as ai_health_check
from app.core.config import get_settings
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()


@router.post("/personalize-message", response_model=PersonalizeMessageResponse)
def personalize_user_message(request: PersonalizeMessageRequest) -> PersonalizeMessageResponse:
    """
    Personalize a user's message using AI to make it more persuasive and well-structured.
    
    This endpoint takes a user's raw message and campaign context, then uses AI
    to transform it into a more effective advocacy message while preserving
    the personal elements that make it authentic.
    
    Args:
        request: PersonalizeMessageRequest containing raw_text, context, and optional tone
        
    Returns:
        PersonalizeMessageResponse: The personalized message with metadata
        
    Raises:
        HTTPException: 400 for invalid input, 500 for AI service errors
    """
    try:
        logger.info(f"Personalizing message: {len(request.raw_text)} chars, context: {request.context[:50]}...")
        
        # Call the AI service
        result = personalize_message(
            raw_text=request.raw_text,
            context=request.context,
            tone=request.tone or "professional"
        )
        
        # Create response
        response = PersonalizeMessageResponse(
            personalized_message=result["personalized_message"],
            original_length=result["original_length"],
            personalized_length=result["personalized_length"],
            processing_time_ms=result["processing_time_ms"]
        )
        
        logger.info(f"Message personalization successful: {response.personalized_length} chars in {response.processing_time_ms:.1f}ms")
        return response
        
    except ValueError as e:
        logger.warning(f"Invalid input for message personalization: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except RuntimeError as e:
        logger.error(f"AI service error during message personalization: {e}")
        raise HTTPException(status_code=500, detail="AI service temporarily unavailable")
    except Exception as e:
        logger.error(f"Unexpected error during message personalization: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health", response_model=AIHealthResponse)
def get_ai_health() -> AIHealthResponse:
    """
    Get the health status of the AI service.
    
    This endpoint provides comprehensive information about the AI service status,
    including model loading status, available capabilities, and performance metrics.
    
    Returns:
        AIHealthResponse: Detailed health information about the AI service
    """
    try:
        logger.info("Checking AI service health...")
        
        # Get health status from AI service
        health_data = ai_health_check()
        
        # Create response
        response = AIHealthResponse(
            status=health_data["status"],
            model_loaded=health_data["model_loaded"],
            capabilities=health_data.get("capabilities", []),
            model_info=health_data["model_info"]
        )
        
        logger.info(f"AI health check completed: {response.status}")
        return response
        
    except Exception as e:
        logger.error(f"AI health check failed: {e}")
        # Return unhealthy status instead of raising exception
        return AIHealthResponse(
            status="unhealthy",
            model_loaded=False,
            capabilities=[],
            model_info={"error": str(e)}
        )


@router.post("/generate-text", response_model=TextGenerationResponse)
def generate_text(request: TextGenerationRequest) -> TextGenerationResponse:
    """
    Generate text based on a prompt using AI.
    
    This endpoint provides general text generation capabilities that can be used
    for various purposes including content creation and message enhancement.
    
    Args:
        request: TextGenerationRequest containing prompt and generation parameters
        
    Returns:
        TextGenerationResponse: The generated text with metadata
        
    Raises:
        HTTPException: 400 for invalid input, 500 for AI service errors
    """
    try:
        logger.info(f"Generating text from prompt: {len(request.prompt)} chars")
        
        # For now, use the personalization function with a generic context
        # In a full implementation, you might want a dedicated text generation function
        result = personalize_message(
            raw_text=request.prompt,
            context="General text generation request",
            tone="professional"
        )
        
        # Create response
        response = TextGenerationResponse(
            generated_text=result["personalized_message"],
            prompt_length=len(request.prompt),
            generated_length=result["personalized_length"],
            processing_time_ms=result["processing_time_ms"]
        )
        
        logger.info(f"Text generation successful: {response.generated_length} chars in {response.processing_time_ms:.1f}ms")
        return response
        
    except ValueError as e:
        logger.warning(f"Invalid input for text generation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except RuntimeError as e:
        logger.error(f"AI service error during text generation: {e}")
        raise HTTPException(status_code=500, detail="AI service temporarily unavailable")
    except Exception as e:
        logger.error(f"Unexpected error during text generation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/capabilities")
def get_ai_capabilities() -> Dict[str, Any]:
    """
    Get information about available AI capabilities and models.
    
    Returns:
        Dict containing information about AI service capabilities
    """
    try:
        health_data = ai_health_check()
        
        return {
            "service": "ModernAction AI Service",
            "version": "1.0.0",
            "status": health_data["status"],
            "capabilities": health_data.get("capabilities", []),
            "models": {
                "summarization": health_data["model_info"].get("model_name", "unknown"),
                "text_generation": health_data["model_info"].get("model_name", "unknown")
            },
            "features": {
                "message_personalization": {
                    "description": "Transform user messages into persuasive advocacy letters",
                    "supported_tones": ["professional", "passionate", "formal", "personal", "urgent"],
                    "max_input_length": 1000,
                    "max_output_length": 500
                },
                "bill_summarization": {
                    "description": "Generate citizen-friendly summaries of legislative text",
                    "max_input_length": 1024,
                    "typical_output_length": "150-200 characters"
                },
                "text_generation": {
                    "description": "Generate text based on prompts",
                    "max_input_length": 500,
                    "max_output_length": 500
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get AI capabilities: {e}")
        return {
            "service": "ModernAction AI Service",
            "version": "1.0.0",
            "status": "error",
            "error": str(e),
            "capabilities": [],
            "models": {},
            "features": {}
        }


@router.get("/models/info")
def get_model_info() -> Dict[str, Any]:
    """
    Get detailed information about loaded AI models.
    
    Returns:
        Dict containing detailed model information
    """
    try:
        from app.services.ai import get_model_info
        
        model_info = get_model_info()
        health_data = ai_health_check()
        
        return {
            "models": {
                "summarization": model_info,
                "text_generation": {
                    "model_name": model_info.get("model_name", "t5-small"),
                    "status": "loaded" if health_data["model_loaded"] else "failed",
                    "framework": "transformers",
                    "device": "cpu",
                    "task": "text2text-generation"
                }
            },
            "performance": {
                "model_load_time": "5-10 seconds (one-time)",
                "inference_time": "1-3 seconds per request",
                "memory_usage": "~300MB RAM",
                "concurrent_requests": "Limited by CPU cores"
            },
            "limitations": {
                "max_input_tokens": 512,
                "max_output_tokens": 300,
                "supported_languages": ["English"],
                "device": "CPU only (no GPU acceleration)"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        return {
            "error": str(e),
            "models": {},
            "performance": {},
            "limitations": {}
        }
