# app/db/base_class.py
from sqlalchemy.ext.declarative import as_declarative, declared_attr
import uuid
from sqlalchemy import Column, DateTime, func
from typing import Any
from app.db.types import get_uuid_type

@as_declarative()
class Base:
    __name__: str
    
    # Generate table name automatically
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower() + "s"
    
    # Common columns for all tables
    id = Column(get_uuid_type(), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)