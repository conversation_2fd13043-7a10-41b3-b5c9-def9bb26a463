# app/tasks.py
"""
Background tasks for the ModernAction API.

This module contains all background task functions that are executed
asynchronously to avoid blocking API responses.
"""

import logging
from uuid import UUID
from sqlalchemy.orm import Session
from app.models.bill import Bill
from app.services.ai import summarize_bill

logger = logging.getLogger(__name__)

def task_generate_summary_for_bill(bill_id: UUID, db: Session) -> None:
    """
    Background task to generate AI summary for a bill.
    
    This function runs in the background after a bill is created via the API.
    It fetches the bill, generates an AI summary, and updates the database.
    
    Args:
        bill_id: UUID of the bill to process
        db: Database session
    """
    try:
        logger.info(f"Starting background task: Generate summary for bill {bill_id}")
        
        # Fetch the bill from the database
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        
        if not bill:
            logger.error(f"Bill {bill_id} not found in database")
            return
        
        # Check if bill already has an AI summary
        if bill.ai_summary and bill.ai_summary.strip():
            logger.info(f"Bill {bill_id} already has AI summary, skipping")
            return
        
        # Check if bill has full text for summarization
        if not bill.full_text or not bill.full_text.strip():
            logger.warning(f"Bill {bill_id} has no full text, cannot generate summary")
            return
        
        # Generate AI summary
        logger.info(f"Generating AI summary for bill {bill_id}: {bill.title[:50]}...")
        
        summary = summarize_bill(
            bill_text=bill.full_text, 
            title=bill.title
        )
        
        # Update the bill with the generated summary
        bill.ai_summary = summary
        db.commit()
        
        logger.info(f"Successfully generated and saved AI summary for bill {bill_id}")
        logger.debug(f"Generated summary: {summary[:100]}...")
        
    except Exception as e:
        logger.error(f"Background task failed for bill {bill_id}: {str(e)}")
        db.rollback()
        # Don't re-raise - background tasks should handle errors gracefully

def task_regenerate_summary_for_bill(bill_id: UUID, db: Session) -> None:
    """
    Background task to regenerate AI summary for an existing bill.
    
    This function is similar to task_generate_summary_for_bill but will
    overwrite existing summaries. Useful for updating summaries when
    the AI model or bill text changes.
    
    Args:
        bill_id: UUID of the bill to process
        db: Database session
    """
    try:
        logger.info(f"Starting background task: Regenerate summary for bill {bill_id}")
        
        # Fetch the bill from the database
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        
        if not bill:
            logger.error(f"Bill {bill_id} not found in database")
            return
        
        # Check if bill has full text for summarization
        if not bill.full_text or not bill.full_text.strip():
            logger.warning(f"Bill {bill_id} has no full text, cannot regenerate summary")
            return
        
        # Generate AI summary (overwrite existing)
        logger.info(f"Regenerating AI summary for bill {bill_id}: {bill.title[:50]}...")
        
        summary = summarize_bill(
            bill_text=bill.full_text, 
            title=bill.title
        )
        
        # Update the bill with the new summary
        old_summary = bill.ai_summary
        bill.ai_summary = summary
        db.commit()
        
        logger.info(f"Successfully regenerated AI summary for bill {bill_id}")
        logger.debug(f"Old summary: {old_summary[:50] if old_summary else 'None'}...")
        logger.debug(f"New summary: {summary[:100]}...")
        
    except Exception as e:
        logger.error(f"Background task failed for bill {bill_id}: {str(e)}")
        db.rollback()
        # Don't re-raise - background tasks should handle errors gracefully

def task_bulk_generate_summaries(bill_ids: list[UUID], db: Session) -> None:
    """
    Background task to generate AI summaries for multiple bills.
    
    This function processes multiple bills in sequence. It's useful for
    bulk operations without blocking the API.
    
    Args:
        bill_ids: List of bill UUIDs to process
        db: Database session
    """
    logger.info(f"Starting bulk summary generation for {len(bill_ids)} bills")
    
    successful = 0
    failed = 0
    
    for bill_id in bill_ids:
        try:
            task_generate_summary_for_bill(bill_id, db)
            successful += 1
        except Exception as e:
            logger.error(f"Failed to process bill {bill_id} in bulk operation: {e}")
            failed += 1
    
    logger.info(f"Bulk summary generation complete: {successful} successful, {failed} failed")

def task_send_action_email(action_id: UUID, db: Session) -> None:
    """
    Background task to send action email to officials.
    
    This function runs in the background after an action is created via the API.
    It fetches the action details and sends the email to the official.
    
    Args:
        action_id: UUID of the action to process
        db: Database session
    """
    from app.models.action import Action, ActionStatus
    from app.services.action import ActionService
    
    try:
        logger.info(f"Starting background task: Send action email for action {action_id}")
        
        # Fetch the action from the database
        action = db.query(Action).filter(Action.id == action_id).first()
        
        if not action:
            logger.error(f"Action {action_id} not found in database")
            return
        
        # Check if action is in pending status
        if action.status != ActionStatus.PENDING:
            logger.info(f"Action {action_id} is not in pending status, skipping")
            return
        
        # Check if we have contact information
        if not action.contact_email:
            logger.warning(f"Action {action_id} has no contact email, cannot send")
            service = ActionService(db)
            from app.schemas.action import ActionUpdate
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message="No contact email available"
            ))
            return
        
        # Send email using the email service
        logger.info(f"Sending email for action {action_id} to {action.contact_email}")
        logger.info(f"Subject: {action.subject}")
        logger.info(f"Message preview: {action.message[:100]}...")
        
        # Import email service here to avoid circular imports
        from app.services.email import EmailService
        
        email_service = EmailService()
        
        # Send the action email
        email_result = email_service.send_action_email(
            to_address=action.contact_email,
            user_name=action.user_name,
            user_email=action.user_email,
            user_address=action.user_address,
            user_zip_code=action.user_zip_code,
            subject=action.subject,
            message=action.message,
            official_name=action.official.name
        )
        
        # Update action status based on email result
        service = ActionService(db)
        from app.schemas.action import ActionUpdate
        
        if email_result['success']:
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.SENT,
                delivery_method='ses',
                delivery_id=email_result.get('message_id')
            ))
            logger.info(f"Successfully sent email for action {action_id}. Message ID: {email_result.get('message_id')}")
        else:
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message=email_result.get('error_message', 'Email sending failed'),
                delivery_method='ses'
            ))
            logger.error(f"Failed to send email for action {action_id}: {email_result.get('error_message')}")
            return  # Exit early on failure
        
        logger.info(f"Successfully processed action {action_id}")
        
    except Exception as e:
        logger.error(f"Background task failed for action {action_id}: {str(e)}")
        
        # Update action status to failed
        try:
            service = ActionService(db)
            from app.schemas.action import ActionUpdate
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message=str(e)
            ))
        except Exception as update_error:
            logger.error(f"Failed to update action {action_id} status: {str(update_error)}")
        
        # Don't re-raise - background tasks should handle errors gracefully