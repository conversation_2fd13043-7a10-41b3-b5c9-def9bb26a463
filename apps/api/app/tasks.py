# app/tasks.py
"""
Background tasks for the ModernAction API.

This module contains all background task functions that are executed
asynchronously to avoid blocking API responses.
"""

import logging
from uuid import UUID
from sqlalchemy.orm import Session
from app.models.bill import Bill
from app.services.ai import summarize_bill

logger = logging.getLogger(__name__)

def task_generate_summary_for_bill(bill_id: UUID, db: Session) -> None:
    """
    Background task to generate AI summary for a bill.
    
    This function runs in the background after a bill is created via the API.
    It fetches the bill, generates an AI summary, and updates the database.
    
    Args:
        bill_id: UUID of the bill to process
        db: Database session
    """
    try:
        logger.info(f"Starting background task: Generate summary for bill {bill_id}")
        
        # Fetch the bill from the database
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        
        if not bill:
            logger.error(f"Bill {bill_id} not found in database")
            return
        
        # Check if bill already has an AI summary
        if bill.ai_summary and bill.ai_summary.strip():
            logger.info(f"Bill {bill_id} already has AI summary, skipping")
            return
        
        # Check if bill has full text for summarization
        if not bill.full_text or not bill.full_text.strip():
            logger.warning(f"Bill {bill_id} has no full text, cannot generate summary")
            return
        
        # Generate AI summary
        logger.info(f"Generating AI summary for bill {bill_id}: {bill.title[:50]}...")
        
        summary = summarize_bill(
            bill_text=bill.full_text, 
            title=bill.title
        )
        
        # Update the bill with the generated summary
        bill.ai_summary = summary
        db.commit()
        
        logger.info(f"Successfully generated and saved AI summary for bill {bill_id}")
        logger.debug(f"Generated summary: {summary[:100]}...")
        
    except Exception as e:
        logger.error(f"Background task failed for bill {bill_id}: {str(e)}")
        db.rollback()
        # Don't re-raise - background tasks should handle errors gracefully

def task_regenerate_summary_for_bill(bill_id: UUID, db: Session) -> None:
    """
    Background task to regenerate AI summary for an existing bill.
    
    This function is similar to task_generate_summary_for_bill but will
    overwrite existing summaries. Useful for updating summaries when
    the AI model or bill text changes.
    
    Args:
        bill_id: UUID of the bill to process
        db: Database session
    """
    try:
        logger.info(f"Starting background task: Regenerate summary for bill {bill_id}")
        
        # Fetch the bill from the database
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        
        if not bill:
            logger.error(f"Bill {bill_id} not found in database")
            return
        
        # Check if bill has full text for summarization
        if not bill.full_text or not bill.full_text.strip():
            logger.warning(f"Bill {bill_id} has no full text, cannot regenerate summary")
            return
        
        # Generate AI summary (overwrite existing)
        logger.info(f"Regenerating AI summary for bill {bill_id}: {bill.title[:50]}...")
        
        summary = summarize_bill(
            bill_text=bill.full_text, 
            title=bill.title
        )
        
        # Update the bill with the new summary
        old_summary = bill.ai_summary
        bill.ai_summary = summary
        db.commit()
        
        logger.info(f"Successfully regenerated AI summary for bill {bill_id}")
        logger.debug(f"Old summary: {old_summary[:50] if old_summary else 'None'}...")
        logger.debug(f"New summary: {summary[:100]}...")
        
    except Exception as e:
        logger.error(f"Background task failed for bill {bill_id}: {str(e)}")
        db.rollback()
        # Don't re-raise - background tasks should handle errors gracefully

def task_bulk_generate_summaries(bill_ids: list[UUID], db: Session) -> None:
    """
    Background task to generate AI summaries for multiple bills.
    
    This function processes multiple bills in sequence. It's useful for
    bulk operations without blocking the API.
    
    Args:
        bill_ids: List of bill UUIDs to process
        db: Database session
    """
    logger.info(f"Starting bulk summary generation for {len(bill_ids)} bills")
    
    successful = 0
    failed = 0
    
    for bill_id in bill_ids:
        try:
            task_generate_summary_for_bill(bill_id, db)
            successful += 1
        except Exception as e:
            logger.error(f"Failed to process bill {bill_id} in bulk operation: {e}")
            failed += 1
    
    logger.info(f"Bulk summary generation complete: {successful} successful, {failed} failed")

def task_process_action(action_id: UUID, db: Session) -> None:
    """
    Background task to process actions (email and/or Twitter).

    This function runs in the background after an action is created via the API.
    It fetches the action details and processes all requested action types.

    Args:
        action_id: UUID of the action to process
        db: Database session
    """
    from app.models.action import Action, ActionStatus
    from app.services.action import ActionService
    
    try:
        logger.info(f"Starting background task: Send action email for action {action_id}")
        
        # Fetch the action from the database
        action = db.query(Action).filter(Action.id == action_id).first()
        
        if not action:
            logger.error(f"Action {action_id} not found in database")
            return
        
        # Check if action is in pending status
        if action.status != ActionStatus.PENDING:
            logger.info(f"Action {action_id} is not in pending status, skipping")
            return

        # Get action types from the action record
        action_types = action.action_types or ["EMAIL"]  # Default to email if not specified
        logger.info(f"Processing action {action_id} with types: {action_types}")

        # Initialize service for status updates
        service = ActionService(db)
        from app.schemas.action import ActionUpdate

        # Track results for each action type
        results = {
            "email": {"attempted": False, "success": False, "error": None},
            "twitter": {"attempted": False, "success": False, "error": None}
        }
        
        # Process EMAIL action type
        if "EMAIL" in action_types:
            results["email"]["attempted"] = True

            if not action.contact_email:
                logger.warning(f"Action {action_id} has no contact email, cannot send email")
                results["email"]["error"] = "No contact email available"
            else:
                try:
                    logger.info(f"Sending email for action {action_id} to {action.contact_email}")
                    logger.info(f"Subject: {action.subject}")
                    logger.info(f"Message preview: {action.message[:100]}...")

                    # Import email service here to avoid circular imports
                    from app.services.email import EmailService

                    email_service = EmailService()

                    # Send the action email
                    email_result = email_service.send_action_email(
                        to_address=action.contact_email,
                        user_name=action.user_name,
                        user_email=action.user_email,
                        user_address=action.user_address,
                        user_zip_code=action.user_zip_code,
                        subject=action.subject,
                        message=action.message,
                        official_name=action.official.name
                    )

                    if email_result['success']:
                        results["email"]["success"] = True
                        logger.info(f"Successfully sent email for action {action_id}. Message ID: {email_result.get('message_id')}")
                    else:
                        results["email"]["error"] = email_result.get('error_message', 'Email sending failed')
                        logger.error(f"Failed to send email for action {action_id}: {results['email']['error']}")

                except Exception as e:
                    results["email"]["error"] = str(e)
                    logger.error(f"Exception while sending email for action {action_id}: {e}")

        # Process TWITTER action type
        if "TWITTER" in action_types:
            results["twitter"]["attempted"] = True

            if not action.official.twitter_handle:
                logger.warning(f"Action {action_id} official has no Twitter handle, cannot send tweet")
                results["twitter"]["error"] = "Official has no Twitter handle"
            else:
                try:
                    logger.info(f"Posting tweet for action {action_id} to @{action.official.twitter_handle}")

                    # Import Twitter service here to avoid circular imports
                    from app.services.twitter import TwitterService

                    twitter_service = TwitterService()

                    if not twitter_service.is_available():
                        results["twitter"]["error"] = "Twitter service not configured"
                        logger.warning(f"Twitter service not available for action {action_id}")
                    else:
                        # Construct tweet message (shorter than email)
                        tweet_message = action.message[:200]  # Truncate to fit Twitter limits
                        if len(action.message) > 200:
                            tweet_message = tweet_message.rstrip() + "..."

                        # Post the tweet
                        tweet_result = twitter_service.post_action_tweet(
                            message=tweet_message,
                            official_twitter_handle=action.official.twitter_handle,
                            campaign_hashtag=getattr(action.campaign, 'hashtag', None),
                            user_name=action.user_name
                        )

                        if tweet_result.get('success'):
                            results["twitter"]["success"] = True
                            logger.info(f"Successfully posted tweet for action {action_id}. Tweet ID: {tweet_result.get('tweet_id')}")
                        else:
                            results["twitter"]["error"] = tweet_result.get('error_message', 'Tweet posting failed')
                            logger.error(f"Failed to post tweet for action {action_id}: {results['twitter']['error']}")

                except Exception as e:
                    results["twitter"]["error"] = str(e)
                    logger.error(f"Exception while posting tweet for action {action_id}: {e}")
        
        # Determine overall action status based on results
        attempted_count = sum(1 for r in results.values() if r["attempted"])
        success_count = sum(1 for r in results.values() if r["success"])

        if attempted_count == 0:
            # No action types were attempted
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message="No valid action types could be processed"
            ))
            logger.error(f"No action types could be processed for action {action_id}")
            return

        if success_count == attempted_count:
            # All attempted actions succeeded
            delivery_methods = []
            if results["email"]["success"]:
                delivery_methods.append("email")
            if results["twitter"]["success"]:
                delivery_methods.append("twitter")

            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.SENT,
                delivery_method=",".join(delivery_methods)
            ))
            logger.info(f"Successfully processed all action types for action {action_id}: {delivery_methods}")

        elif success_count > 0:
            # Some actions succeeded, some failed
            error_messages = []
            if results["email"]["attempted"] and not results["email"]["success"]:
                error_messages.append(f"Email: {results['email']['error']}")
            if results["twitter"]["attempted"] and not results["twitter"]["success"]:
                error_messages.append(f"Twitter: {results['twitter']['error']}")

            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.PARTIAL,
                error_message="; ".join(error_messages)
            ))
            logger.warning(f"Partial success for action {action_id}: {success_count}/{attempted_count} succeeded")

        else:
            # All attempted actions failed
            error_messages = []
            if results["email"]["attempted"]:
                error_messages.append(f"Email: {results['email']['error']}")
            if results["twitter"]["attempted"]:
                error_messages.append(f"Twitter: {results['twitter']['error']}")

            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message="; ".join(error_messages)
            ))
            logger.error(f"All action types failed for action {action_id}")
        
        logger.info(f"Successfully processed action {action_id}")
        
    except Exception as e:
        logger.error(f"Background task failed for action {action_id}: {str(e)}")
        
        # Update action status to failed
        try:
            service = ActionService(db)
            from app.schemas.action import ActionUpdate
            service.update_action(action_id, ActionUpdate(
                status=ActionStatus.FAILED,
                error_message=str(e)
            ))
        except Exception as update_error:
            logger.error(f"Failed to update action {action_id} status: {str(update_error)}")
        
        # Don't re-raise - background tasks should handle errors gracefully