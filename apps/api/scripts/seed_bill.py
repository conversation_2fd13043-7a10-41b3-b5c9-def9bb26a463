#!/usr/bin/env python3
"""
Manual bill seeding script with AI summarization integration.

This script fetches bills from external APIs and creates them in our database
with AI-generated summaries for citizen-friendly consumption.
"""

import sys
import os
from typing import Dict, Any, Optional
import requests
from sqlalchemy.orm import Session

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.db.database import SessionLocal
from app.models.bill import Bill, BillStatus, BillType
from app.services.ai import summarize_bill
from app.core.config import settings

def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e

def fetch_bill_from_openstates(bill_id: str) -> Optional[Dict[str, Any]]:
    """
    Fetch bill data from OpenStates API.
    
    Args:
        bill_id: OpenStates bill ID
        
    Returns:
        Dict containing bill data or None if not found
    """
    if not settings.OPEN_STATES_API_KEY:
        print("ERROR: OPEN_STATES_API_KEY not configured")
        return None
    
    api_url = f"https://v3.openstates.org/bills/{bill_id}"
    headers = {
        "X-API-KEY": settings.OPEN_STATES_API_KEY,
        "Accept": "application/json"
    }
    
    try:
        print(f"Fetching bill data from OpenStates API: {bill_id}")
        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        print(f"Successfully fetched bill: {data.get('title', 'Unknown Title')}")
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"ERROR: Failed to fetch bill from OpenStates API: {e}")
        return None
    except Exception as e:
        print(f"ERROR: Unexpected error fetching bill: {e}")
        return None

def convert_openstates_to_bill_model(bill_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert OpenStates API data to our Bill model format.
    
    Args:
        bill_data: Raw data from OpenStates API
        
    Returns:
        Dict formatted for Bill model creation
    """
    # Map OpenStates bill types to our enum
    bill_type_mapping = {
        'bill': BillType.HOUSE_BILL,
        'resolution': BillType.HOUSE_RESOLUTION,
        'joint_resolution': BillType.JOINT_RESOLUTION,
        'concurrent_resolution': BillType.CONCURRENT_RESOLUTION,
    }
    
    # Map OpenStates status to our enum
    status_mapping = {
        'introduced': BillStatus.INTRODUCED,
        'passed': BillStatus.PASSED,
        'signed': BillStatus.SIGNED,
        'vetoed': BillStatus.VETOED,
        'failed': BillStatus.FAILED,
    }
    
    # Extract basic bill information
    bill_model_data = {
        'title': bill_data.get('title', ''),
        'description': bill_data.get('abstract', ''),
        'bill_number': bill_data.get('identifier', ''),
        'bill_type': bill_type_mapping.get(
            bill_data.get('classification', ['bill'])[0], 
            BillType.HOUSE_BILL
        ),
        'status': status_mapping.get(
            bill_data.get('status', 'introduced'), 
            BillStatus.INTRODUCED
        ),
        'session_year': bill_data.get('session', {}).get('year', 2024),
        'chamber': bill_data.get('from_organization', {}).get('name', 'house').lower(),
        'state': bill_data.get('jurisdiction', {}).get('name', 'federal'),
        'openstates_id': bill_data.get('id', ''),
        'source_url': bill_data.get('sources', [{}])[0].get('url', ''),
        'full_text': bill_data.get('text', ''),
        'summary': bill_data.get('abstract', ''),
    }
    
    # Extract sponsor information
    sponsors = bill_data.get('sponsorships', [])
    if sponsors:
        primary_sponsor = sponsors[0]
        bill_model_data.update({
            'sponsor_name': primary_sponsor.get('name', ''),
            'sponsor_party': primary_sponsor.get('party', ''),
            'sponsor_state': primary_sponsor.get('jurisdiction', ''),
        })
    
    # Extract dates
    actions = bill_data.get('actions', [])
    if actions:
        bill_model_data['introduced_date'] = actions[0].get('date')
        bill_model_data['last_action_date'] = actions[-1].get('date')
    
    return bill_model_data

def seed_single_bill(bill_external_id: str, db: Session) -> bool:
    """
    Seed a single bill with AI summarization.
    
    Args:
        bill_external_id: External ID from OpenStates
        db: Database session
        
    Returns:
        True if successful, False otherwise
    """
    print(f"\n=== Seeding Bill: {bill_external_id} ===")
    
    # Check if bill already exists
    existing_bill = db.query(Bill).filter(
        Bill.openstates_id == bill_external_id
    ).first()
    
    if existing_bill:
        print(f"Bill {bill_external_id} already exists in database")
        return False
    
    # Fetch bill data from OpenStates API
    bill_data = fetch_bill_from_openstates(bill_external_id)
    if not bill_data:
        print(f"Failed to fetch bill data for {bill_external_id}")
        return False
    
    # Convert to our model format
    bill_model_data = convert_openstates_to_bill_model(bill_data)
    
    # Create Bill model instance
    new_bill = Bill(**bill_model_data)
    
    # Generate AI summary if full text is available
    full_text = bill_model_data.get('full_text', '')
    title = bill_model_data.get('title', '')
    
    if full_text and len(full_text.strip()) > 0:
        print(f"Generating AI summary for: {title}")
        try:
            # Call the AI service we built
            ai_summary = summarize_bill(bill_text=full_text, title=title)
            new_bill.ai_summary = ai_summary
            print(f"✅ AI summary generated: {ai_summary[:100]}...")
            
        except Exception as e:
            print(f"⚠️  Could not generate AI summary: {e}")
            # Continue without AI summary - not a blocking error
    else:
        print("⚠️  No full text available - skipping AI summary")
    
    # Save to database
    try:
        db.add(new_bill)
        db.commit()
        db.refresh(new_bill)
        print(f"✅ Successfully seeded bill: {new_bill.bill_number}")
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ Failed to save bill to database: {e}")
        return False

def seed_multiple_bills(bill_ids: list[str]) -> None:
    """
    Seed multiple bills with AI summarization.
    
    Args:
        bill_ids: List of OpenStates bill IDs to seed
    """
    db = get_db()
    
    try:
        successful = 0
        failed = 0
        
        for bill_id in bill_ids:
            try:
                if seed_single_bill(bill_id, db):
                    successful += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ Unexpected error seeding {bill_id}: {e}")
                failed += 1
        
        print(f"\n=== SEEDING COMPLETE ===")
        print(f"✅ Successfully seeded: {successful} bills")
        print(f"❌ Failed to seed: {failed} bills")
        
    finally:
        db.close()

def main():
    """Main function for command-line usage"""
    if len(sys.argv) < 2:
        print("Usage: python seed_bill.py <bill_id> [<bill_id2> ...]")
        print("Example: python seed_bill.py ocd-bill/12345 ocd-bill/67890")
        sys.exit(1)
    
    bill_ids = sys.argv[1:]
    
    print("🚀 Starting bill seeding with AI summarization")
    print(f"📋 Bills to seed: {len(bill_ids)}")
    
    # Check AI service health
    from app.services.ai import health_check
    health = health_check()
    if health['status'] != 'healthy':
        print(f"⚠️  AI service health check failed: {health.get('error', 'Unknown error')}")
        print("Proceeding without AI summaries...")
    else:
        print("✅ AI service is healthy")
    
    seed_multiple_bills(bill_ids)

if __name__ == "__main__":
    main()