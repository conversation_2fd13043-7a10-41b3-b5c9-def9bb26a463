# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/modernaction

# Environment
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# External API Keys
OPENSTATES_API_KEY=your-openstates-api-key
GOOGLE_CIVIC_INFO_API_KEY=your-google-civic-info-api-key
HUGGING_FACE_API_KEY=your-hugging-face-api-key

# AWS Configuration (for production)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key

# SES Configuration
SES_FROM_EMAIL=<EMAIL>
SES_REGION=us-east-1

# Redis (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO