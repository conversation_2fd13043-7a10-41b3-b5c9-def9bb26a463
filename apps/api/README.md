# ModernAction API

A FastAPI-based backend for the ModernAction civic engagement platform.

## Features

- **Action Management**: Create and track citizen actions to representatives
- **Email Integration**: AWS SES for sending emails to government officials
- **Campaign System**: Manage legislative campaigns and bill tracking
- **Representative Data**: Official information and contact details
- **AI Integration**: Bill summarization using machine learning
- **Background Tasks**: Asynchronous processing for email delivery
- **Comprehensive API**: RESTful endpoints with OpenAPI documentation

## Architecture

### Technology Stack

- **Framework**: FastAPI with async/await support
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Background Tasks**: FastAPI BackgroundTasks + Celery (future)
- **Email Service**: AWS SES integration with boto3
- **AI/ML**: Hugging Face Transformers for bill summarization
- **Authentication**: JWT tokens (future implementation)
- **Documentation**: OpenAPI/Swagger auto-generated docs

### Project Structure

```
app/
   api/                    # API route definitions
      v1/
          endpoints/     # API endpoint modules
          api.py         # Router aggregation
   core/                  # Core application configuration
      config.py         # Settings and environment
   db/                    # Database configuration
      base_class.py     # SQLAlchemy base class
      database.py       # Database session management
      types.py          # Custom database types
   models/               # SQLAlchemy models
      action.py         # Action model
      bill.py           # Bill model
      campaign.py       # Campaign model
      official.py       # Official model
      user.py           # User model
   schemas/              # Pydantic schemas
      action.py         # Action schemas
      bill.py           # Bill schemas
      campaign.py       # Campaign schemas
      official.py       # Official schemas
      user.py           # User schemas
   services/             # Business logic services
      action.py         # Action service
      ai.py             # AI/ML service
      bills.py          # Bill service
      campaigns.py      # Campaign service
      email.py          # Email service
      officials.py      # Official service
   tasks.py              # Background tasks
   main.py               # Application entry point
```

## Getting Started

### Prerequisites

- Python 3.11+
- Poetry for dependency management
- PostgreSQL database
- AWS account with SES access
- Redis (for future Celery integration)

### Installation

```bash
# Install dependencies
poetry install

# Set up environment variables
cp .env.example .env

# Run database migrations
poetry run alembic upgrade head

# Start development server
poetry run uvicorn app.main:app --reload
```

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:password@localhost/modernaction

# AWS SES Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=ModernAction

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ModernAction API
ENVIRONMENT=development
DEBUG=true

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# External APIs
OPEN_STATES_API_KEY=your_openstates_key
HUGGING_FACE_API_KEY=your_huggingface_key
```

## API Endpoints

### Actions

#### POST /api/v1/actions
Create a new action and queue for email delivery.

```bash
curl -X POST "http://localhost:8000/api/v1/actions" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Re: Climate Action Now Act (HR-1234)",
    "message": "I urge you to support this important legislation...",
    "user_name": "John Doe",
    "user_email": "<EMAIL>",
    "user_zip_code": "12345",
    "campaign_id": "campaign_uuid",
    "official_id": "official_uuid"
  }'
```

#### GET /api/v1/actions
Retrieve actions with optional filtering.

```bash
curl "http://localhost:8000/api/v1/actions?campaign_id=uuid&status=sent&limit=10"
```

#### GET /api/v1/actions/{id}
Get detailed action information.

```bash
curl "http://localhost:8000/api/v1/actions/action_uuid"
```

#### POST /api/v1/actions/{id}/retry
Retry a failed action.

```bash
curl -X POST "http://localhost:8000/api/v1/actions/action_uuid/retry"
```

### Campaigns

#### GET /api/v1/campaigns
List all campaigns with pagination.

```bash
curl "http://localhost:8000/api/v1/campaigns?skip=0&limit=20"
```

#### GET /api/v1/campaigns/{id}
Get campaign details including associated bill.

```bash
curl "http://localhost:8000/api/v1/campaigns/campaign_uuid"
```

#### GET /api/v1/campaigns/search
Search campaigns by various criteria.

```bash
curl "http://localhost:8000/api/v1/campaigns/search?query=climate&status=active"
```

### Officials

#### GET /api/v1/officials/zip/{zip_code}
Find representatives by zip code.

```bash
curl "http://localhost:8000/api/v1/officials/zip/12345"
```

#### GET /api/v1/officials/{id}
Get official details.

```bash
curl "http://localhost:8000/api/v1/officials/official_uuid"
```

### Bills

#### GET /api/v1/bills
List bills with optional filtering.

```bash
curl "http://localhost:8000/api/v1/bills?status=committee&session_year=2024"
```

#### GET /api/v1/bills/{id}
Get bill details with AI summary.

```bash
curl "http://localhost:8000/api/v1/bills/bill_uuid"
```

#### POST /api/v1/bills/{id}/regenerate-summary
Regenerate AI summary for a bill.

```bash
curl -X POST "http://localhost:8000/api/v1/bills/bill_uuid/regenerate-summary"
```

## Services

### Action Service

Handles action creation, validation, and management.

**Location**: `app/services/action.py`

**Key Methods**:
- `create_action()`: Creates action with user/official validation
- `get_actions()`: Retrieves actions with filtering
- `update_action()`: Updates action status and delivery info
- `get_action_stats()`: Provides campaign and system statistics
- `retry_failed_action()`: Handles failed action retries

**Usage**:
```python
from app.services.action import ActionService

service = ActionService(db)
action = service.create_action(action_data)
stats = service.get_action_stats(campaign_id)
```

### Email Service

Manages email sending via AWS SES.

**Location**: `app/services/email.py`

**Key Methods**:
- `send_action_email()`: Sends formatted constituent emails
- `send_email()`: Generic email sending
- `health_check()`: Monitors SES service health
- `get_send_statistics()`: Retrieves sending metrics

**Usage**:
```python
from app.services.email import EmailService

service = EmailService()
result = service.send_action_email(
    to_address="<EMAIL>",
    user_name="John Doe",
    user_email="<EMAIL>",
    subject="Re: Important Bill",
    message="I support this legislation...",
    official_name="Representative Smith"
)
```

### AI Service

Provides bill summarization using machine learning.

**Location**: `app/services/ai.py`

**Key Methods**:
- `summarize_bill()`: Generates AI summaries for bills
- `get_summarizer()`: Singleton model instance
- `health_check()`: Monitors AI service health

**Usage**:
```python
from app.services.ai import summarize_bill

summary = summarize_bill(
    bill_text="Full text of the bill...",
    title="Climate Action Now Act"
)
```

## Background Tasks

### Email Sending Task

Asynchronous email delivery to prevent API blocking.

**Location**: `app/tasks.py`

**Function**: `task_send_action_email(action_id, db)`

**Process**:
1. Fetch action details from database
2. Validate contact information
3. Send email via EmailService
4. Update action status based on result
5. Handle errors and implement retry logic

**Usage**:
```python
from app.tasks import task_send_action_email
from fastapi import BackgroundTasks

background_tasks.add_task(task_send_action_email, action_id, db)
```

### AI Summarization Task

Background bill summarization for improved performance.

**Function**: `task_generate_summary_for_bill(bill_id, db)`

**Process**:
1. Fetch bill text from database
2. Generate AI summary using ML model
3. Update bill record with summary
4. Handle errors gracefully

## Database Models

### Action Model

Stores user actions to representatives.

**Key Fields**:
- `subject`: Email subject line
- `message`: User's message content
- `user_name`, `user_email`: Constituent information
- `contact_email`: Official's email address
- `status`: Action status (pending, sent, failed)
- `delivery_id`: External email service ID
- `retry_count`: Number of retry attempts

**Relationships**:
- Belongs to User, Campaign, and Official
- Cascading deletes for data integrity

### Campaign Model

Manages legislative campaigns.

**Key Fields**:
- `title`, `description`: Campaign information
- `campaign_type`: Support, oppose, or neutral
- `status`: Draft, active, paused, completed
- `call_to_action`: Default message template
- `talking_points`: Array of key points
- `target_actions`: Goal for engagement

**Relationships**:
- Belongs to Bill
- Has many Actions

### Official Model

Stores representative information.

**Key Fields**:
- `name`, `title`: Official identification
- `level`: Federal, state, or local
- `chamber`: House, Senate, or other
- `email`, `phone`: Contact information
- `state`, `district`: Geographic representation
- `bioguide_id`: External identifier

**Relationships**:
- Has many Actions
- Used for zip code lookups

## Testing

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=app

# Run specific test file
poetry run pytest tests/test_actions.py

# Run tests with verbose output
poetry run pytest -v
```

### Test Structure

```
tests/
   conftest.py           # Test configuration and fixtures
   test_actions.py       # Action endpoint tests
   test_campaigns.py     # Campaign endpoint tests
   test_officials.py     # Official endpoint tests
   test_bills.py         # Bill endpoint tests
   test_ai_service.py    # AI service tests
   test_email_service.py # Email service tests
   test_tasks.py         # Background task tests
```

### Test Coverage

- **Unit Tests**: Service layer business logic
- **Integration Tests**: API endpoints and database
- **Background Task Tests**: Async processing validation
- **Email Tests**: SES integration with mocking
- **AI Tests**: ML model functionality

## Deployment

### Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] AWS SES production access enabled
- [ ] SSL certificates installed
- [ ] Background workers running
- [ ] Monitoring and logging configured
- [ ] Health checks enabled
- [ ] Error tracking active

### Docker Deployment

```bash
# Build image
docker build -t modernaction-api .

# Run container
docker run -p 8000:8000 --env-file .env modernaction-api
```

### Database Migrations

```bash
# Generate new migration
poetry run alembic revision --autogenerate -m "Description"

# Apply migrations
poetry run alembic upgrade head

# Rollback migration
poetry run alembic downgrade -1
```

## Monitoring

### Health Checks

#### API Health
```bash
curl "http://localhost:8000/api/v1/health"
```

#### Email Service Health
```bash
curl "http://localhost:8000/api/v1/health/email"
```

#### AI Service Health
```bash
curl "http://localhost:8000/api/v1/health/ai"
```

### Metrics

- **Action Success Rate**: Percentage of successfully sent emails
- **Email Delivery Time**: Average time from creation to delivery
- **Error Rates**: Breakdown of failure reasons
- **SES Quotas**: Sending limits and usage
- **AI Performance**: Summarization speed and accuracy

### Logging

```python
import logging

logger = logging.getLogger(__name__)
logger.info("Action created", extra={"action_id": action.id})
logger.error("Email failed", extra={"error": str(e)})
```

## Security

### Authentication

Future implementation will include:
- JWT token-based authentication
- Role-based access control
- API key management
- OAuth integration

### Data Protection

- **Encryption**: All sensitive data encrypted at rest
- **Validation**: Input validation and sanitization
- **Rate Limiting**: Prevent abuse and DoS attacks
- **Audit Logging**: Track all data access and changes

### AWS Security

- **IAM Policies**: Least privilege access
- **SES Reputation**: Monitor bounce and complaint rates
- **Credentials**: Secure key management
- **VPC**: Network isolation for production

## Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run test suite
5. Submit pull request

### Code Standards

- **Python**: PEP 8 style guide
- **Type Hints**: Full type annotation
- **Documentation**: Docstrings for all functions
- **Testing**: Unit tests for all features

### Pull Request Process

1. Ensure all tests pass
2. Add/update documentation
3. Include migration files if needed
4. Update changelog
5. Request review from team

## Troubleshooting

### Common Issues

1. **Database Connection**: Check DATABASE_URL
2. **SES Errors**: Verify AWS credentials
3. **AI Model Loading**: Check available memory
4. **Migration Errors**: Check database schema

### Debug Mode

Enable detailed logging:

```env
DEBUG=true
LOG_LEVEL=DEBUG
```

### Support

- **Documentation**: `/docs` directory
- **API Docs**: http://localhost:8000/docs
- **Issues**: GitHub issue tracker
- **Discussions**: Team Slack channel