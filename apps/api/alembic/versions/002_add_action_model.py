"""Add action model and relationships

Revision ID: 002
Revises: 001
Create Date: 2024-07-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # Create action status enum
    action_status = postgresql.ENUM('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', name='actionstatus')
    action_status.create(op.get_bind())
    
    # Create action type enum  
    action_type = postgresql.ENUM('EMAIL', 'PHONE', 'LETTER', 'SOCIAL_MEDIA', 'PETITION', name='actiontype')
    action_type.create(op.get_bind())
    
    # Create actions table
    op.create_table('actions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('subject', sa.String(), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('action_type', action_type, nullable=False),
        sa.Column('status', action_status, nullable=False),
        sa.Column('sent_at', sa.DateTime(), nullable=True),
        sa.Column('delivered_at', sa.DateTime(), nullable=True),
        sa.Column('response_received', sa.Boolean(), nullable=False),
        sa.Column('response_content', sa.Text(), nullable=True),
        sa.Column('response_received_at', sa.DateTime(), nullable=True),
        sa.Column('contact_email', sa.String(), nullable=True),
        sa.Column('contact_phone', sa.String(), nullable=True),
        sa.Column('contact_address', sa.Text(), nullable=True),
        sa.Column('user_name', sa.String(), nullable=False),
        sa.Column('user_email', sa.String(), nullable=False),
        sa.Column('user_address', sa.Text(), nullable=True),
        sa.Column('user_zip_code', sa.String(), nullable=True),
        sa.Column('delivery_method', sa.String(), nullable=True),
        sa.Column('delivery_id', sa.String(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False),
        sa.Column('action_metadata', sa.JSON(), nullable=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('campaign_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('official_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
        sa.ForeignKeyConstraint(['official_id'], ['officials.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_actions_created_at'), 'actions', ['created_at'], unique=False)
    op.create_index(op.f('ix_actions_status'), 'actions', ['status'], unique=False)
    op.create_index(op.f('ix_actions_campaign_id'), 'actions', ['campaign_id'], unique=False)
    op.create_index(op.f('ix_actions_user_id'), 'actions', ['user_id'], unique=False)
    op.create_index(op.f('ix_actions_official_id'), 'actions', ['official_id'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_actions_official_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_user_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_campaign_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_status'), table_name='actions')
    op.drop_index(op.f('ix_actions_created_at'), table_name='actions')
    
    # Drop actions table
    op.drop_table('actions')
    
    # Drop enums
    op.execute('DROP TYPE actiontype')
    op.execute('DROP TYPE actionstatus')