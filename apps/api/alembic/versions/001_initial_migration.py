"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Users table
    op.create_table('users',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('first_name', sa.String(), nullable=False),
        sa.Column('last_name', sa.String(), nullable=False),
        sa.Column('zip_code', sa.String(), nullable=True),
        sa.Column('phone_number', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_verified', sa.Boolean(), nullable=False),
        sa.Column('is_superuser', sa.Boolean(), nullable=False),
        sa.Column('email_notifications', sa.Boolean(), nullable=False),
        sa.Column('sms_notifications', sa.Boolean(), nullable=False),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('profile_picture_url', sa.String(), nullable=True),
        sa.Column('email_verification_token', sa.String(), nullable=True),
        sa.Column('email_verified_at', sa.DateTime(), nullable=True),
        sa.Column('password_reset_token', sa.String(), nullable=True),
        sa.Column('password_reset_expires_at', sa.DateTime(), nullable=True),
        sa.Column('last_login_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)

    # Bills table
    op.create_table('bills',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('bill_number', sa.String(), nullable=False),
        sa.Column('bill_type', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('session_year', sa.Integer(), nullable=False),
        sa.Column('chamber', sa.String(), nullable=False),
        sa.Column('state', sa.String(), nullable=False),
        sa.Column('full_text', sa.Text(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('ai_summary', sa.Text(), nullable=True),
        sa.Column('openstates_id', sa.String(), nullable=True),
        sa.Column('congress_gov_id', sa.String(), nullable=True),
        sa.Column('source_url', sa.String(), nullable=True),
        sa.Column('text_url', sa.String(), nullable=True),
        sa.Column('introduced_date', sa.DateTime(), nullable=True),
        sa.Column('last_action_date', sa.DateTime(), nullable=True),
        sa.Column('sponsor_name', sa.String(), nullable=True),
        sa.Column('sponsor_party', sa.String(), nullable=True),
        sa.Column('sponsor_state', sa.String(), nullable=True),
        sa.Column('cosponsors', sa.Text(), nullable=True),
        sa.Column('vote_history', sa.Text(), nullable=True),
        sa.Column('is_featured', sa.Boolean(), nullable=False),
        sa.Column('priority_score', sa.Integer(), nullable=False),
        sa.Column('tags', sa.Text(), nullable=True),
        sa.Column('categories', sa.Text(), nullable=True),
        sa.Column('bill_metadata', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bills_bill_number'), 'bills', ['bill_number'], unique=False)
    op.create_index(op.f('ix_bills_id'), 'bills', ['id'], unique=False)
    op.create_index(op.f('ix_bills_title'), 'bills', ['title'], unique=False)

    # Officials table
    op.create_table('officials',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('party', sa.String(), nullable=True),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('phone', sa.String(), nullable=True),
        sa.Column('website', sa.String(), nullable=True),
        sa.Column('office_address', sa.Text(), nullable=True),
        sa.Column('office_city', sa.String(), nullable=True),
        sa.Column('office_state', sa.String(), nullable=True),
        sa.Column('office_zip', sa.String(), nullable=True),
        sa.Column('level', sa.String(), nullable=False),
        sa.Column('chamber', sa.String(), nullable=True),
        sa.Column('state', sa.String(), nullable=True),
        sa.Column('district', sa.String(), nullable=True),
        sa.Column('bioguide_id', sa.String(), nullable=True),
        sa.Column('openstates_id', sa.String(), nullable=True),
        sa.Column('google_civic_id', sa.String(), nullable=True),
        sa.Column('twitter_handle', sa.String(), nullable=True),
        sa.Column('facebook_url', sa.String(), nullable=True),
        sa.Column('instagram_handle', sa.String(), nullable=True),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('profile_picture_url', sa.String(), nullable=True),
        sa.Column('term_start', sa.String(), nullable=True),
        sa.Column('term_end', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('voting_record', sa.Text(), nullable=True),
        sa.Column('positions', sa.Text(), nullable=True),
        sa.Column('response_rate', sa.Integer(), nullable=True),
        sa.Column('avg_response_time', sa.Integer(), nullable=True),
        sa.Column('official_metadata', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_officials_id'), 'officials', ['id'], unique=False)
    op.create_index(op.f('ix_officials_name'), 'officials', ['name'], unique=False)

    # Campaigns table
    op.create_table('campaigns',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('short_description', sa.String(), nullable=True),
        sa.Column('campaign_type', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('call_to_action', sa.Text(), nullable=False),
        sa.Column('email_template', sa.Text(), nullable=True),
        sa.Column('talking_points', sa.Text(), nullable=True),
        sa.Column('target_audience', sa.String(), nullable=True),
        sa.Column('geographic_scope', sa.Text(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('is_featured', sa.Boolean(), nullable=False),
        sa.Column('is_public', sa.Boolean(), nullable=False),
        sa.Column('requires_verification', sa.Boolean(), nullable=False),
        sa.Column('goal_actions', sa.Integer(), nullable=True),
        sa.Column('actual_actions', sa.Integer(), nullable=False),
        sa.Column('banner_image_url', sa.String(), nullable=True),
        sa.Column('thumbnail_image_url', sa.String(), nullable=True),
        sa.Column('social_media_message', sa.Text(), nullable=True),
        sa.Column('hashtags', sa.Text(), nullable=True),
        sa.Column('campaign_metadata', sa.Text(), nullable=True),
        sa.Column('bill_id', sa.String(36), nullable=False),
        sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_campaigns_id'), 'campaigns', ['id'], unique=False)
    op.create_index(op.f('ix_campaigns_title'), 'campaigns', ['title'], unique=False)

    # Actions table
    op.create_table('actions',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('subject', sa.String(), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('action_type', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('sent_at', sa.DateTime(), nullable=True),
        sa.Column('delivered_at', sa.DateTime(), nullable=True),
        sa.Column('response_received', sa.Boolean(), nullable=False),
        sa.Column('response_content', sa.Text(), nullable=True),
        sa.Column('response_received_at', sa.DateTime(), nullable=True),
        sa.Column('contact_email', sa.String(), nullable=True),
        sa.Column('contact_phone', sa.String(), nullable=True),
        sa.Column('contact_address', sa.Text(), nullable=True),
        sa.Column('user_name', sa.String(), nullable=False),
        sa.Column('user_email', sa.String(), nullable=False),
        sa.Column('user_address', sa.Text(), nullable=True),
        sa.Column('user_zip_code', sa.String(), nullable=True),
        sa.Column('delivery_method', sa.String(), nullable=True),
        sa.Column('delivery_id', sa.String(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False),
        sa.Column('action_metadata', sa.Text(), nullable=True),
        sa.Column('user_id', sa.String(36), nullable=False),
        sa.Column('campaign_id', sa.String(36), nullable=False),
        sa.Column('official_id', sa.String(36), nullable=False),
        sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
        sa.ForeignKeyConstraint(['official_id'], ['officials.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_actions_id'), 'actions', ['id'], unique=False)


def downgrade() -> None:
    op.drop_index(op.f('ix_actions_id'), table_name='actions')
    op.drop_table('actions')
    op.drop_index(op.f('ix_campaigns_title'), table_name='campaigns')
    op.drop_index(op.f('ix_campaigns_id'), table_name='campaigns')
    op.drop_table('campaigns')
    op.drop_index(op.f('ix_officials_name'), table_name='officials')
    op.drop_index(op.f('ix_officials_id'), table_name='officials')
    op.drop_table('officials')
    op.drop_index(op.f('ix_bills_title'), table_name='bills')
    op.drop_index(op.f('ix_bills_id'), table_name='bills')
    op.drop_index(op.f('ix_bills_bill_number'), table_name='bills')
    op.drop_table('bills')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')